{"auth.signIn": "Sign in", "auth.signUp": "Create account", "auth.email": "Email", "auth.password": "Password", "auth.confirmPassword": "Confirm password", "auth.forgotPassword": "Forgot your password?", "auth.resetPassword": "Reset your password", "auth.enterCredentials": "Enter your credentials to sign in", "auth.createAccount": "Create account", "auth.alreadyHaveAccount": "Already have an account?", "auth.noAccount": "Don't have an account?", "auth.passwordReset": "Password reset", "auth.enterEmailForReset": "Enter the email address associated with your account, and we'll send you a link to reset your password.", "auth.passwordResetSent": "A reset email has been sent", "auth.sendResetEmail": "Continue", "auth.backToSignIn": "Return to sign in", "auth.successfulReset": "Your password has been successfully reset", "auth.firstName": "First name", "auth.lastName": "Last name", "auth.createPassword": "Password", "auth.passwordHint": "Password must be at least 8 characters with letters and numbers", "auth.agreeTerms": "I agree to the", "auth.termsAndConditions": "terms and conditions", "auth.enterDetails": "Enter your information to create an account", "auth.confirmEmail": "Please check your email to activate your account", "auth.emailInUse": "This email is already associated with an account. Please sign in or reset your password", "auth.sidebarTitle": "Find medical appointments quickly", "auth.sidebarDescription": "Sans rendez-vous express helps you find medical appointments in walk-in clinics near you within 24 to 48 hours.", "auth.benefit1": "Access to same-day emergency appointments", "auth.benefit2": "Clinic search within a 50 km radius", "auth.benefit3": "Service available throughout Quebec", "auth.errors.invalidCredentials": "Invalid email or password", "auth.errors.passwordsDontMatch": "Passwords don't match", "auth.errors.emailInUse": "This email is already in use", "auth.errors.invalidEmail": "Invalid email format", "auth.errors.passwordTooShort": "Password must be at least 8 characters", "auth.errors.generalError": "An error occurred. Please try again.", "auth.errors.agreeTerms": "You must agree to the terms and conditions", "auth.errors.generic": "An error occurred during registration", "auth.continueWithGoogle": "Sign in with Google", "auth.orContinueWith": "or", "auth.errors.socialAuthFailed": "Authentication with social provider failed. Please try again.", "common.save": "Save", "common.cancel": "Cancel", "common.confirm": "Confirm", "common.delete": "Delete", "common.loading": "Loading", "common.search": "Search", "common.logout": "Log out", "common.loggingOut": "Logging out...", "common.close": "Close menu", "common.active": "Active", "common.inactive": "Inactive", "common.submit": "Submit", "common.submitting": "Submitting...", "common.processing": "Processing...", "common.newRequest": "New request", "common.required": "required", "common.yes": "Yes", "common.no": "No", "common.continue": "continue", "common.manage": "Manage", "common.modify": "Modify", "common.back": "Back", "common.saved": "Saved", "common.saveChanges": "Save changes", "nav.home": "Home", "nav.dashboard": "Dashboard", "nav.appointments": "My appointments", "nav.findAppointment": "Find an appointment", "nav.calendar": "Calendar", "nav.help": "Help", "nav.account": "Account", "nav.mobileNavigation": "Mobile navigation", "nav.needHelp": "Need help?", "account.profile": "Profile", "account.preferences": "Preferences", "account.subscription": "Subscription", "account.users": "Users", "account.manageUsers": "Manage users", "account.yourAccount": "Your account", "account.individualPlan": "Individual plan", "account.individualPlanMonthly": "Individual plan - $7.95 / month", "account.individualPlanAnnual": "Annual individual plan", "account.familyPlan": "Family plan", "account.familyPlanMonthly": "Family plan - $14.95 / month", "account.familyPlanAnnual": "Annual family plan", "account.manageInformation": "Manage your contact information", "account.personalInfoDescription": "Modify your personal information and contact details", "account.modifyProfile": "Edit contact information", "account.modifySubscription": "Modify your subscription", "account.subscribePlan": "Subscribe to a plan", "account.choosePlan": "Choose your plan", "account.subscriptionDescription": "Change your plan or modify your payment information", "account.manageSubscription": "Manage subscription", "account.appearanceLanguage": "Communication language", "account.appearanceDescription": "Customize the language of your client portal", "account.modifyPreferences": "Edit preferences", "account.manageAccountUsers": "Manage account users", "account.manageUsersDescription": "Add or modify family members on your account", "account.manageProfile": "Manage your profile", "account.manageProfileDescription": "View or update your health card information", "account.manageProfileButton": "Manage profile", "account.editPersonalInfo": "Edit your personal information", "account.firstName": "First name", "account.lastName": "Last name", "account.email": "Email address", "account.phone": "Phone number", "account.emailCannotBeEmpty": "Email cannot be empty", "account.invalidEmail": "The email you have entered is invalid", "account.firstNameRequired": "Please enter your first name", "account.lastNameRequired": "Please enter your last name", "home.greeting": "Hello, {{firstName}}!", "home.welcome": "Welcome to your dashboard", "home.findAppointmentTitle": "Find an appointment", "home.findAppointmentDesc": "Find a medical appointment near you", "home.viewRequests": "View requests", "home.manageAppointmentsDesc": "View and manage your medical appointment requests", "home.manageUsersDesc": "Add or modify family members on your account", "home.manageProfileTitle": "Manage your profile", "home.manageProfileDesc": "Add your health card information", "home.manageProfileButton": "Manage profile", "help.needHelp": "Need help?", "help.helpDescription": "Get help using our platform", "help.faq": "Frequently asked questions", "help.faqDescription": "Check our answers to frequently asked questions", "help.howToBookAppointment": "How to make an appointment?", "help.howToBookDescription": "Click on \"Find an appointment\" in the menu, then enter your postal code and availability to see options near you.", "help.howToCancelAppointment": "How to cancel an appointment?", "help.howToCancelDescription": "On the \"My appointments\" page, find your appointment and click the \"Cancel\" button.", "help.howToChangePlan": "How to change my plan?", "help.howToChangePlanDescription": "Go to the \"Your account\" page and click on \"Manage subscription\" to see available plan options.", "help.customerSupport": "Customer support", "help.supportDescription": "Our team is available to help you", "help.email": "Email", "help.supportEmail": "<EMAIL>", "help.responseTime": "We respond within 24 hours", "help.contactSupport": "Contact support", "preferences.managePreferences": "Manage your preferences", "preferences.languageAppearance": "Communication language", "preferences.customizeInterface": "Customize the language of your client portal", "preferences.preferredLanguage": "Preferred language", "preferences.french": "French", "preferences.english": "English", "preferences.languageDescription": "This preference applies to the portal interface and emails you receive from us.", "preferences.appTheme": "Application theme", "preferences.light": "Light", "preferences.dark": "Dark", "preferences.themeDescription": "Choose the visual theme that suits you best.", "preferences.saveChanges": "Save changes", "preferences.saving": "Saving...", "preferences.changesSaved": "Your preferences have been saved successfully.", "preferences.errorSaving": "An error occurred while saving your preferences.", "users.manageAccountUsers": "Manage account users", "users.manageProfile": "Manage your profile", "users.familyMembers": "Family members", "users.userProfile": "Your user information", "users.familyMembersDescription": "Manage up to 5 profiles for your family. Add their health card information to facilitate appointment booking.", "users.userProfileDescription": "Add your health card information to facilitate appointment booking.", "users.addYourInfo": "Add your information", "users.addYourInfoPrompt": "Click on the edit icon to add your user information.", "users.cancel": "Cancel", "users.firstName": "First name", "users.lastName": "Last name", "users.healthCardPrefix": "First 4 characters of the health insurance card", "users.healthCardDescription": "These 4 characters will be used for appointment booking", "users.birthDate": "Date of birth", "users.save": "Save", "users.edit": "Edit", "users.healthCard": "Health insurance card:", "users.addMember": "Add a member", "users.editMemberPrompt": "Click on the edit icon to add this member's information.", "users.selectDate": "Select a date", "users.validationError": "Please enter first name, last name, health card (4 characters), and birth date to save.", "subscription.modifySubscription": "Modify your subscription", "subscription.individualPlan": "Individual plan", "subscription.monthlyCost": "$7.95 / month", "subscription.benefits": "What you get:", "subscription.unlimitedAccess": "Unlimited access to appointment search", "subscription.emailNotifications": "Email notifications for your appointments", "subscription.familyProfiles": "Management of up to 5 profiles for your family", "subscription.modifyPlan": "Modify subscription", "subscription.cancelPlan": "Cancel subscription", "subscription.paymentHistory": "Payment history", "subscription.monthlySubscription": "Monthly subscription", "subscription.march": "March 14, 2025", "subscription.february": "February 14, 2025", "subscription.january": "January 14, 2025", "subscription.cost": "$7.95", "subscription.changePlan": "Change subscription plan", "subscription.changePlanDescription": "You are currently on the individual plan at $7.95 per month. By confirming, you will switch to the family plan at $14.95 per month starting from the next billing cycle.", "subscription.confirmChange": "Confirm change", "subscription.cancelConfirmation": "Are you sure you want to cancel your subscription?", "subscription.cancelWarning": "By canceling your subscription, you will lose access to all premium services at the end of your billing period.", "subscription.yesCancel": "Yes, I want to cancel", "subscription.noCancel": "No, not now", "subscription.status": "Subscription Status", "subscription.verifyingPayment": "Verification of your subscription payment", "subscription.success": "Subscription Successful!", "subscription.successMessage": "Your subscription has been activated successfully.", "subscription.details": "Subscription Details", "subscription.plan": "Plan", "subscription.billing": "Billing", "subscription.amount": "Amount", "subscription.currentPeriod": "Current period", "subscription.nextSteps": "What would you like to do next?", "subscription.goToDashboard": "Go to Dashboard", "subscription.manageAccount": "Manage Account", "subscription.error": "Something went wrong", "subscription.errorMessage": "We couldn't verify your subscription. Please try again.", "subscription.needHelp": "Need help?", "subscription.returnToPlans": "Return to Plans", "subscription.contactSupport": "Contact Support", "subscription.canceledCheckout": "You canceled the checkout process. Your subscription was not activated.", "subscription.processingSubscription": "Your subscription is being processed. Please check back in a few minutes.", "subscription.noSessionId": "No session ID found. Please try again.", "subscription.notLoggedIn": "You must be logged in to verify your subscription.", "appointments.title": "My appointments", "appointments.description": "Manage your medical appointment requests", "appointments.requestsTitle": "Appointment requests", "appointments.all": "All", "appointments.inProgress": "In progress", "appointments.completed": "Completed", "appointments.cancelled": "Cancelled", "appointments.noRequests": "No appointment requests", "appointments.noRequestsInProgress": "You have no appointment requests in progress", "appointments.noRequestsCompleted": "You have no completed appointment requests", "appointments.noRequestsCancelled": "You have no cancelled appointment requests", "appointments.postalCode": "Postal code", "appointments.sentOn": "<PERSON>t on", "appointments.pending": "Pending", "appointments.done": "Completed", "appointments.cancelAppointment": "Cancel", "appointments.cancelConfirmation": "Cancellation confirmation", "appointments.cancelConfirmationText": "Are you sure you want to cancel this appointment request?", "appointments.noContinue": "No, continue", "appointments.yesCancel": "Yes, cancel", "appointments.viewAll": "View all appointments", "findAppointment.title": "Find an appointment", "findAppointment.description": "Search for a medical appointment near you", "findAppointment.searchCriteria": "Enter your search criteria", "findAppointment.requiredFields": "All fields marked with an asterisk (*) are required", "findAppointment.appointmentFor": "The appointment is for *", "findAppointment.selectPerson": "Select a person", "findAppointment.managedInUsersSection": "The people listed here are managed in the \"Manage account users\" section", "findAppointment.healthCard": "Health insurance card", "findAppointment.healthCardOf": "Health insurance card of", "findAppointment.lastDigits": "Last 8 characters of the health insurance number *", "findAppointment.enterEightDigits": "Please enter all 8 digits", "findAppointment.format": "Format: xxxx-xxxx (8 digits in total)", "findAppointment.sequenceNumber": "Sequence number of the health insurance card *", "findAppointment.sequenceInfo": "Show information about the sequence number", "findAppointment.enterTwoDigits": "Please enter two digits", "findAppointment.postalCode": "Postal code *", "findAppointment.postalExample": "e.g.: K1A 0L8", "findAppointment.invalidPostalFormat": "Invalid postal code format. Use the format 'A1A 1A1'", "findAppointment.postalFormatWarning": "Please enter a valid postal code (format: A1A 1A1)", "findAppointment.postalCodeDescription": "Enter your postal code or one near where you would like to find an appointment", "findAppointment.fromDate": "From this date *", "findAppointment.selectDate": "Select a date", "findAppointment.appointmentTime": "Appointment time *", "findAppointment.chooseTime": "Choose a time", "findAppointment.morning": "Morning", "findAppointment.afternoon": "Afternoon", "findAppointment.evening": "Evening", "findAppointment.asap": "As soon as possible", "findAppointment.submitRequest": "Submit request", "findAppointment.thankYou": "Thank you! We have forwarded your appointment request.", "findAppointment.confirmationMessage": "You will receive information about your appointment as soon as it is confirmed.", "findAppointment.viewRequests": "View my requests", "findAppointment.selectDateError": "Please select a date", "findAppointment.selectTimeError": "Please select a time", "findAppointment.enterPostalError": "Please enter a postal code", "findAppointment.invalidPostalError": "The postal code must be in A1A 1A1 format", "findAppointment.selectPersonError": "Please select a person", "findAppointment.healthCardDigitsError": "Please enter all 8 digits", "findAppointment.sequenceNumberError": "Please enter two digits", "findAppointment.noSubscription": "No membership. <a href='/pricing' class='text-blue-600 hover:underline'>Click here to purchase a plan.</a>", "landing.hero.title": "Find an appointment with a doctor today.", "landing.hero.subtitle": "Simplify your access to healthcare. Sans rendez-vous express helps you find medical appointments in clinics across Quebec.", "landing.hero.findAppointment": "Find an appointment", "landing.hero.learnMore": "Learn more", "landing.hero.imageAlt": "Interface de rendez-vous médicaux", "landing.features.sameDay.title": "Find an appointment as soon as today", "landing.features.sameDay.description": "Our team helps you find an appointment at a clinic for the same day or within 24-48 hours on average.", "landing.features.nearbyClinic.title": "Medical clinics near you", "landing.features.nearbyClinic.description": "Our booking system allows you to find appointments near your home, whether you live in the city or in a rural area.", "landing.features.anywhereInQuebec.title": "Available everywhere in Quebec", "landing.features.anywhereInQuebec.description": "Wherever you are in Quebec, our service is accessible everywhere to meet your needs.", "landing.howItWorks.title": "Medical care without the wait", "landing.howItWorks.customAppointments.title": "Appointments according to your availability", "landing.howItWorks.customAppointments.description": "Schedule your doctor's appointment at a time and location that works for you.", "landing.howItWorks.easyManagement.title": "Easily manage your appointments", "landing.howItWorks.easyManagement.description": "Schedule, modify or cancel your medical appointments in just a few clicks, without phone calls or waiting.", "landing.howItWorks.imageAlt": "Comment ça fonctionne", "landing.pricing.title": "Affordable plans", "landing.pricing.description": "Our medical appointment booking service is designed for all needs. Pick the plan that works best for your situation.", "landing.pricing.period.monthly": "Monthly", "landing.pricing.period.annually": "Yearly", "landing.pricing.individual.title": "Individual Plan", "landing.pricing.individual.description": "Ideal for people who need occasional medical consultations.", "landing.pricing.individual.features": "Unlimited bookings for one person", "landing.pricing.individual.annualSavings": "Save $23 per year", "landing.pricing.family.title": "Family Plan", "landing.pricing.family.description": "For families who need to schedule many medical appointments.", "landing.pricing.family.features": "Unlimited bookings for five people", "landing.pricing.family.annualSavings": "Save $45 per year", "landing.pricing.choosePlan": "Choose this plan", "landing.pricing.manageSubscription": "Manage Subscription", "landing.pricing.included": "What's included:", "landing.pricing.feature1": "Unlimited bookings for one person", "landing.pricing.feature2": "Quick appointment search", "landing.pricing.feature3": "Email notifications", "landing.pricing.feature4": "Priority support", "landing.faq.title": "Common Questions", "landing.faq.viewFullFaq": "View full FAQ", "landing.faq.questions.0.question": "What exactly does Sans rendez-vous express offer?", "landing.faq.questions.0.answer": "Sans rendez-vous express is an online platform that facilitates the search and booking of appointments with doctors in Quebec. Our goal is to simplify the process for patients by allowing them to quickly find available appointments and providing them with easy access to a variety of healthcare professionals in their area.", "landing.faq.questions.1.question": "What is the waiting time to get an appointment?", "landing.faq.questions.1.answer": "At Sans rendez-vous express, we strive to find an appointment with a doctor as quickly as possible for all our users. In general, most appointments are found within 48 hours. For larger municipalities, the majority of appointments are found within 24 hours. Our team works diligently to meet the needs of patients and to ensure quick access to necessary healthcare services.", "landing.faq.questions.2.question": "What information is needed to schedule an appointment with a doctor via Sans rendez-vous express?", "landing.faq.questions.2.answer": "To schedule an appointment with a doctor through our platform, users will need to provide basic information such as their name, phone number, email address, and health insurance number. Once this information is provided, users can then specify the time, date, and location of the appointment according to their preferences. These details help us find an appointment that best matches the needs and availability of each user.", "landing.faq.questions.3.question": "How are appointments booked?", "landing.faq.questions.3.answer": "After submitting your request, our system automatically searches for available appointments in participating clinics according to your preferences. Once an appropriate appointment is found, you will receive a confirmation by email and/or SMS with all the appointment details.", "landing.faq.questions.4.question": "Who can use this service?", "landing.faq.questions.4.answer": "Our service is accessible to anyone holding a Quebec Health Insurance Card.", "landing.cta.title": "Don't wait to see a doctor.", "landing.cta.subtitle": "Let us find you a medical appointment today.", "landing.cta.buttonText": "Find an appointment", "landing.cta.imageAlt": "Médecin et patient", "landing.navbar.title": "Sans rendez-vous express", "landing.navbar.signIn": "Log In", "landing.navbar.signUp": "Sign up", "landing.navbar.service": "Le service", "landing.navbar.pricing": "<PERSON><PERSON><PERSON>", "landing.navbar.faq": "FAQ", "landing.footer.description": "Sans rendez-vous express is an independent service that helps people find medical appointments quickly in Quebec.", "landing.footer.contactUs": "Contact us", "landing.footer.privacyPolicy": "Privacy Policy", "landing.footer.termsOfUse": "Terms of Use", "landing.footer.termsOfSale": "General Terms and Conditions of Sale", "landing.footer.copyright": "© 2025 Sans rendez-vous express. All rights reserved."}