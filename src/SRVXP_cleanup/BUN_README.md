# SRVXP Project - Bun Setup

This project has been configured to use [Bun](https://bun.sh/) instead of npm for improved performance and developer experience.

## Prerequisites

- Install Bun by following the instructions at [https://bun.sh/](https://bun.sh/)

## Getting Started

1. Install dependencies:
   ```bash
   bun install
   ```

2. Run the development server:
   ```bash
   bun dev
   ```

3. Build for production:
   ```bash
   bun run build
   ```

4. Start the production server:
   ```bash
   bun start
   ```

## Bun vs npm Commands

| npm Command | Bun Equivalent |
|------------|----------------|
| `npm install` | `bun install` |
| `npm ci` | `bun install --frozen-lockfile` |
| `npm run dev` | `bun dev` |
| `npm run build` | `bun run build` |
| `npm start` | `bun start` |
| `npm run lint` | `bun run lint` |
| `npx <command>` | `bunx <command>` |

## Benefits of Using Bun

- Faster installation times
- Improved development server startup speed
- Better TypeScript performance
- Integrated runtime and bundler
- Compatible with existing Node.js and npm packages
- Built-in .env file support
- Smaller disk space usage

## Configuration

The project includes a `bunfig.toml` file that configures Bun's behavior. You can modify this file to adjust settings.

## Troubleshooting

If you encounter any issues:

1. Make sure you have the latest version of Bun installed:
   ```bash
   bun upgrade
   ```

2. Try clearing Bun's cache:
   ```bash
   bun pm cache rm
   ```

3. If a package isn't working correctly with Bun, you can temporarily fall back to Node.js:
   ```bash
   NODE_NO_BUN=1 node your-script.js
   ```
