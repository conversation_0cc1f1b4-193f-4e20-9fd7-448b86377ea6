-- <PERSON><PERSON> Sc<PERSON>t to fix user ID references in SRVXP Supabase database
-- This script will ensure all tables use consistent user ID references

-- Step 1: Check for data consistency issues
DO $$
DECLARE
    inconsistent_count INT;
BEGIN
    -- Check if there are any appointment_requests with user_id not in auth.users
    SELECT COUNT(*) INTO inconsistent_count
    FROM public.appointment_requests ar
    LEFT JOIN auth.users au ON ar.user_id = au.id
    WHERE au.id IS NULL AND ar.user_id IS NOT NULL;

    IF inconsistent_count > 0 THEN
        RAISE NOTICE 'Found % appointment_requests with invalid user_id references', inconsistent_count;
    ELSE
        RAISE NOTICE 'All appointment_requests have valid user_id references';
    END IF;

    -- Check if there are any family_members with user_id not in auth.users
    SELECT COUNT(*) INTO inconsistent_count
    FROM public.family_members fm
    LEFT JOIN auth.users au ON fm.user_id = au.id
    WHERE au.id IS NULL AND fm.user_id IS NOT NULL;

    IF inconsistent_count > 0 THEN
        RAISE NOTICE 'Found % family_members with invalid user_id references', inconsistent_count;
    ELSE
        RAISE NOTICE 'All family_members have valid user_id references';
    END IF;

    -- Check if public.users.id matches auth.users.id
    SELECT COUNT(*) INTO inconsistent_count
    FROM public.users pu
    LEFT JOIN auth.users au ON pu.id = au.id
    WHERE au.id IS NULL;

    IF inconsistent_count > 0 THEN
        RAISE NOTICE 'Found % public.users with id not matching auth.users.id', inconsistent_count;
    ELSE
        RAISE NOTICE 'All public.users have id matching auth.users.id';
    END IF;

    -- Check if public.users.user_id matches public.users.id
    SELECT COUNT(*) INTO inconsistent_count
    FROM public.users pu
    WHERE pu.id::text != pu.user_id;

    IF inconsistent_count > 0 THEN
        RAISE NOTICE 'Found % public.users where id does not match user_id', inconsistent_count;
    ELSE
        RAISE NOTICE 'All public.users have id matching user_id';
    END IF;

    -- Check if subscriptions.user_id references valid public.users.user_id
    SELECT COUNT(*) INTO inconsistent_count
    FROM public.subscriptions s
    LEFT JOIN public.users pu ON s.user_id = pu.user_id
    WHERE pu.user_id IS NULL AND s.user_id IS NOT NULL;

    IF inconsistent_count > 0 THEN
        RAISE NOTICE 'Found % subscriptions with invalid user_id references', inconsistent_count;
    ELSE
        RAISE NOTICE 'All subscriptions have valid user_id references';
    END IF;
END $$;

-- Step 2: Fix data type inconsistency in subscriptions.user_id
-- First, create a backup of the subscriptions table
CREATE TABLE IF NOT EXISTS public.subscriptions_backup AS
SELECT * FROM public.subscriptions;

-- Alter subscriptions.user_id to be UUID type to match auth.users.id
DO $$
BEGIN
    -- Only proceed if we have a backup
    IF EXISTS (SELECT 1 FROM public.subscriptions_backup LIMIT 1) THEN
        -- First, save the RLS policies so we can recreate them later
        RAISE NOTICE 'Dropping RLS policies on subscriptions table...';

        -- Drop the RLS policies that depend on the user_id column
        DROP POLICY IF EXISTS "Users can view own subscriptions" ON public.subscriptions;
        DROP POLICY IF EXISTS "Users can view their own subscriptions" ON public.subscriptions;

        -- Drop the existing foreign key constraint
        ALTER TABLE public.subscriptions DROP CONSTRAINT IF EXISTS subscriptions_user_id_fkey;

        -- Create a temporary column with the correct data type
        ALTER TABLE public.subscriptions ADD COLUMN user_id_uuid UUID;

        -- Update the new column with converted values
        UPDATE public.subscriptions SET user_id_uuid = user_id::UUID WHERE user_id IS NOT NULL;

        -- Drop the old column and rename the new one
        ALTER TABLE public.subscriptions DROP COLUMN user_id;
        ALTER TABLE public.subscriptions RENAME COLUMN user_id_uuid TO user_id;

        -- Add the foreign key constraint back
        ALTER TABLE public.subscriptions
        ADD CONSTRAINT subscriptions_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id);

        -- Recreate the RLS policies
        CREATE POLICY "Users can view own subscriptions"
        ON public.subscriptions FOR SELECT
        USING (auth.uid() = user_id);

        CREATE POLICY "Users can view their own subscriptions"
        ON public.subscriptions FOR SELECT
        USING (user_id = auth.uid());

        RAISE NOTICE 'Successfully converted subscriptions.user_id to UUID type and recreated RLS policies';
    ELSE
        RAISE EXCEPTION 'Backup table is empty or does not exist. Aborting operation for safety.';
    END IF;
END $$;

-- Step 3: Check and add foreign key constraints to appointment_requests and family_members if needed
DO $$
DECLARE
    constraint_exists_ar BOOLEAN;
    constraint_exists_fm BOOLEAN;
BEGIN
    -- Check if the constraint already exists for appointment_requests
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'appointment_requests_user_id_fkey'
        AND table_name = 'appointment_requests'
    ) INTO constraint_exists_ar;

    -- Check if the constraint already exists for family_members
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'family_members_user_id_fkey'
        AND table_name = 'family_members'
    ) INTO constraint_exists_fm;

    -- Add foreign key constraint to appointment_requests.user_id if it doesn't exist
    IF NOT constraint_exists_ar THEN
        ALTER TABLE public.appointment_requests
        ADD CONSTRAINT appointment_requests_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id);
        RAISE NOTICE 'Added foreign key constraint to appointment_requests';
    ELSE
        RAISE NOTICE 'Foreign key constraint for appointment_requests already exists';
    END IF;

    -- Add foreign key constraint to family_members.user_id if it doesn't exist
    IF NOT constraint_exists_fm THEN
        ALTER TABLE public.family_members
        ADD CONSTRAINT family_members_user_id_fkey
        FOREIGN KEY (user_id) REFERENCES auth.users(id);
        RAISE NOTICE 'Added foreign key constraint to family_members';
    ELSE
        RAISE NOTICE 'Foreign key constraint for family_members already exists';
    END IF;

    RAISE NOTICE 'Foreign key constraint check completed for appointment_requests and family_members';
END $$;

-- Step 4: Ensure public.users.id and public.users.user_id are consistent
DO $$
BEGIN
    -- Update public.users.user_id to match id if they're different
    UPDATE public.users
    SET user_id = id::text
    WHERE id::text != user_id;

    RAISE NOTICE 'Successfully ensured consistency between public.users.id and public.users.user_id';
END $$;

-- Step 5: Add a trigger to maintain consistency for new users
CREATE OR REPLACE FUNCTION public.maintain_user_id_consistency()
RETURNS TRIGGER AS $$
BEGIN
    NEW.user_id := NEW.id::text;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS maintain_user_id_consistency_trigger ON public.users;
CREATE TRIGGER maintain_user_id_consistency_trigger
BEFORE INSERT OR UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION public.maintain_user_id_consistency();

-- Final verification
DO $$
BEGIN
    RAISE NOTICE 'Successfully created trigger to maintain user_id consistency';
    RAISE NOTICE 'Database schema update complete. All user ID references should now be consistent.';
    RAISE NOTICE 'Please verify the application functionality after applying these changes.';
END $$;
