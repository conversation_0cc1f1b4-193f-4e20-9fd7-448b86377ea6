<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Google OAuth Authentication Test</h1>
        <p>This page tests the Google OAuth authentication flow and session persistence.</p>

        <div class="test-section info">
            <h3>📋 Test Instructions</h3>
            <ol>
                <li>Click "Test Google OAuth" to initiate Google authentication</li>
                <li>Complete the Google sign-in process</li>
                <li>You should be redirected back to the dashboard</li>
                <li>Navigate to Stripe dashboard and back to test session persistence</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔐 Authentication Status</h3>
            <div id="authStatus">Checking...</div>
            <button onclick="checkAuthStatus()">Refresh Status</button>
            <button onclick="testGoogleOAuth()">Test Google OAuth</button>
            <button onclick="testLogout()">Test Logout</button>
        </div>

        <div class="test-section">
            <h3>🍪 Cookie Information</h3>
            <div id="cookieInfo">Loading...</div>
            <button onclick="checkCookies()">Refresh Cookies</button>
        </div>

        <div class="test-section">
            <h3>💾 Local Storage</h3>
            <div id="storageInfo">Loading...</div>
            <button onclick="checkStorage()">Refresh Storage</button>
        </div>

        <div class="test-section">
            <h3>🔄 Session Persistence Test</h3>
            <button onclick="simulateStripeNavigation()">Simulate Stripe Navigation</button>
            <button onclick="testSessionRestore()">Test Session Restore</button>
        </div>

        <div class="test-section">
            <h3>📝 Test Log</h3>
            <div id="testLog" class="log"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script type="module">
        import { createClient } from '@supabase/supabase-js';

        const supabaseUrl = 'https://tfvswgreslsbctjrvdbd.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRmdnN3Z3Jlc2xzYmN0anJ2ZGJkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDIzMjEyODUsImV4cCI6MjA1Nzg5NzI4NX0.5bNos8JS_77EUjWOxIGHpgNaYE28qQvO2g4qj0wRWiU';

        const supabase = createClient(supabaseUrl, supabaseAnonKey);

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('testLog');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        window.clearLog = function() {
            document.getElementById('testLog').textContent = '';
        }

        window.checkAuthStatus = async function() {
            try {
                log('Checking authentication status...');
                const { data: { session }, error } = await supabase.auth.getSession();
                
                const statusDiv = document.getElementById('authStatus');
                if (error) {
                    statusDiv.innerHTML = `<span style="color: red;">❌ Error: ${error.message}</span>`;
                    log(`Auth error: ${error.message}`);
                } else if (session) {
                    statusDiv.innerHTML = `<span style="color: green;">✅ Authenticated as ${session.user.email}</span>`;
                    log(`Authenticated as: ${session.user.email}`);
                } else {
                    statusDiv.innerHTML = `<span style="color: orange;">⚠️ Not authenticated</span>`;
                    log('Not authenticated');
                }
            } catch (error) {
                log(`Error checking auth status: ${error.message}`);
            }
        }

        window.testGoogleOAuth = async function() {
            try {
                log('Initiating Google OAuth...');
                const origin = window.location.origin;
                const callbackUrl = `${origin}/auth/callback?redirectTo=/dashboard`;
                
                log(`Callback URL: ${callbackUrl}`);
                
                const { data, error } = await supabase.auth.signInWithOAuth({
                    provider: 'google',
                    options: {
                        redirectTo: callbackUrl
                    }
                });

                if (error) {
                    log(`OAuth error: ${error.message}`);
                } else {
                    log('OAuth initiated successfully, redirecting...');
                }
            } catch (error) {
                log(`Error initiating OAuth: ${error.message}`);
            }
        }

        window.testLogout = async function() {
            try {
                log('Testing logout...');
                const { error } = await supabase.auth.signOut();
                if (error) {
                    log(`Logout error: ${error.message}`);
                } else {
                    log('Logout successful');
                    checkAuthStatus();
                }
            } catch (error) {
                log(`Error during logout: ${error.message}`);
            }
        }

        window.checkCookies = function() {
            const cookies = document.cookie.split(';').map(c => c.trim()).filter(c => c);
            const authCookies = cookies.filter(c => 
                c.includes('supabase') || 
                c.includes('auth') || 
                c.includes('oauth') ||
                c.includes('session')
            );
            
            const cookieDiv = document.getElementById('cookieInfo');
            if (authCookies.length > 0) {
                cookieDiv.innerHTML = authCookies.map(c => `<div>${c}</div>`).join('');
            } else {
                cookieDiv.innerHTML = '<span style="color: orange;">No auth-related cookies found</span>';
            }
            
            log(`Found ${authCookies.length} auth-related cookies`);
        }

        window.checkStorage = function() {
            const authKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('auth') || key.includes('supabase') || key.includes('session'))) {
                    authKeys.push(key);
                }
            }
            
            const storageDiv = document.getElementById('storageInfo');
            if (authKeys.length > 0) {
                storageDiv.innerHTML = authKeys.map(key => 
                    `<div><strong>${key}:</strong> ${localStorage.getItem(key)?.substring(0, 100)}...</div>`
                ).join('');
            } else {
                storageDiv.innerHTML = '<span style="color: orange;">No auth-related localStorage items found</span>';
            }
            
            log(`Found ${authKeys.length} auth-related localStorage items`);
        }

        window.simulateStripeNavigation = function() {
            log('Simulating Stripe navigation...');
            sessionStorage.setItem('stripe_redirect', 'true');
            sessionStorage.setItem('external_return', 'true');
            log('Stripe navigation flags set');
        }

        window.testSessionRestore = async function() {
            try {
                log('Testing session restore...');
                const { data: { session }, error } = await supabase.auth.refreshSession();
                
                if (error) {
                    log(`Session restore error: ${error.message}`);
                } else if (session) {
                    log('Session restored successfully');
                    checkAuthStatus();
                } else {
                    log('No session to restore');
                }
            } catch (error) {
                log(`Error testing session restore: ${error.message}`);
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            log('Google OAuth test page loaded');
            checkAuthStatus();
            checkCookies();
            checkStorage();
        });
    </script>
</body>
</html>
