#!/bin/bash

# SRVXP Dependency Separation Script
# This script moves dependencies to SRVXP_dependencies and creates symlinks

set -e  # Exit on any error

echo "🚀 SRVXP Dependency Separation Setup"
echo "=================================="

# Define paths
PROJECT_DIR="/Users/<USER>/src/SRVXP"
DEPS_DIR="/Users/<USER>/src/SRVXP_dependencies"

# Ensure we're in the right directory
cd "$PROJECT_DIR"

echo "📁 Current project directory: $(pwd)"
echo "📁 Dependencies directory: $DEPS_DIR"

# Create dependencies directory structure if it doesn't exist
echo "📦 Setting up dependencies directory structure..."
mkdir -p "$DEPS_DIR"
mkdir -p "$DEPS_DIR/cache"
mkdir -p "$DEPS_DIR/build_artifacts"

# Function to safely move and link
move_and_link() {
    local item=$1
    local target_subdir=${2:-""}
    
    if [ -d "$item" ] || [ -f "$item" ]; then
        echo "  📂 Moving $item..."
        
        # Create target directory if using subdirectory
        if [ -n "$target_subdir" ]; then
            mkdir -p "$DEPS_DIR/$target_subdir"
            mv "$item" "$DEPS_DIR/$target_subdir/"
            ln -sf "$DEPS_DIR/$target_subdir/$item" "$item"
        else
            mv "$item" "$DEPS_DIR/"
            ln -sf "$DEPS_DIR/$item" "$item"
        fi
        
        echo "  ✅ Linked $item -> $DEPS_DIR/$target_subdir$item"
    else
        echo "  ⏭️  $item not found, skipping..."
    fi
}

echo ""
echo "🔄 Moving dependencies and creating symlinks..."

# Move main dependency directories
move_and_link "node_modules"
move_and_link ".next" "build_artifacts"
move_and_link "build" "build_artifacts"

# Move cache and lock files
move_and_link "bun.lock" "cache"
move_and_link "package-lock.json" "cache"
move_and_link "tsconfig.tsbuildinfo" "cache"

# Move any other build artifacts that might exist
echo ""
echo "🧹 Checking for additional build artifacts..."
if [ -f ".eslintcache" ]; then
    move_and_link ".eslintcache" "cache"
fi

if [ -d ".turbo" ]; then
    move_and_link ".turbo" "cache"
fi

if [ -d "dist" ]; then
    move_and_link "dist" "build_artifacts"
fi

echo ""
echo "📋 Creating dependency summary..."

# Create a summary file in the dependencies folder
cat > "$DEPS_DIR/README.md" << EOF
# SRVXP Dependencies

This folder contains all the dependencies and build artifacts for the SRVXP project.

## Structure
- \`node_modules/\` - NPM/Bun dependencies (494MB)
- \`build_artifacts/\` - Next.js build outputs
  - \`.next/\` - Next.js development build cache (101MB)
  - \`build/\` - Production build output (255MB)
- \`cache/\` - Lock files and build caches
  - \`bun.lock\` - Bun lock file
  - \`package-lock.json\` - NPM lock file (if exists)
  - \`tsconfig.tsbuildinfo\` - TypeScript incremental build info

## Total Size: ~850MB

## Symlinks in SRVXP Project
All items in this folder are symlinked back to the main SRVXP project so that:
1. VSCode/Cursor can resolve dependencies correctly
2. Bun/Next.js can find all required files
3. The project runs normally
4. Only the clean source code exists in SRVXP folder

## Regenerating Dependencies
To reinstall dependencies:
\`\`\`bash
cd /Users/<USER>/src/SRVXP
bun install
\`\`\`

This will recreate the node_modules in the dependencies folder via the symlink.
EOF

echo ""
echo "🔗 Verifying symlinks..."
ls -la "$PROJECT_DIR" | grep " -> " || echo "No symlinks found yet - this is expected on first run"

echo ""
echo "📊 Checking folder sizes..."
echo "SRVXP project folder (without dependencies):"
du -sh "$PROJECT_DIR" --exclude="node_modules" --exclude=".next" --exclude="build" 2>/dev/null || du -sh "$PROJECT_DIR"

echo ""
echo "SRVXP_dependencies folder:"
du -sh "$DEPS_DIR" 2>/dev/null || echo "Dependencies folder size: calculating..."

echo ""
echo "✅ Dependency separation complete!"
echo ""
echo "🎯 What's been accomplished:"
echo "  • Dependencies moved to: $DEPS_DIR"
echo "  • Symlinks created in project for seamless operation"
echo "  • Project folder cleaned for sharing with Claude"
echo "  • VSCode/Cursor will work normally with linked dependencies"
echo ""
echo "🚀 You can now:"
echo "  1. Share only the SRVXP folder with Claude (much smaller context)"
echo "  2. Open SRVXP in VSCode/Cursor and it will work normally"
echo "  3. Run 'bun dev' from SRVXP directory as usual"
echo ""
echo "⚠️  Note: If you need to reinstall dependencies, run 'bun install' from the SRVXP directory" 