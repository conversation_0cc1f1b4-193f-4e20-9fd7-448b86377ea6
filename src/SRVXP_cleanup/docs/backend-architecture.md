# Backend Worker Architecture Plan
## Sans Rendez-Vous Express - Appointment Booking System

### Table of Contents
1. [Current Project Understanding](#current-project-understanding)
2. [System Overview](#system-overview)
3. [Core Components](#core-components)
4. [Detailed Architecture](#detailed-architecture)
5. [Data Flow](#data-flow)
6. [Docker Architecture](#docker-architecture)
7. [Monitoring and Scaling](#monitoring-and-scaling)
8. [Security Considerations](#security-considerations)
9. [Development and Deployment](#development-and-deployment)
10. [Configuration Files](#configuration-files)
11. [Implementation Roadmap](#implementation-roadmap)

---

## Current Project Understanding

Your project already has:
- **Frontend**: Next.js app with French/English UI for appointment booking
- **Database**: Supabase with well-structured tables:
  - `appointment_requests` - Main table for booking requests
  - `completed_appointments` - Successful bookings
  - `cancelled_appointments` - Cancelled requests  
  - `family_members` - Multi-user support
  - `subscriptions` - Payment system integration
- **Authentication**: Supabase Auth with RLS policies
- **API Layer**: Next.js API routes for CRUD operations
- **Payment**: Stripe integration for subscriptions

---

## System Overview

```
[Frontend] → [Next.js API] → [Supabase] → [Worker Manager] → [Redis Queue] → [Puppeteer Workers] → [Email Notifications]
```

### Architecture Diagram
```
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│   Frontend      │───▶│  Next.js API │───▶│   Supabase DB   │
│   (React)       │    │   Routes     │    │ appointment_    │
└─────────────────┘    └──────────────┘    │   requests      │
                                           └─────────┬───────┘
                                                     │
                                                     ▼
┌─────────────────┐    ┌──────────────┐    ┌─────────────────┐
│  Email Service  │◀───│ Redis Queue  │◀───│ Worker Manager  │
│   (Resend)      │    │  (Bull/BQQ)  │    │   (Node.js)     │
└─────────────────┘    └──────┬───────┘    └─────────────────┘
                              │
                              ▼
                     ┌─────────────────┐
                     │ Puppeteer       │
                     │ Worker Pool     │
                     │ (Docker)        │
                     └─────────────────┘
```

---

## Core Components

### A. Worker Manager (Node.js Service)
- **Location**: `backend/worker-manager/`
- **Purpose**: Monitors Supabase for new appointment requests and queues them
- **Key Features**:
  - PostgreSQL `LISTEN/NOTIFY` for real-time detection
  - Uses the existing `notify_new_appointment()` trigger
  - Rate limiting and duplicate prevention
  - Health monitoring and logging
  - Graceful shutdown handling

#### Implementation Tasks:

**Setup & Configuration:** ✅ **COMPLETED**
- [x] Create project structure (`backend/worker-manager/`)
- [x] Initialize Node.js project with TypeScript
- [x] Configure development dependencies (nodemon, ts-node, etc.)
- [x] Create environment configuration system
- [x] Set up logging infrastructure (winston/pino)

**Database Integration:** ✅ **COMPLETED**
- [x] Set up Supabase client connection
- [x] Implement Supabase Realtime listener (upgraded from PostgreSQL LISTEN/NOTIFY)
- [x] Create database connection health checks
- [x] Add connection retry logic with exponential backoff
- [x] Test notification trigger integration

**Redis Queue Integration:** ✅ **COMPLETED**
- [x] Set up Redis client connection
- [x] Implement job publisher for appointment requests
- [x] Add job deduplication logic
- [x] Create job prioritization system
- [x] Add Redis connection health checks

**Core Service Logic:** ✅ **COMPLETED**
- [x] Create appointment monitor service
- [x] Implement rate limiting (prevent spam/duplicate jobs)
- [x] Add graceful shutdown handling
- [x] Create health check endpoint
- [x] Add application startup sequence

**Docker & Deployment:** ✅ **COMPLETED**
- [x] Create Dockerfile for the service
- [x] Set up docker-compose configuration
- [x] Add volume mounts for logs
- [x] Configure environment variables
- [x] Test containerized deployment

**Monitoring & Logging:** ✅ **COMPLETED**
- [x] Implement structured logging with correlation IDs
- [x] Add metrics collection (job counts, processing times)
- [x] Create health monitoring dashboard endpoint
- [x] Add error tracking and alerting hooks
- [x] Set up log rotation and retention

**Testing & Documentation:** 🚧 **PARTIALLY COMPLETED**
- [x] Create basic standalone tests for core components
- [x] Test integration with Supabase Realtime
- [x] Test Redis job publishing and queue operations
- [ ] Write comprehensive unit tests for core components
- [ ] Create integration tests with Supabase
- [ ] Add Redis integration tests
- [ ] Create API documentation
- [ ] Write deployment and operation guides

#### 🎉 **Worker Manager Status: PRODUCTION READY**

**✅ Successfully Implemented & Tested:**
- **Real-time Processing**: Uses Supabase Realtime subscriptions for immediate appointment detection
- **FIFO Job Processing**: First-come, first-serve queue ordering with retry job priority
- **Job Publishing**: Successfully publishing real appointment data to Redis queues with FIFO ordering
- **Metrics Collection**: Tracking job publication metrics
- **Database Operations**: Updating appointment status from 'pending' → 'in_progress'
- **Deduplication**: Preventing duplicate job creation with base64 encoded keys
- **Health Monitoring**: All services healthy and monitored (Supabase, Redis, Appointment Monitor, Job Publisher)
- **Error Handling**: Graceful handling of failures and reconnection logic

**🔍 Verified with Real Data:**
```json
{
  "jobId": "job_1749009486613_618vwoy32",
  "appointmentRequestId": "83e479b8-3430-47ae-a28e-d5b0adc5ca0a",
  "patientInfo": {
    "firstName": "Ami", "lastName": "Jette",
    "healthCard": "GADG23532523", "healthCardSequence": "35",
    "dateOfBirth": "1987-06-05", "postalCode": "K2G 6S3"
  },
  "searchCriteria": {
    "appointmentDate": "2025-06-03",
    "timePreference": "asap", "searchRadius": 50
  },
  "isRetry": false, "status": "queued"
}
```

**🎯 FIFO Queue System:**
- **Regular Jobs**: Processed in order of submission (timestamp-based scoring)
- **Retry Jobs**: Skip to front of queue (negative timestamp scoring)
- **No Priority Logic**: All new jobs are treated equally regardless of urgency or time preference
- **Exception**: Failed/timed-out jobs get priority for retry processing

### B. Redis Queue System ✅ **IMPLEMENTED** (Integrated within Worker Manager)
- **Location**: `backend/worker-manager/src/queue/` (integrated approach)
- **Purpose**: Manages job distribution and worker coordination
- **Queue Types**:
  - ✅ `appointment-requests` (FIFO-based sorted set) 
  - 🚧 `retry-queue` (planned for worker implementation)
  - 🚧 `dead-letter-queue` (planned for worker implementation)
- **Features**:
  - ✅ **FIFO job processing** (First-Come, First-Serve ordering)
  - ✅ **Retry job prioritization** (Failed/timed-out jobs skip to front of queue)
  - ✅ **Job deduplication** (Base64 encoded keys prevent duplicates)
  - ✅ **Redis health monitoring** with connection retry logic
  - ✅ **Metrics collection** (job publication tracking)
  - 🚧 Retry mechanisms with exponential backoff (for worker implementation)
  - 🚧 Job progress tracking (for worker implementation)
  - 🚧 Queue monitoring dashboard (planned)

#### Implementation Tasks:

**Core Queue Infrastructure:** ✅ **COMPLETED**
- [x] Redis client connection with health checks (`redis-client.ts`)
- [x] Job publisher with priority queuing (`job-publisher.ts`)
- [x] Priority-based sorted sets (using Redis ZADD with priority scores)
- [x] Job deduplication system (Base64 encoded unique keys)
- [x] Connection retry logic with exponential backoff
- [x] Metrics tracking (job publication counters)

**Queue Operations:** ✅ **COMPLETED**
- [x] **Job Publishing**: Publishing real appointment data to Redis
- [x] **FIFO Processing**: First-come, first-serve job ordering using timestamps
- [x] **Retry Job Priority**: Failed/timed-out jobs skip to front of queue (negative scores)
- [x] **Deduplication**: Preventing duplicate jobs with unique identifiers
- [x] **Health Monitoring**: Redis connection status tracking

**Advanced Features:** 🚧 **PLANNED FOR WORKER IMPLEMENTATION**
- [ ] Job consumption patterns (for Puppeteer workers)
- [ ] Retry queue management (failed job handling)
- [ ] Dead letter queue (permanently failed jobs)
- [ ] Queue monitoring dashboard (Bull Board or custom)
- [ ] Job progress tracking (in-progress, completed, failed states)

#### 🔍 **Current Queue Status:**
```bash
# Active job example in Redis:
Key: appointment-requests
Type: sorted set (ZSET)
Score: 1749009486613 (timestamp for FIFO ordering)
Value: {
  "jobId": "job_1749009486613_618vwoy32",
  "appointmentRequestId": "83e479b8-3430-47ae-a28e-d5b0adc5ca0a",
  "patientInfo": { "firstName": "Ami", "lastName": "Jette", ... },
  "searchCriteria": { "appointmentDate": "2025-06-03", "timePreference": "asap" },
  "isRetry": false, "status": "queued"
}

# Retry job example (skips to front):
Score: -1749009486613 (negative timestamp for priority)
Value: { ..., "isRetry": true, "attempts": 1, ... }

# Metrics tracked:
metrics:appointment_request_published = 1
```

**🎯 Integration Notes:**
- Originally planned as separate `backend/queue-manager/` service
- **Actually implemented:** Integrated within Worker Manager for better cohesion
- **Benefits:** Reduced complexity, direct communication, easier deployment
- **Next:** Puppeteer workers will consume from this established queue system

### C. Puppeteer Worker Containers
- **Location**: `backend/workers/puppeteer/`
- **Purpose**: Scrape government appointment websites
- **Architecture**:
  - Horizontally scalable Docker containers
  - Each worker handles one job at a time
  - Headless Chrome with stealth plugins
  - Screenshot capture for debugging
  - Proxy rotation support

### D. Notification Service
- **Location**: `backend/services/notifications/`
- **Purpose**: Email notifications via Resend
- **Features**:
  - Template-based emails (French/English)
  - Success/failure notifications
  - Appointment details and next steps
  - Unsubscribe handling

---

## Detailed Architecture

### Worker Manager Implementation
```
backend/worker-manager/
├── src/
│   ├── index.ts                 # Main entry point
│   │   ├── database/
│   │   │   ├── supabase-client.ts   # Supabase connection
│   │   │   └── listeners.ts         # PostgreSQL LISTEN setup
│   │   ├── queue/
│   │   │   ├── redis-client.ts      # Redis connection
│   │   │   └── job-publisher.ts     # Job publishing logic
│   │   ├── services/
│   │   │   ├── appointment-monitor.ts # Monitor for new requests
│   │   │   └── health-check.ts      # Health monitoring
│   │   └── utils/
│   │       ├── logger.ts            # Structured logging
│   │       └── config.ts            # Environment configuration
│   ├── Dockerfile
│   ├── package.json
│   └── docker-compose.yml
```

### Redis Queue Structure
```javascript
// Jobs in Redis (FIFO-based)
{
  "id": "uuid",
  "type": "appointment-request",
  "isRetry": false, // true for retry jobs that skip to front
  "data": {
    "appointment_request_id": "uuid",
    "patient_info": {
      "first_name": "string",
      "last_name": "string",
      "health_card": "string",
      "health_card_sequence": "string",
      "date_of_birth": "string",
      "postal_code": "string"
    },
    "search_criteria": {
      "appointment_date": "string",
      "time_preference": "asap|morning|afternoon|evening",
      "search_radius": "number"
    },
    "notification_preferences": {
      "email": "string",
      "language": "fr|en"
    }
  },
  "attempts": 0,
  "max_attempts": 3,
  "created_at": "timestamp",
  "scheduled_for": "timestamp"
}
```

### Puppeteer Worker Architecture
```
backend/workers/
├── puppeteer/
│   ├── Dockerfile
│   ├── src/
│   │   ├── index.ts             # Worker entry point
│   │   ├── scrapers/
│   │   │   ├── base-scraper.ts  # Abstract scraper class
│   │   │   ├── quebec-scraper.ts # Quebec government sites
│   │   │   └── ontario-scraper.ts # Ontario government sites
│   │   ├── utils/
│   │   │   ├── browser-pool.ts  # Browser instance management
│   │   │   ├── stealth.ts       # Anti-detection measures
│   │   │   └── captcha-solver.ts # CAPTCHA handling
│   │   └── services/
│   │       ├── appointment-finder.ts # Core appointment logic
│   │       └── result-processor.ts  # Process found appointments
│   └── config/
│       ├── user-agents.json    # Rotating user agents
│       └── proxies.json        # Proxy configuration
├── shared/
│   ├── types/
│   │   └── appointment-types.ts # Shared TypeScript types
│   └── utils/
│       └── validation.ts       # Data validation utilities
└── docker-compose.workers.yml
```

---

## Data Flow

### New Appointment Request Flow
1. **User submits appointment request** via frontend
2. **Next.js API validates** and stores in `appointment_requests` table
3. **PostgreSQL trigger fires** `notify_new_appointment()`
4. **Worker Manager receives notification** via `LISTEN`
5. **Worker Manager validates request** and publishes to Redis queue
6. **Available Puppeteer worker picks up job**
7. **Worker scrapes government website** for appointments
8. **Results stored** in `completed_appointments` or retry queue
9. **Notification service sends email** to user
10. **Appointment request status updated** to 'completed'

### Retry Logic Flow
- **Failed jobs moved** to retry queue with exponential backoff
- **Maximum 3 retry attempts**
- **Different failure types** handled differently:
  - Network errors → Immediate retry
  - CAPTCHA failures → Delayed retry with different approach
  - No appointments found → Lower priority retry
- **Dead letter queue** for permanently failed jobs

### Job Processing Order
```javascript
// FIFO Queue Scoring System
const QUEUE_SCORING = {
  REGULAR_JOBS: 'timestamp',     // Positive timestamp for FIFO order
  RETRY_JOBS: '-timestamp',      // Negative timestamp to skip to front
  DEAD_LETTER: 'timestamp'       // Regular timestamp (no priority)
}

// Example scores:
// Job 1 (submitted first):  score = 1749009486613
// Job 2 (submitted second): score = 1749009486614  
// Job 3 (retry):           score = -1749009486615 (processed first)
// Job 4 (submitted third):  score = 1749009486616
// 
// Processing order: Job 3 (retry), Job 1, Job 2, Job 4
```

---

## Docker Architecture

### docker-compose.yml (Main Infrastructure)
```yaml
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    restart: unless-stopped
  
  worker-manager:
    build: ./backend/worker-manager
    environment:
      - NODE_ENV=production
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=info
    depends_on:
      - redis
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
  
  notification-service:
    build: ./backend/services/notifications
    environment:
      - NODE_ENV=production
      - RESEND_API_KEY=${RESEND_API_KEY}
      - REDIS_URL=redis://redis:6379
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
    depends_on:
      - redis
    restart: unless-stopped

  queue-dashboard:
    image: deadly0/bull-board
    ports:
      - "3001:3000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis

volumes:
  redis_data:
  logs:
```

### docker-compose.workers.yml (Scalable Workers)
```yaml
version: '3.8'
services:
  puppeteer-worker:
    build: ./backend/workers/puppeteer
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - WORKER_ID=${WORKER_ID:-worker-${RANDOM}}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - PROXY_ROTATION=${PROXY_ROTATION:-false}
      - HEADLESS=${HEADLESS:-true}
      - DEBUG_SCREENSHOTS=${DEBUG_SCREENSHOTS:-false}
    volumes:
      - ./backend/workers/screenshots:/app/screenshots
      - ./logs:/app/logs
    networks:
      - worker-network
    shm_size: 1gb
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
    restart: unless-stopped

networks:
  worker-network:
    driver: bridge
```

---

## Monitoring and Scaling

### Monitoring Components
- **Redis queue length monitoring**
- **Worker health checks** (heartbeat every 30s)
- **Success/failure rate tracking**
- **Performance metrics**:
  - Scraping time per job
  - Queue wait time
  - Memory/CPU usage per worker
- **Error alerting** via webhooks
- **Queue dashboard** (Bull Board UI)

### Metrics to Track
```javascript
// Key Performance Indicators
const METRICS = {
  queue: {
    pending_jobs: 'number',
    active_jobs: 'number',
    completed_jobs: 'number',
    failed_jobs: 'number',
    avg_wait_time: 'seconds'
  },
  workers: {
    active_workers: 'number',
    worker_health: 'boolean',
    avg_processing_time: 'seconds',
    success_rate: 'percentage'
  },
  business: {
    appointments_found: 'number',
    appointment_success_rate: 'percentage',
    user_satisfaction: 'rating'
  }
}
```

### Scaling Strategy
- **Horizontal Scaling**: Add more Puppeteer worker containers
- **Load Balancing**: Redis automatically distributes jobs
- **Auto-scaling**: Based on queue length and worker availability
  ```bash
  # Auto-scale command
  docker-compose -f docker-compose.workers.yml up --scale puppeteer-worker=5
  ```
- **Resource Management**: CPU/memory limits per container

---

## Security Considerations

### Data Protection
- **Environment Variables**: All sensitive data in env files
- **Network Isolation**: Docker networks for inter-service communication
- **API Keys**: Separate service keys for different components
- **Data Encryption**: Encrypt sensitive patient data in Redis
- **PII Handling**: Minimal data retention, secure deletion

### Scraping Protection
- **Rate Limiting**: Prevent abuse of government websites
- **IP Rotation**: Use proxy services to avoid IP bans
- **User Agent Rotation**: Mimic real browsers
- **Session Management**: Clean browser state between jobs
- **Respectful Scraping**: Honor robots.txt and rate limits

### Access Control
```javascript
// Redis ACL configuration
const REDIS_USERS = {
  'worker-manager': ['read', 'write'] // Full queue access
  'puppeteer-worker': ['read'] // Job consumption only
  'notification-service': ['read'] // Result consumption only
  'monitoring': ['read'] // Metrics only
}
```

---

## Development and Deployment

### Local Development Setup
```bash
# 1. Start infrastructure
docker-compose up redis queue-dashboard

# 2. Install dependencies
cd backend/worker-manager && npm install
cd backend/workers/puppeteer && npm install
cd backend/services/notifications && npm install

# 3. Run services locally
cd backend/worker-manager && npm run dev
cd backend/workers/puppeteer && npm run dev
cd backend/services/notifications && npm run dev

# 4. Scale workers for testing
docker-compose -f docker-compose.workers.yml up --scale puppeteer-worker=2

# 5. Access queue dashboard
open http://localhost:3001
```

### Production Deployment Options

#### Option 1: Docker Swarm
```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml appointment-system

# Scale workers
docker service scale appointment-system_puppeteer-worker=5
```

#### Option 2: Kubernetes
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: puppeteer-workers
spec:
  replicas: 3
  selector:
    matchLabels:
      app: puppeteer-worker
  template:
    metadata:
      labels:
        app: puppeteer-worker
    spec:
      containers:
      - name: puppeteer-worker
        image: your-registry/puppeteer-worker:latest
        resources:
          limits:
            memory: "1Gi"
            cpu: "500m"
```