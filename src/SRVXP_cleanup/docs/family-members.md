# Family Members Management

This document explains how the family members feature works in the Understood application.

## Overview

The family members feature allows users to save information about up to 5 family members. This information includes:

- First name
- Last name
- Health card (first 4 characters)
- Birth date

## Implementation Details

### Database

Family member data is stored in the `family_members` table in Supabase with the following structure:

- `id`: UUID primary key
- `user_id`: Foreign key to auth.users
- `first_name`: Text
- `last_name`: Text
- `health_card`: Text (maximum 4 characters)
- `birth_date`: Timestamp with timezone
- `position`: Integer (1-5, for ordering)
- `created_at`: Timestamp
- `updated_at`: Timestamp

Row Level Security (RLS) policies are configured so that users can only access their own family members.

### Frontend Components

- **FamilyMembersContext**: React context that manages the state of family members
- **FamilyMembersProvider**: Provider component that wraps parts of the application needing access to family member data
- **UtilisateursPage**: Page component for managing family members

### API

The family members API is defined in `/src/lib/family-members/api.ts` and includes the following functions:

- `getFamilyMembers(userId)`: Fetches all family members for a user
- `saveFamilyMember(userId, member, position)`: Saves a single family member
- `saveAllFamilyMembers(userId, members)`: Saves all family members at once
- `deleteFamilyMember(memberId)`: Deletes a family member

## Workflow

1. When a user visits the family members page, the system fetches their family members from Supabase
2. The system ensures that exactly 5 slots are always available for family members
3. The user can edit each family member's information
4. When saving a family member by clicking the Save button:
   - The changes are saved locally in the application state
   - The data is also saved to Supabase in the background
5. The user can delete a family member by clicking the Delete button:
   - The member's data is cleared in the application state
   - The record is also deleted from Supabase in the background
6. All changes are automatically synchronized with Supabase

## Implementation Details for 5 Slots

The system guarantees that exactly 5 slots for family members are always displayed, even if fewer members are stored in the database:

1. The `getFamilyMembers` API function always returns an array of 5 members, filling in empty slots for positions that don't have data
2. The `FamilyMembersContext` has additional validation to maintain 5 slots
3. The UI component in `utilisateurs/page.tsx` has a fail-safe check to ensure 5 slots are rendered

## Error Handling

- If there's an error fetching family members, an error message is displayed
- If there's an error saving a family member, the UI indicates the failure
- Loading states are properly handled to provide feedback to the user
