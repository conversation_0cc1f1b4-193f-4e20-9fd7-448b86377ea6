"use client";

import { useState, useEffect, useMemo } from "react";
import { useAuth } from "@/lib/AuthContext";
import { getUserProfile } from "@/lib/user-utils";
import { useProfileUpdates } from "@/lib/profile-update-context";

// Type for user profile
export interface UserProfile {
  firstName: string;
  lastName: string;
  avatar: string | null;
  initials: string;
  role: string;
  fullName?: string;
  phone?: string;
}

/**
 * Hook to get and memoize the current user's profile information
 * Uses cached data where possible to minimize API calls
 */
// Create a global cache for the profile data to persist across component unmounts
let globalProfileCache: Record<string, UserProfile> = {};

export function useUserProfile() {
  // Function to invalidate profile cache and optionally reload data
  const invalidateCache = (userId: string, email: string, reload: boolean = true) => {
    // Clear from both localStorage and global cache
    const emailKey = `user_profile_${email}`;
    const idKey = `user_profile_id_${userId}`;
    
    localStorage.removeItem(emailKey);
    localStorage.removeItem(idKey);
    
    // Also clear from global cache
    if (emailKey in globalProfileCache) delete globalProfileCache[emailKey];
    if (idKey in globalProfileCache) delete globalProfileCache[idKey];
    
    // Force reload profile data if requested
    if (reload && userId && status === 'authenticated') {
      loadUserProfileData(userId);
      
      // Notify other components about the profile update
      if (notifyProfileUpdate) {
        notifyProfileUpdate(userId);
      }
    }
  };
  
  // Function to load profile data
  const loadUserProfileData = async (userId: string) => {
    try {
      // Fetch user profile from Supabase
      const userProfileData = await getUserProfile(userId);
      
      // Create profile object
      const firstName = userProfileData.firstName || "";
      const lastName = userProfileData.lastName || "";
      
      const supabaseProfile: UserProfile = {
        firstName,
        lastName,
        avatar: null,
        initials: `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || "??",
        role: "user",
        fullName: firstName || lastName ? `${firstName} ${lastName}`.trim() : "User",
        phone: userProfileData.phone || "",
      };
      
      setProfile(supabaseProfile);
      
      // Cache the profile in both localStorage and memory
      if (cacheKey) {
        localStorage.setItem(cacheKey, JSON.stringify(supabaseProfile));
        // Also cache in our global cache
        globalProfileCache[cacheKey] = supabaseProfile;
      }
      
      return supabaseProfile;
    } catch (err) {
      console.error("Error loading user profile:", err);
      throw err;
    }
  };
  const { user, status } = useAuth();
  const { notifyProfileUpdate, lastUpdatedUserId, lastUpdateTimestamp } = useProfileUpdates();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Generate a cache key from the user's email or ID
  const cacheKey = useMemo(() => {
    // Prefer using email for cache key, but fall back to ID if available
    return user?.email ? `user_profile_${user.email}` : (user?.id ? `user_profile_id_${user.id}` : null);
  }, [user?.email, user?.id]);

  // Load the user profile
  useEffect(() => {
    async function loadUserProfile() {
      if (status === "loading") {
        return;
      }

      if (status === "unauthenticated" || !user) {
        setProfile(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        // First try to get from global memory cache (fastest)
        if (cacheKey && cacheKey in globalProfileCache) {
          setProfile(globalProfileCache[cacheKey]);
          setIsLoading(false);
          return;
        }
        
        // Then try to get from localStorage cache
        const cachedProfile = cacheKey ? localStorage.getItem(cacheKey) : null;
        
        if (cachedProfile) {
          const parsedProfile = JSON.parse(cachedProfile);
          // Also store in global cache for future quick access
          if (cacheKey) globalProfileCache[cacheKey] = parsedProfile;
          setProfile(parsedProfile);
          setIsLoading(false);
          return;
        }

        // Fetch user profile from Supabase
        if (user?.id) {
          // Use the extracted function to load profile data
          await loadUserProfileData(user.id);
        } else {
          // Create a default profile using email
          const email = user?.email || "";
          const nameParts = email.split("@")[0].split(".");
          const firstName = nameParts[0] ? nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1) : "User";
          const lastName = nameParts[1] ? nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1) : "";
          
          const defaultProfile: UserProfile = {
            firstName,
            lastName,
            avatar: null,
            initials: `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase(),
            role: "user",
            fullName: `${firstName} ${lastName}`,
            phone: "",
          };
          
          setProfile(defaultProfile);
          
          // Cache the profile in both localStorage and memory
          if (cacheKey) {
            localStorage.setItem(cacheKey, JSON.stringify(defaultProfile));
            globalProfileCache[cacheKey] = defaultProfile;
          }
        }
      } catch (err) {
        console.error("Error loading user profile:", err);
        setError(err instanceof Error ? err : new Error("Failed to load user profile"));
      } finally {
        setIsLoading(false);
      }
    }

    loadUserProfile();
  }, [user, status, cacheKey]);
  
  // Listen for profile updates from other components
  useEffect(() => {
    // If this is our user that was updated, and we have a valid profile
    if (lastUpdatedUserId && user?.id === lastUpdatedUserId) {
      // Force a reload of profile data
      if (user.id) {
        // Clear cache from both localStorage and global cache
        if (cacheKey) {
          localStorage.removeItem(cacheKey);
          // Also clear from global cache
          if (cacheKey in globalProfileCache) delete globalProfileCache[cacheKey];
        }
        // Load fresh data
        loadUserProfileData(user.id);
      }
    }
  }, [lastUpdateTimestamp, lastUpdatedUserId, user?.id, cacheKey]);

  // Compute derived properties with better fallbacks
  const firstName = profile?.firstName || "";
  const lastName = profile?.lastName || "";
  const email = user?.email || "";
  
  // Derive a fallback name from email if we have no profile or incomplete profile
  let derivedFirstName = firstName;
  let derivedLastName = lastName;
  
  if ((!firstName && !lastName) && email) {
    const nameParts = email.split("@")[0].split(".");
    derivedFirstName = nameParts[0] ? nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1) : "";
    derivedLastName = nameParts[1] ? nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1) : "";
  }
  
  // Get best available full name
  const fullName = profile?.fullName || 
    (firstName || lastName ? `${firstName} ${lastName}`.trim() : 
     (derivedFirstName || derivedLastName ? `${derivedFirstName} ${derivedLastName}`.trim() : "User"));
  
  // Get best available initials
  const computedInitials = profile?.initials || 
    (firstName || lastName ? `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() : 
     (derivedFirstName || derivedLastName ? `${derivedFirstName.charAt(0)}${derivedLastName.charAt(0)}`.toUpperCase() : "??"));
  
  // Get phone number from profile
  const phone = profile?.phone || "";

  return {
    profile,
    isLoading,
    error,
    initials: computedInitials,
    fullName: fullName,
    // Helper computed properties
    firstName: firstName || derivedFirstName,
    lastName: lastName || derivedLastName,
    phone,
    // Add functions to manage profile data
    invalidateCache,
    reloadProfile: (userId: string) => loadUserProfileData(userId),
    // Return true if the profile was loaded from cache
    isFromCache: !!cacheKey && (!!globalProfileCache[cacheKey] || !!localStorage.getItem(cacheKey))
  };
}