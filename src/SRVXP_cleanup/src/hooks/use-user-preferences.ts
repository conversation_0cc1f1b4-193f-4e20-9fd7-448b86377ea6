"use client";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";
import { useAuth } from "@/lib/AuthContext";
import { getUserPreferences, saveUserPreferences, UserPreferences } from "@/lib/user-preferences-utils";

export function useUserPreferences() {
  const { user, status } = useAuth();
  const { setTheme } = useTheme();
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<Error | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);

  // Load preferences from Supabase
  useEffect(() => {
    async function loadPreferences() {
      if (status === "loading") {
        return;
      }

      if (status === "unauthenticated" || !user) {
        setPreferences(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // Fetch preferences directly from Supabase without using localStorage
        const supabasePrefs = await getUserPreferences(user.id);
        
        if (supabasePrefs) {
          setPreferences(supabasePrefs);
          
          // Apply theme from preferences
          if (supabasePrefs.theme) {
            setTheme(supabasePrefs.theme);
            console.log(`Applied theme from user preferences: ${supabasePrefs.theme}`);
          }
        }
      } catch (error) {
        console.error("Error loading preferences:", error);
      } finally {
        setIsLoading(false);
      }
    }

    loadPreferences();
  }, [user, status]);

  // Function to update preferences
  const updatePreferences = async (newPreferences: UserPreferences) => {
    if (!user) return;
    
    console.log('Updating user preferences:', newPreferences);
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      // Update local state right away for responsive UI
      setPreferences(newPreferences);
      
      // Store only language in localStorage (theme is exclusively in Supabase)
      localStorage.setItem("language", newPreferences.language);
      
      console.log('Local language preferences updated');
      
      // Save to Supabase
      const result = await saveUserPreferences(user.id, newPreferences);
      
      if (!result.success) {
        throw result.error || new Error("Failed to save preferences");
      }
      
      console.log('Preferences saved successfully to Supabase');
      setSaveSuccess(true);
      
      // Auto-reset success message after 3 seconds
      setTimeout(() => {
        setSaveSuccess(false);
      }, 3000);
      
    } catch (error) {
      console.error("Error saving preferences:", error);
      setSaveError(error as Error);
      
      // Reset success state if there was an error
      setSaveSuccess(false);
    } finally {
      setIsSaving(false);
    }
  };

  return {
    preferences,
    isLoading,
    isSaving,
    saveError,
    saveSuccess,
    updatePreferences
  };
}