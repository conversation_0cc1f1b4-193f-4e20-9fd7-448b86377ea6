/**
 * Zustand Usage Examples
 * 
 * This file contains examples of how to use the Zustand stores in the application.
 * It is not meant to be used in production, but rather as a reference for developers.
 */

'use client'

import { useEffect } from 'react'
import { 
  useUserStore, 
  useUserProfile, 
  usePreferences, 
  useTranslations, 
  useAppointmentHistory, 
  useSubscriptionDetails,
  useAppStore,
  useInitializeApp
} from '@/stores'

// Example 1: Basic usage with individual stores
export function BasicUsageExample() {
  // User authentication
  const user = useUserStore((state) => state.user)
  const signOut = useUserStore((state) => state.signOut)
  
  // User profile
  const { firstName, lastName, email } = useUserProfile()
  
  // Preferences
  const { theme, setTheme } = usePreferences()
  
  // Language
  const { language, setLanguage, t } = useTranslations()
  
  // Appointment history
  const { pendingAppointments } = useAppointmentHistory()
  
  // Subscription
  const { planType, formattedAmount, daysUntilRenewal } = useSubscriptionDetails()
  
  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold">Basic Usage Example</h1>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">User</h2>
        {user ? (
          <>
            <p>Email: {user.email}</p>
            <button 
              className="px-4 py-2 bg-red-500 text-white rounded"
              onClick={() => signOut()}
            >
              Sign Out
            </button>
          </>
        ) : (
          <p>Not signed in</p>
        )}
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Profile</h2>
        <p>Name: {firstName} {lastName}</p>
        <p>Email: {email}</p>
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Preferences</h2>
        <p>Theme: {theme}</p>
        <div className="flex space-x-2">
          <button 
            className="px-4 py-2 bg-gray-200 text-black rounded"
            onClick={() => setTheme('light')}
          >
            Light
          </button>
          <button 
            className="px-4 py-2 bg-gray-800 text-white rounded"
            onClick={() => setTheme('dark')}
          >
            Dark
          </button>
        </div>
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Language</h2>
        <p>Current: {language}</p>
        <div className="flex space-x-2">
          <button 
            className="px-4 py-2 bg-blue-500 text-white rounded"
            onClick={() => setLanguage('fr')}
          >
            Français
          </button>
          <button 
            className="px-4 py-2 bg-blue-500 text-white rounded"
            onClick={() => setLanguage('en')}
          >
            English
          </button>
        </div>
        <p>Translated greeting: {t('common.hello')}</p>
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Appointments</h2>
        <p>Pending appointments: {pendingAppointments.length}</p>
        {pendingAppointments.length > 0 && (
          <ul className="list-disc pl-5">
            {pendingAppointments.map((appointment) => (
              <li key={appointment.id}>
                {appointment.request_details.date} - {appointment.request_details.time}
              </li>
            ))}
          </ul>
        )}
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Subscription</h2>
        <p>Plan: {planType || 'None'}</p>
        {formattedAmount && (
          <p>Price: {formattedAmount}</p>
        )}
        {daysUntilRenewal && (
          <p>Renews in: {daysUntilRenewal} days</p>
        )}
      </div>
    </div>
  )
}

// Example 2: Using the combined app store
export function CombinedStoreExample() {
  // Get actions from the app store
  const { 
    signIn, 
    signOut, 
    fetchProfile, 
    setTheme, 
    setLanguage, 
    translate, 
    fetchAppointmentRequests, 
    fetchSubscription 
  } = useAppStore((state) => ({
    signIn: state.signIn,
    signOut: state.signOut,
    fetchProfile: state.fetchProfile,
    setTheme: state.setTheme,
    setLanguage: state.setLanguage,
    translate: state.translate,
    fetchAppointmentRequests: state.fetchAppointmentRequests,
    fetchSubscription: state.fetchSubscription
  }))
  
  // Get state from individual stores for better performance
  const user = useUserStore((state) => state.user)
  const profile = useUserProfileStore((state) => state.profile)
  const theme = usePreferencesStore((state) => state.preferences.theme)
  const language = useLanguageStore((state) => state.language)
  
  // Example of initializing data
  useEffect(() => {
    if (user) {
      // Fetch user data
      fetchProfile(user.id)
      fetchSubscription(user.id)
      fetchAppointmentRequests('pending')
    }
  }, [user, fetchProfile, fetchSubscription, fetchAppointmentRequests])
  
  return (
    <div className="p-4 space-y-4">
      <h1 className="text-2xl font-bold">Combined Store Example</h1>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Authentication</h2>
        {user ? (
          <>
            <p>Signed in as: {user.email}</p>
            <button 
              className="px-4 py-2 bg-red-500 text-white rounded"
              onClick={() => signOut()}
            >
              Sign Out
            </button>
          </>
        ) : (
          <div className="space-y-2">
            <p>Not signed in</p>
            <button 
              className="px-4 py-2 bg-green-500 text-white rounded"
              onClick={() => signIn('<EMAIL>', 'password')}
            >
              Demo Sign In
            </button>
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Profile</h2>
        {profile ? (
          <p>Name: {profile.firstName} {profile.lastName}</p>
        ) : (
          <p>No profile data</p>
        )}
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Preferences</h2>
        <div className="flex space-x-2">
          <button 
            className={`px-4 py-2 rounded ${theme === 'light' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setTheme('light')}
          >
            Light
          </button>
          <button 
            className={`px-4 py-2 rounded ${theme === 'dark' ? 'bg-blue-500 text-white' : 'bg-gray-800 text-white'}`}
            onClick={() => setTheme('dark')}
          >
            Dark
          </button>
        </div>
      </div>
      
      <div className="space-y-2">
        <h2 className="text-xl font-semibold">Language</h2>
        <div className="flex space-x-2">
          <button 
            className={`px-4 py-2 rounded ${language === 'fr' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setLanguage('fr')}
          >
            Français
          </button>
          <button 
            className={`px-4 py-2 rounded ${language === 'en' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            onClick={() => setLanguage('en')}
          >
            English
          </button>
        </div>
        <p>Translated greeting: {translate('common.hello')}</p>
      </div>
    </div>
  )
}

// Example 3: App initialization
export function AppInitializationExample({ children }: { children: React.ReactNode }) {
  const { initializeApp, isLoading } = useInitializeApp()
  
  useEffect(() => {
    initializeApp()
  }, [initializeApp])
  
  if (isLoading) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">Loading application...</p>
        </div>
      </div>
    )
  }
  
  return <>{children}</>
}
