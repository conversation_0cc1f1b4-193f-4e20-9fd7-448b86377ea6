"use client"

import { useState, useEffect } from "react"
import { CheckCircle, Clock, Info, X, AlertTriangle } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useTranslation } from "@/components/t"
import { useCombinedStore } from "@/stores/useAppStore"
import { PaginationMeta } from "@/stores/useAppointmentHistoryStore"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useAppointmentHistoryStore } from "@/stores/useAppointmentHistoryStore"

// Pagination component for appointment history
function AppointmentPagination({ type, pagination }: { type: 'requests' | 'completed' | 'cancelled', pagination: PaginationMeta }) {
  const { goToPage, fetchNextPage, fetchPreviousPage } = useCombinedStore();
  const { currentPage, totalPages, hasMore } = pagination;

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // Show all pages if there are fewer than maxPagesToShow
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate start and end of page range around current page
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);

      // Adjust if we're near the beginning or end
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, 4);
      } else if (currentPage >= totalPages - 1) {
        start = Math.max(2, totalPages - 3);
      }

      // Add ellipsis if needed before middle pages
      if (start > 2) {
        pages.push('ellipsis');
      }

      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      // Add ellipsis if needed after middle pages
      if (end < totalPages - 1) {
        pages.push('ellipsis');
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }

    return pages;
  };

  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;

  return (
    <Pagination className="mt-4">
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            onClick={() => fetchPreviousPage(type)}
            className={currentPage <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
          />
        </PaginationItem>

        {getPageNumbers().map((page, index) => (
          page === 'ellipsis' ? (
            <PaginationItem key={`ellipsis-${index}`}>
              <PaginationEllipsis />
            </PaginationItem>
          ) : (
            <PaginationItem key={page}>
              <PaginationLink
                isActive={page === currentPage}
                onClick={() => goToPage(type, page as number)}
              >
                {page}
              </PaginationLink>
            </PaginationItem>
          )
        ))}

        <PaginationItem>
          <PaginationNext
            onClick={() => fetchNextPage(type)}
            className={!hasMore ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}

export default function MesRendezVousPage() {
  const [activeTab, setActiveTab] = useState("tous")
  const [openDialogId, setOpenDialogId] = useState<string | null>(null)
  const { t, language } = useTranslation()

  // Get data and actions from the combined store
  const {
    appointmentRequests,
    completedAppointments,
    cancelledAppointments,
    requestsPagination,
    completedPagination,
    cancelledPagination,
    fetchAppointmentRequests,
    fetchCompletedAppointments,
    fetchCancelledAppointments,
    fetchAllAppointmentHistory,
    cancelAppointmentRequest,
    isAppointmentHistoryLoading,
    fetchNextPage,
    fetchPreviousPage,
    goToPage
  } = useCombinedStore()

  // Get appointment error directly from appointment store
  const appointmentError = useAppointmentHistoryStore((state) => state.error)

  // Use appointment-specific loading state
  const isLoading = isAppointmentHistoryLoading

  // Fetch appointment history when component mounts
  useEffect(() => {
    console.log('MesRendezVous: Fetching appointment history...')
    console.log('MesRendezVous: Current state - isLoading:', isLoading, 'appointmentRequests:', appointmentRequests.length, 'completedAppointments:', completedAppointments.length, 'cancelledAppointments:', cancelledAppointments.length)
    fetchAllAppointmentHistory().then(() => {
      console.log('MesRendezVous: Appointment history fetched successfully')
      console.log('MesRendezVous: After fetch - appointmentRequests:', appointmentRequests.length, 'completedAppointments:', completedAppointments.length, 'cancelledAppointments:', cancelledAppointments.length)
    }).catch((error) => {
      console.error('MesRendezVous: Error fetching appointment history:', error)
    })
  }, [fetchAllAppointmentHistory])

  // Get the current pagination based on active tab
  const getCurrentPagination = () => {
    if (activeTab === "en-cours") return requestsPagination
    if (activeTab === "completes") return completedPagination
    if (activeTab === "annules") return cancelledPagination
    return requestsPagination // Default to requests pagination
  }

  // Get the current appointments based on active tab
  const getCurrentAppointments = () => {
    if (activeTab === "tous") {
      // Combine all appointments with proper null filtering
      const pending = appointmentRequests.filter(r => r != null)
      const completed = completedAppointments.map(ca => ca.appointment_request).filter(appointment => appointment != null)
      const cancelled = cancelledAppointments.map(ca => ca.appointment_request).filter(appointment => appointment != null)
      return [...pending, ...completed, ...cancelled]
    }
    if (activeTab === "en-cours") return appointmentRequests.filter(r => r != null)
    if (activeTab === "completes") return completedAppointments.map(ca => ca.appointment_request).filter(appointment => appointment != null)
    if (activeTab === "annules") return cancelledAppointments.map(ca => ca.appointment_request).filter(appointment => appointment != null)
    return []
  }

  // Get the current pagination type based on active tab
  const getCurrentPaginationType = (): 'requests' | 'completed' | 'cancelled' => {
    if (activeTab === "en-cours") return 'requests'
    if (activeTab === "completes") return 'completed'
    if (activeTab === "annules") return 'cancelled'
    return 'requests' // Default to requests
  }

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    // Reset to first page when changing tabs
    if (value === "en-cours") fetchAppointmentRequests({ page: 1 })
    else if (value === "completes") fetchCompletedAppointments({ page: 1 })
    else if (value === "annules") fetchCancelledAppointments({ page: 1 })
    else fetchAllAppointmentHistory()
  }

  // Cancel appointment request
  const handleCancelRequest = async (id: string | undefined) => {
    if (!id) {
      console.error('Cannot cancel appointment: ID is undefined')
      return
    }
    
    try {
      const result = await cancelAppointmentRequest(id)
      if (result.success) {
        // Refresh the appointment list
        fetchAllAppointmentHistory()
        setOpenDialogId(null)
      } else {
        console.error('Failed to cancel appointment:', result.error)
      }
    } catch (error) {
      console.error('Error cancelling appointment:', error)
    }
  }

  // Debug logging for render state
  console.log('MesRendezVous render - isLoading:', isLoading, 'error:', appointmentError, 'appointments count:', getCurrentAppointments().length)

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            <T keyName={translationKeys.appointments.title} />
          </h1>
          <p className="text-muted-foreground mt-1.5">
            <T keyName={translationKeys.appointments.description} />
          </p>
        </div>

        <Card>
          <CardHeader className="pb-3 flex flex-col gap-4">
            <CardTitle>
              <T keyName={translationKeys.appointments.requestsTitle} />
            </CardTitle>
            <Tabs defaultValue="tous" value={activeTab} onValueChange={handleTabChange} className="w-full">
              <TabsList className="w-full sm:w-auto">
                <TabsTrigger value="tous" className="flex-1 sm:flex-initial">
                  <T keyName={translationKeys.appointments.all} />
                </TabsTrigger>
                <TabsTrigger value="en-cours" className="flex-1 sm:flex-initial">
                  <T keyName={translationKeys.appointments.inProgress} />
                </TabsTrigger>
                <TabsTrigger value="completes" className="flex-1 sm:flex-initial">
                  <T keyName={translationKeys.appointments.completed} />
                </TabsTrigger>
                <TabsTrigger value="annules" className="flex-1 sm:flex-initial">
                  {language === 'fr' ? 'Annulés' : 'Cancelled'}
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <LoadingSpinner />
              </div>
            ) : appointmentError ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertTriangle className="h-12 w-12 text-destructive opacity-80 mb-3" />
                <h3 className="font-medium text-lg">
                  <T keyName={translationKeys.common.error} />
                </h3>
                <p className="text-muted-foreground mt-1">
                  {appointmentError.message || "Une erreur s'est produite lors du chargement des rendez-vous."}
                </p>
                <Button onClick={() => fetchAllAppointmentHistory()} className="mt-4">
                  Réessayer
                </Button>
              </div>
            ) : getCurrentAppointments().length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <Info className="h-12 w-12 text-muted-foreground opacity-20 mb-3" />
                <h3 className="font-medium text-lg">
                  <T keyName={translationKeys.appointments.noRequests} />
                </h3>
                <p className="text-muted-foreground mt-1">
                  {activeTab === "en-cours" ? (
                    <T keyName={translationKeys.appointments.noRequestsInProgress} />
                  ) : activeTab === "completes" ? (
                    <T keyName={translationKeys.appointments.noRequestsCompleted} />
                  ) : activeTab === "annules" ? (
                    t(translationKeys.appointments.noRequestsCancelled) || (language === 'fr' ? 'Vous n\'avez aucune demande de rendez-vous annulée' : 'You have no cancelled appointment requests')
                  ) : (
                    <T keyName={translationKeys.appointments.noRequests} />
                  )}
                </p>
              </div>
            ) : (
              <>
                <div className="space-y-4">
                  {getCurrentAppointments().map((appointment, index) => (
                    <div
                      key={appointment.id || `appointment-${index}`}
                      className="flex flex-col sm:flex-row sm:items-center justify-between p-4 rounded-lg border bg-card text-card-foreground"
                    >
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium">
                            {appointment?.family_members?.first_name || appointment?.patient_first_name || 'Rendez-vous'}
                          </h3>
                          <Badge
                            variant={appointment?.status === "completed" ? "outline" :
                                   appointment?.status === "cancelled" ? "destructive" : "secondary"}
                            className="ml-2"
                          >
                            {appointment?.status === "completed" ? (
                              <div className="flex items-center">
                                <CheckCircle className="h-3.5 w-3.5 mr-1 text-green-600" />
                                <span>
                                  <T keyName={translationKeys.appointments.done} />
                                </span>
                              </div>
                            ) : appointment?.status === "cancelled" ? (
                              <div className="flex items-center">
                                <X className="h-3.5 w-3.5 mr-1" />
                                <span>
                                  Annulé
                                </span>
                              </div>
                            ) : (
                              <div className="flex items-center">
                                <Clock className="h-3.5 w-3.5 mr-1" />
                                <span>
                                  <T keyName={translationKeys.appointments.pending} />
                                </span>
                              </div>
                            )}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Code postal du rendez-vous: {appointment?.request_details?.postalCode || appointment?.patient_postal_code || 'Non spécifié'}
                        </div>
                        <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-6 text-xs text-muted-foreground">
                          <span>
                            <T keyName={translationKeys.appointments.sentOn} />: {appointment?.created_at ? format(new Date(appointment.created_at), "d MMMM yyyy 'à' HH:mm", { locale: fr }) : 'Date inconnue'}
                          </span>
                        </div>
                      </div>

                      {appointment?.status === "pending" && (
                        <div className="mt-3 sm:mt-0">
                          <Dialog open={openDialogId === appointment.id} onOpenChange={(open) => setOpenDialogId(open ? appointment.id : null)}>
                            <DialogTrigger asChild>
                              <Button 
                                variant="outline" 
                                size="sm"
                                className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-500 dark:hover:bg-red-950 dark:border-red-400 dark:text-red-400 dark:hover:text-red-400"
                              >
                                <X className="h-4 w-4 mr-1" />
                                <T keyName={translationKeys.appointments.cancelAppointment} />
                              </Button>
                            </DialogTrigger>
                            <DialogContent className="w-[90%] mx-auto sm:max-w-md p-6 pt-10 rounded-md">
                              <DialogHeader className="space-y-3">
                                <DialogTitle className="text-xl font-bold text-center">
                                  <T keyName={translationKeys.appointments.cancelConfirmation} />
                                </DialogTitle>
                                <DialogDescription className="text-base text-left">
                                  <T keyName={translationKeys.appointments.cancelConfirmationText} />
                                </DialogDescription>
                              </DialogHeader>
                              <div className="flex flex-col sm:flex-row gap-3 w-full pt-6">
                                <Button
                                  variant="outline"
                                  className="w-full order-1 sm:order-none"
                                  onClick={() => setOpenDialogId(null)}
                                >
                                  <T keyName={translationKeys.appointments.noContinue} />
                                </Button>
                                <Button
                                  variant="destructive"
                                  className="w-full order-0 sm:order-none"
                                  onClick={() => handleCancelRequest(appointment.id)}
                                >
                                  <T keyName={translationKeys.appointments.yesCancel} />
                                </Button>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Pagination */}
                {activeTab !== "tous" && (
                  <AppointmentPagination
                    type={getCurrentPaginationType()}
                    pagination={getCurrentPagination()}
                  />
                )}
              </>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
