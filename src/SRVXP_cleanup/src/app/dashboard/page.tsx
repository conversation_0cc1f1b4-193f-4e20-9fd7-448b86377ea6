"use client";

import Link from "next/link"
import {
  Users,
  User,
  ClipboardList,
  Search,
  PlusCircle,
  Calendar,
  TrendingUp,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle
} from "lucide-react"

import { DynamicDashboardLayout } from "@/components/dynamic/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { T, useTranslation } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { Suspense, useEffect } from "react"
import { useLanguage } from "@/lib/LanguageContext"
import { useUserProfile } from "@/hooks/use-user-profile"
import { useCombinedStore } from "@/stores/useAppStore"
import { FullPageLoading, LoadingSpinner } from '@/components/ui/loading-spinner'

interface DashboardStatsProps {
  loading?: boolean
  totalAppointments: number
  upcomingAppointments: number
  completedAppointments: number
  cancelledAppointments: number
}

function DashboardStats({ 
  loading = false,
  totalAppointments = 0, 
  upcomingAppointments = 0, 
  completedAppointments = 0,
  cancelledAppointments = 0 
}: DashboardStatsProps) {
  const { language } = useTranslation()

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 w-20 bg-gray-200 rounded animate-pulse"></div>
              </CardTitle>
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
            </CardHeader>
            <CardContent>
              <div className="h-7 w-16 bg-gray-200 rounded animate-pulse mb-1"></div>
              <div className="h-3 w-24 bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const stats = [
    {
      title: language === 'fr' ? 'Total' : 'Total',
      value: totalAppointments,
      description: language === 'fr' ? 'Tous les rendez-vous' : 'All appointments',
      icon: Calendar,
      color: "text-blue-600"
    },
    {
      title: <T keyName={translationKeys.appointments.inProgress} />,
      value: upcomingAppointments,
      description: language === 'fr' ? 'En cours' : 'In progress',
      icon: Clock,
      color: "text-orange-600"
    },
    {
      title: <T keyName={translationKeys.appointments.completed} />,
      value: completedAppointments,
      description: language === 'fr' ? 'Terminés' : 'Completed',
      icon: CheckCircle,
      color: "text-green-600"
    },
    {
      title: language === 'fr' ? 'Annulés' : 'Cancelled',
      value: cancelledAppointments,
      description: language === 'fr' ? 'Annulés' : 'Cancelled',
      icon: AlertTriangle,
      color: "text-red-600"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

function PageContent() {
  // Get data from the combined store
  const {
    profile,
    subscription,
    pendingAppointments,
    completedAppointments,
    cancelledAppointments,
    fetchAllAppointmentHistory,
    getAppointmentSummary,
    isLoading
  } = useCombinedStore();

  // Fallback to useUserProfile for backward compatibility
  const { firstName, fullName } = useUserProfile();
  const { } = useTranslation(); // t is used via the T component

  // Fetch appointment history when component mounts
  useEffect(() => {
    fetchAllAppointmentHistory();
  }, [fetchAllAppointmentHistory]);

  // Get appointment summary
  const appointmentSummary = getAppointmentSummary();

  // Get current language and translation function
  const { t, language } = useTranslation();

  // Determine subscription plan type and status
  const planType = subscription?.plan_type || 'individual';
  const isActive = subscription?.status === 'active';

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">
          <T
            keyName={translationKeys.home.greeting}
            params={{ firstName: firstName }}
          />
        </h1>
        <p className="text-muted-foreground">
          <T keyName={translationKeys.home.welcome} />
        </p>
      </div>

      {/* Account info card */}
      <Card>
        <CardHeader className="py-4 flex flex-row items-center justify-between">
          <div>
            <CardTitle>
              <T keyName={translationKeys.account.yourAccount} />
            </CardTitle>
          </div>
          <Badge className={isActive ? "bg-green-100 text-green-800 hover:bg-green-100" : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"}>
            <T keyName={isActive ? translationKeys.common.active : translationKeys.common.inactive} />
          </Badge>
        </CardHeader>
        <CardContent className="pb-6">
          <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="h-5 w-5 text-blue-700" />
              </div>
              <div>
                <h3 className="font-medium">{profile?.firstName ? `${profile.firstName} ${profile.lastName}` : fullName}</h3>
                <p className="text-sm text-muted-foreground">
                  <T keyName={planType === 'family' ? translationKeys.account.familyPlan : translationKeys.account.individualPlan} />
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick access cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        <Card className="hover:shadow-md transition-shadow flex flex-col">
          <CardHeader className="p-4">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-5">
                <Search className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-base break-words hyphens-auto">
                <T keyName={translationKeys.home.findAppointmentTitle} />
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 flex-1 flex flex-col">
            <CardDescription className="mb-3 flex-1">
              <T keyName={translationKeys.home.findAppointmentDesc} />
            </CardDescription>
            <Button asChild className="w-full mt-auto">
              <Link href="/trouver-rendez-vous">
                <T keyName={translationKeys.common.search} />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow flex flex-col">
          <CardHeader className="p-4">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-5">
                <ClipboardList className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-base break-words hyphens-auto">
                <T keyName={translationKeys.nav.appointments} />
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 flex-1 flex flex-col">
            <CardDescription className="mb-3 flex-1">
              <T keyName={translationKeys.home.manageAppointmentsDesc} />
            </CardDescription>
            <Button asChild className="w-full mt-auto" variant="default">
              <Link href="/mes-rendez-vous">
                <T keyName={translationKeys.home.viewRequests} />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow flex flex-col">
          <CardHeader className="p-4">
            <div className="flex items-center gap-2">
              <div className="flex-shrink-0 w-5">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-base break-words hyphens-auto">
                {planType === 'family' ? (
                  <T keyName={translationKeys.account.manageUsers} />
                ) : (
                  <T keyName={translationKeys.home.manageProfileTitle} />
                )}
              </CardTitle>
            </div>
          </CardHeader>
          <CardContent className="p-4 pt-0 flex-1 flex flex-col">
            <CardDescription className="mb-3 flex-1">
              {planType === 'family' ? (
                <T keyName={translationKeys.home.manageUsersDesc} />
              ) : (
                <T keyName={translationKeys.home.manageProfileDesc} />
              )}
            </CardDescription>
            <Button asChild className="w-full mt-auto" variant="default">
              <Link href="/compte/utilisateurs">
                {planType === 'family' ? (
                  <T keyName={translationKeys.account.manageUsers} />
                ) : (
                  <T keyName={translationKeys.home.manageProfileButton} />
                )}
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Appointment Summary Card */}
      <Card className="col-span-3">
        <CardHeader>
          <CardTitle>
            {language === 'fr' ? 'Résumé' : 'Summary'}
          </CardTitle>
        </CardHeader>
        <CardContent className="pb-6">
          {isLoading ? (
            <div className="flex justify-center py-4">
              <LoadingSpinner />
            </div>
          ) : (
            <div className="grid gap-4 grid-cols-1 md:grid-cols-3">
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <T keyName={translationKeys.appointments.inProgress} />
                </p>
                <p className="text-2xl font-bold">{appointmentSummary.counts.in_progress}</p>
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  <T keyName={translationKeys.appointments.completed} />
                </p>
                <p className="text-2xl font-bold">{appointmentSummary.counts.completed}</p>
              </div>
              <div className="bg-red-50 p-3 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  {language === 'fr' ? 'Annulés' : 'Cancelled'}
                </p>
                <p className="text-2xl font-bold">{appointmentSummary.counts.cancelled}</p>
              </div>
            </div>
          )}
          <div className="mt-4 flex justify-end">
            <Button asChild variant="outline" size="sm">
              <Link href="/mes-rendez-vous">
                {t(translationKeys.appointments.viewAll) || (language === 'fr' ? 'Voir tous les rendez-vous' : 'View all appointments')}
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function DashboardPage() {
  // Get the initialization function from the combined store
  const { initializeApp, isLoading } = useCombinedStore();

  // Initialize the app when the component mounts
  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  return (
    <DynamicDashboardLayout>
      {isLoading && <FullPageLoading />}
      <Suspense fallback={
        <div className="flex h-full w-full items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      }>
        <PageContent />
      </Suspense>
    </DynamicDashboardLayout>
  )
}
