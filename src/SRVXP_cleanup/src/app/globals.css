@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 217 83% 48%; /* Updated from 221.2 83.2% 53.3% to match #144ee0 */
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96.1%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96.1%;
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 217 83% 48%; /* Updated to match primary color */
  --radius: 0.5rem;
  --navy-blue: 230 20% 20%;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply text-[#212244] font-bold;
  }
}

/* Button styling */
.btn-primary {
  @apply bg-brandBlue text-white hover:bg-brandBlue/90 transition-colors duration-300;
}

/* Navbar styling */
.nav-link {
  @apply hover:text-brandBlue transition-colors duration-200;
}

/* Animation styling */
@layer utilities {
  .opacity-0 {
    opacity: 0;
  }

  .animate-fade-in-up-1,
  .animate-fade-in-up-2,
  .animate-fade-in-up-3,
  .animate-fade-in-up-4,
  .animate-fade-in-together,
  .animate-price-fade,
  .animate-arrow-bounce {
    opacity: 0;
    animation-fill-mode: forwards;
  }

  /* Price animation */
  .animate-price-fade {
    animation: priceFade 0.4s ease-in-out forwards;
  }

  @keyframes priceFade {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Arrow bounce animation */
  .arrow-icon {
    transition: transform 0.3s ease;
  }

  .group:hover .arrow-icon {
    transform: translateX(4px);
    animation: arrowBounce 1s ease-in-out infinite;
  }

  @keyframes arrowBounce {
    0%, 100% {
      transform: translateX(0);
    }
    50% {
      transform: translateX(4px);
    }
  }
}
