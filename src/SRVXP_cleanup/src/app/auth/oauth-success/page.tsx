"use client"

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/lib/AuthContext'
import { FullPageLoading } from '@/components/ui/loading-spinner'

export default function OAuthSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { status, refresh } = useAuth()
  const [attempts, setAttempts] = useState(0)
  const [isChecking, setIsChecking] = useState(true)

  // Get the intended redirect destination
  const redirectTo = searchParams.get('redirectTo') || '/dashboard'

  useEffect(() => {
    let timeoutId: NodeJS.Timeout
    let intervalId: NodeJS.Timeout

    const checkAuthStatus = async () => {
      console.log(`OAuth Success: Checking auth status (attempt ${attempts + 1})`, { status })

      if (status === 'authenticated') {
        console.log('OAuth Success: User is authenticated, redirecting to:', redirectTo)
        setIsChecking(false)
        
        // Small delay to ensure everything is settled
        setTimeout(() => {
          window.location.href = redirectTo
        }, 500)
        return
      }

      if (status === 'loading') {
        console.log('OAuth Success: Auth still loading, waiting...')
        return
      }

      if (status === 'unauthenticated') {
        console.log('OAuth Success: Not authenticated, refreshing auth state...')
        try {
          await refresh()
          setAttempts(prev => prev + 1)
        } catch (error) {
          console.error('OAuth Success: Error refreshing auth:', error)
        }
      }
    }

    // Initial check
    checkAuthStatus()

    // Set up polling to check auth status
    intervalId = setInterval(checkAuthStatus, 1000)

    // Timeout after 10 seconds - redirect to sign-in if still not authenticated
    timeoutId = setTimeout(() => {
      if (status !== 'authenticated') {
        console.log('OAuth Success: Timeout reached, redirecting to sign-in')
        setIsChecking(false)
        router.push('/auth/sign-in?error=oauth_timeout')
      }
    }, 10000)

    return () => {
      if (timeoutId) clearTimeout(timeoutId)
      if (intervalId) clearInterval(intervalId)
    }
  }, [status, refresh, redirectTo, router, attempts])

  // Force a refresh on mount to ensure we get the latest auth state
  useEffect(() => {
    const forceRefresh = async () => {
      try {
        await refresh()
      } catch (error) {
        console.error('OAuth Success: Error on initial refresh:', error)
      }
    }
    forceRefresh()
  }, [refresh])

  if (!isChecking) {
    return null // Don't render anything while redirecting
  }

  return <FullPageLoading />
}
