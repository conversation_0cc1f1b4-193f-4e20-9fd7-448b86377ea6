import { createClient, createAdminClient } from '@/lib/supabase/server'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

/**
 * Updates the user's profile data in Supabase after Google Auth
 */
async function updateUserProfileFromGoogleAuth(supabase: any, userId: string) {
  try {
    // Get the admin client for privileged operations
    const adminClient = await createAdminClient()

    // Get the user's metadata from auth.users
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserById(userId)

    if (userError || !userData) {
      console.error('Error fetching user data:', userError)
      return
    }

    const metadata = userData.user.user_metadata

    // Check if this is a Google Auth user
    const isGoogleAuth = metadata?.provider_id ||
                         metadata?.iss === 'https://accounts.google.com' ||
                         metadata?.sub?.startsWith('google')

    if (!isGoogleAuth) {
      console.log('Not a Google Auth user, skipping profile update')
      return
    }

    console.log('Processing Google Auth user profile update')

    // Extract name information from Google metadata
    // Google provides different fields depending on the authentication flow
    const firstName = metadata.given_name || metadata.first_name || metadata.name?.split(' ')[0] || ''
    const lastName = metadata.family_name || metadata.last_name || (metadata.name?.split(' ').slice(1).join(' ') || '')

    // Get the language from the cookie or default to French
    const cookieStore = await cookies()
    const languageCookie = cookieStore.get('NEXT_LOCALE')
    const language = languageCookie?.value || 'fr'

    console.log(`Extracted user info - First: "${firstName}", Last: "${lastName}", Language: "${language}"`)

    // Update the auth.users metadata to ensure consistency
    const { error: updateAuthError } = await adminClient.auth.admin.updateUserById(
      userId,
      {
        user_metadata: {
          ...metadata,
          first_name: firstName,
          last_name: lastName,
          language: language
        }
      }
    )

    if (updateAuthError) {
      console.error('Error updating auth user metadata:', updateAuthError)
    }

    // The database trigger should handle updating the users table,
    // but we'll do it explicitly as well to ensure it happens
    const { error: updateUserError } = await adminClient
      .from('users')
      .upsert({
        id: userId,
        user_id: userId,
        email: userData.user.email || '',
        first_name: firstName,
        last_name: lastName,
        language: language,
        avatar_url: metadata.avatar_url || metadata.picture || '',
        token_identifier: userData.user.email || '',
        created_at: userData.user.created_at,
        updated_at: new Date().toISOString()
      }, { onConflict: 'id' })

    if (updateUserError) {
      console.error('Error updating user profile:', updateUserError)
    } else {
      console.log('Successfully updated user profile with Google Auth data')
    }

  } catch (error) {
    console.error('Error in updateUserProfileFromGoogleAuth:', error)
  }
}

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')
  const redirectTo = requestUrl.searchParams.get('redirectTo') || '/dashboard'

  // Force localhost origin to match OAuth configuration
  const origin = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002'

  console.log(`OAuth callback - Code: ${!!code}, RedirectTo: ${redirectTo}, Origin: ${origin}`)

  if (code) {
    try {
      // Create the Supabase client
      const supabase = await createClient()

      // Exchange the code for a session
      const { data, error } = await supabase.auth.exchangeCodeForSession(code)

      if (error) {
        console.error('Error exchanging code for session:', error)
        // Redirect to sign-in page with error
        const errorUrl = `${origin}/auth/sign-in?error=oauth_callback_failed`
        return NextResponse.redirect(errorUrl)
      }

      console.log('Successfully exchanged code for session')

      // If this is a successful authentication, update the user profile
      if (data?.session?.user?.id) {
        await updateUserProfileFromGoogleAuth(supabase, data.session.user.id)
      }

      // Redirect to intermediate OAuth success page to allow browser to establish session
      const oauthSuccessUrl = `${origin}/auth/oauth-success?redirectTo=${encodeURIComponent(redirectTo)}`
      console.log(`Redirecting OAuth user to success page: ${oauthSuccessUrl}`)

      const response = NextResponse.redirect(oauthSuccessUrl)

      // Set proper cookies for OAuth session persistence
      const cookieOptions = {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax' as const,
        maxAge: 60 * 60 * 24 * 30, // 30 days
        path: '/',
        // Don't set domain for localhost to avoid cross-domain issues
        domain: process.env.NODE_ENV === 'production' ? undefined : undefined,
      }

      // Clear any logout flags and set OAuth success flag
      response.cookies.set('oauth_success', 'true', { ...cookieOptions, maxAge: 60 })
      response.cookies.delete('isLoggedOut')

      return response

    } catch (error) {
      console.error('Error in auth callback:', error)
      // Redirect to sign-in page with error
      const errorUrl = `${origin}/auth/sign-in?error=oauth_callback_error`
      return NextResponse.redirect(errorUrl)
    }
  }

  // No code provided - redirect to sign-in
  console.log('No OAuth code provided, redirecting to sign-in')
  const signInUrl = `${origin}/auth/sign-in`
  return NextResponse.redirect(signInUrl)
}
