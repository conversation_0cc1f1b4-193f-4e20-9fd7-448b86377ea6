"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { DynamicDashboardLayout } from "@/components/dynamic/dashboard-layout";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";
import { Suspense } from "react";
import { useAuth } from "@/lib/AuthContext";
import { useLanguage } from "@/lib/LanguageContext";
import { useSubscriptionStore, useSubscriptionDetails } from "@/stores/useSubscriptionStore";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

// Pricing configuration based on plan and billing period
const PRICING_CONFIG = {
  individual: {
    monthly: 7.95,
    annual: 71.40
  },
  family: {
    monthly: 14.95,
    annual: 134.40
  }
} as const;

function SubscriptionSuccessContent() {
  const { user } = useAuth();
  const { translate } = useLanguage();
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const canceled = searchParams.get('canceled');

  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use Zustand store for subscription details
  const fetchSubscription = useSubscriptionStore((state) => state.fetchSubscription);
  const subscriptionDetails = useSubscriptionDetails();

  // Helper function to get the correct amount based on plan and billing period
  const getCorrectAmount = (planType: string, billingPeriod: string) => {
    const plan = planType as keyof typeof PRICING_CONFIG;
    const period = billingPeriod as keyof typeof PRICING_CONFIG.individual;
    
    if (plan in PRICING_CONFIG && period in PRICING_CONFIG[plan]) {
      return PRICING_CONFIG[plan][period];
    }
    
    // Fallback to the stored amount if configuration is missing
    return subscriptionDetails.amount || 0;
  };

  // Helper function to calculate period dates
  const calculatePeriodDates = (startDate: Date, billingPeriod: string) => {
    const start = new Date(startDate);
    const end = new Date(startDate);
    
    if (billingPeriod === 'annual') {
      end.setFullYear(end.getFullYear() + 1);
    } else {
      end.setMonth(end.getMonth() + 1);
    }
    
    return { start, end };
  };

  // Helper function to format billing period for display
  const formatBillingPeriod = (billingPeriod: string) => {
    return billingPeriod === 'monthly' ? 'Monthly' : 'Annual';
  };

  // Verify subscription when component mounts
  useEffect(() => {
    async function verifySubscription() {
      // Handle canceled checkout
      if (canceled === 'true') {
        setError(translate(translationKeys.subscription.canceledCheckout));
        setIsLoading(false);
        return;
      }

      if (!sessionId) {
        setError(translate(translationKeys.subscription.noSessionId));
        setIsLoading(false);
        return;
      }

      if (!user) {
        setError(translate(translationKeys.subscription.notLoggedIn));
        setIsLoading(false);
        return;
      }

      try {
        // Fetch subscription data using Zustand store
        const subscription = await fetchSubscription(user.id);
        
        if (subscription && subscription.status === 'active') {
          setIsSuccess(true);
        } else {
          setError(translate(translationKeys.subscription.processingSubscription));
        }
      } catch (err) {
        console.error('Error verifying subscription:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    }

    verifySubscription();
  }, [sessionId, user, fetchSubscription, translate]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-center text-lg"><T keyName={translationKeys.common.loading} /></p>
      </div>
    );
  }

  // Success state
  if (isSuccess && subscriptionDetails.hasSubscription && subscriptionDetails.planType && subscriptionDetails.billingPeriod) {
    // Get the correct amount based on plan type and billing period
    const correctAmount = getCorrectAmount(subscriptionDetails.planType, subscriptionDetails.billingPeriod);
    
    // Calculate the correct period dates
    const periodDates = subscriptionDetails.currentPeriodStart 
      ? calculatePeriodDates(subscriptionDetails.currentPeriodStart, subscriptionDetails.billingPeriod)
      : { start: new Date(), end: new Date() };

    // Format the billing period for display
    const displayBillingPeriod = formatBillingPeriod(subscriptionDetails.billingPeriod);

    // Format the currency
    const formatCurrency = (amount: number, currency: string = 'CAD') => {
      return new Intl.NumberFormat('en-CA', {
        style: 'currency',
        currency: currency.toUpperCase(),
      }).format(amount);
    };

    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="rounded-full bg-green-100 p-3">
          <svg
            className="h-8 w-8 text-green-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        </div>
        <h2 className="mt-4 text-center text-2xl font-bold"><T keyName={translationKeys.subscription.success} /></h2>
        <p className="mt-2 text-center text-gray-600">
          <T keyName={translationKeys.subscription.successMessage} />
        </p>

        {/* Subscription details */}
        <div className="mt-8 w-full max-w-md rounded-lg border p-6">
          <h3 className="mb-4 text-lg font-medium"><T keyName={translationKeys.subscription.details} /></h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-500"><T keyName={translationKeys.subscription.plan} />:</span>
              <span className="font-medium">{subscriptionDetails.planType.charAt(0).toUpperCase() + subscriptionDetails.planType.slice(1)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500"><T keyName={translationKeys.subscription.billing} />:</span>
              <span className="font-medium">{displayBillingPeriod}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500"><T keyName={translationKeys.subscription.amount} />:</span>
              <span className="font-medium">{formatCurrency(correctAmount, subscriptionDetails.currency || 'CAD')}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500"><T keyName={translationKeys.subscription.currentPeriod} />:</span>
              <span className="font-medium">
                {periodDates.start.toLocaleDateString()} - {periodDates.end.toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Next steps */}
        <div className="mt-8 text-center">
          <p className="text-gray-600"><T keyName={translationKeys.subscription.nextSteps} /></p>
          <div className="mt-4 flex flex-wrap justify-center gap-4">
            <a
              href="/dashboard"
              className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
            >
              <T keyName={translationKeys.subscription.goToDashboard} />
            </a>
            <a
              href="/compte"
              className="rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-50"
            >
              <T keyName={translationKeys.subscription.manageAccount} />
            </a>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <div className="rounded-full bg-red-100 p-3">
        <svg
          className="h-8 w-8 text-red-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </div>
      <h2 className="mt-4 text-center text-2xl font-bold"><T keyName={translationKeys.subscription.error} /></h2>
      <p className="mt-2 text-center text-gray-600">
        {error || translate(translationKeys.subscription.errorMessage)}
      </p>

      {/* Help options */}
      <div className="mt-8 text-center">
        <p className="text-gray-600"><T keyName={translationKeys.subscription.needHelp} /></p>
        <div className="mt-4 flex flex-wrap justify-center gap-4">
          <a
            href="/pricing"
            className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
          >
            <T keyName={translationKeys.subscription.returnToPlans} />
          </a>
          <a
            href="/aide"
            className="rounded-md border border-gray-300 px-4 py-2 hover:bg-gray-50"
          >
            <T keyName={translationKeys.subscription.contactSupport} />
          </a>
        </div>
      </div>
    </div>
  );
}

export default function SubscriptionSuccessPage() {
  return (
    <DynamicDashboardLayout>
      <Suspense
        fallback={
          <div className="flex h-full w-full items-center justify-center">
            <LoadingSpinner size="lg" />
          </div>
        }
      >
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              <T keyName="subscription.status" />
            </h1>
            <p className="text-muted-foreground mt-1.5">
              <T keyName="subscription.verifyingPayment" />
            </p>
          </div>
          <SubscriptionSuccessContent />
        </div>
      </Suspense>
    </DynamicDashboardLayout>
  );
}
