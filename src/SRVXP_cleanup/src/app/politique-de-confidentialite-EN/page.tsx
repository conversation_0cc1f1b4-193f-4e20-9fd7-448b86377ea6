import { Navbar } from "@/components/zaply/layout/Navbar";
import { Footer } from "@/components/zaply/layout/Footer";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Privacy Policy | Sans rendez-vous express",
  description: "Privacy Policy for Sans rendez-vous express.",
};

export default function PrivacyPolicy() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow bg-white">
        <div className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-6 py-12">
          <div className="mb-4 text-gray-500">
            <p>Last updated on March 24, 2025</p>
          </div>

          <h1 className="text-4xl sm:text-5xl xl:text-[60px] leading-tight font-bold text-[#212244] mb-16 max-w-[1000px]">
            Privacy Policy
          </h1>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              General
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                By using our website www.sansrendezvousexpress.ca (hereinafter referred to as the "Platform"), you agree to the terms of this Privacy Policy (hereinafter referred to as the "Policy").
              </p>
              <p>
                The purpose of this Policy is to inform users of our Platform about the types of personal data we collect and how they are used.
              </p>
              <p>
                Sans rendez-vous express Inc. (hereinafter referred to as "SRVXP") strictly adheres to Quebec's Law 25 concerning the protection of personal data. SRVXP is a company legally registered in Quebec, Canada.
              </p>
              <p>
                This Policy also outlines our commitment and the measures we take to ensure the protection of your personal information.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Acceptance of the Policy
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                By accessing and using the Platform, you, the end user or the organization you represent (hereinafter referred to as "you", "the User" or "the Users"), agree to be bound by this Policy as well as our Terms and Conditions. If you do not accept this Policy, please do not use our Platform.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Collection of Personal Information
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                During your visit to the Platform, we automatically collect information about your device and your interaction with our site via Google Analytics.
              </p>
              <p>
                This includes the following data:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>IP Address</li>
                <li>Location</li>
                <li>Hardware and software details</li>
                <li>Content that the User views on our site</li>
                <li>Links that a User clicks on while visiting the site</li>
                <li>Non-Automatically Collected Personal Data</li>
              </ul>

              <p className="mt-6">
                Your personal data is collected through:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Contact form</li>
                <li>Medical appointment request form</li>
              </ul>

              <p className="mt-6">
                This includes the following data:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>First name</li>
                <li>Last name</li>
                <li>Quebec Health insurance number</li>
                <li>Sequential number of the Quebec Health Insurance Card</li>
                <li>Date of birth</li>
                <li>Gender</li>
                <li>Postal code near the appointment location</li>
                <li>Reason for the consultation</li>
                <li>Email</li>
                <li>Mobile phone</li>
                <li>Home phone</li>
                <li>Payment information</li>
              </ul>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Use of Cookies
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                The Platform uses navigation cookies as well as Facebook and Google pixels to offer you a personalized experience.
              </p>
              <p>
                Navigation cookies are text files that are transmitted to your browser when you access a website. Google Analytics cookies transmit anonymous browsing statistics. They allow, among other things, to know the number of visits to a page and the average time of these visits.
              </p>
              <p>
                Facebook and Google pixels are JavaScript code that allows for the detection of, among other things, the number of visits to a page, anonymously. The pixels are not placed on your computer; they remain on the visited site. They are used to collect statistics, analyze site performance, and conduct targeted advertising based on interests.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Data Usage
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                Please note that we only collect data necessary to achieve the objectives described in the Policy. We will not collect additional information without informing you in advance.
              </p>
              <p>
                We use this data to:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Find medical appointments</li>
                <li>Send promotional offers and targeted communications via our newsletter</li>
                <li>Conduct statistical analyses to improve the site</li>
                <li>Track requests for medical appointments</li>
                <li>Follow up on messages received through the contact form</li>
                <li>As part of a data security protocol</li>
              </ul>
              <p>
                User data may be accessed, processed, or collected in Canada.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Personal Data Retention Period
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                We store personal data for a duration of 7 days, after which it is permanently deleted. However, the first 8 characters of the Users' health insurance numbers are preserved for a period of 5 years to ensure that the free trial of the service is not used more than once.
              </p>
              <p>
                We will ensure that Users are notified if their data is retained longer than this period. You can contact us if you wish to make changes, delete, or anonymize your personal data.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Security Incident Management
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                In the event of a leak of your personal data, we commit to keeping you informed as well as notifying the Quebec's Access to Information Commission.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              User Rights
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                If you are a resident of Quebec, in accordance with Law 25, you have specific rights regarding your personal data:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Right to be informed</li>
                <li>Right of access</li>
                <li>Right to rectification</li>
                <li>Right to data portability</li>
              </ul>
              <p className="mt-6">
                You have the right to object to the use of your data and to withdraw it. To exercise these rights, contact our data protection officer:
              </p>
              <p className="mt-6 font-semibold">
                By mail:
              </p>
              <p>
                Sans rendez-vous express - Data Protection Officer<br />
                PO BOX 99900 VG 550 446<br />
                RPO VILLENEUVE<br />
                MONTREAL QC<br />
                H2T 0A6
              </p>
              <p className="mt-6 font-semibold">
                By email:
              </p>
              <p>
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Right of Access and Rectification
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                You can view, modify, or delete your data. To do so, use the same contact details mentioned above.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Consent and Withdrawal
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                It is possible to browse our site anonymously. However, access to certain features may require the collection of personal data. You always have the option not to provide this information.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Minors
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                Law 25 specifies that individuals under the age of 14 are considered minors for the purposes of data collection. Their personal information cannot be collected from them without the consent of the person holding parental authority or the guardian, except when the collection is clearly for the benefit of the minor.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              External Links
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                Our Platform may contain links to third-party websites. We have no control over the content of these sites and disclaim any responsibility for their content or any damages that may result from their use.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Priority to French
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                In the event of a conflict in the interpretation of the Policy compared with the French version, the French version shall prevail.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Updating the Policy
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                This Policy may be periodically updated to remain compliant with laws and to reflect changes in the management of our site as well as the expectations of our site's Users.
              </p>
              <p>
                We advise our Users to regularly review this Policy to stay informed of any updates. If necessary, we will notify Users of changes via email or we will post a notice on our site. If you continue to use the Platform, this will indicate that you accept the new Policy.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Interpretation Rules
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                The titles of the various sections of the Policy are provided solely for reference and clarity. They should not be considered when interpreting or applying the provisions contained in this Policy.
              </p>
              <p>
                In the Policy, words written in the singular also include their plural form when the context requires it, and vice versa. Similarly, words indicating a gender, male or female, encompass the other gender when necessary for a correct understanding of the text.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Divisibility
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                If, at any time, a clause in the Policy is declared non-compliant or invalid according to the applicable laws, this clause will then be considered null and void and will be excluded from the Policy. The other clauses will remain in force and will not be affected by this invalidity, and the rest of the Policy will continue to be considered valid.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Governing Law
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                This Policy is governed by the laws applicable in the province of Quebec, Canada. Any dispute will be subject to the exclusive jurisdiction of the courts of the city of Montreal.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#212244] mb-6">
              Contacting Us
            </h2>
            <div className="text-gray-700 space-y-6 max-w-[1000px]">
              <p>
                For any questions regarding this Policy, please contact us by email at <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline"><EMAIL></a>.
              </p>
            </div>
          </section>

        </div>
      </main>
      <Footer />
    </div>
  );
}
