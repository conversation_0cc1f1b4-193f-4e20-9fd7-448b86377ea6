import { Navbar } from "@/components/zaply/layout/Navbar";
import { Footer } from "@/components/zaply/layout/Footer";
import { HeroSection } from "@/components/zaply/sections/HeroSection";
import { FeaturesSection } from "@/components/zaply/sections/FeaturesSection";
import { HowItWorksSection } from "@/components/zaply/sections/HowItWorksSection";
import { PricingSection } from "@/components/zaply/sections/PricingSection";
import { FaqSection } from "@/components/zaply/sections/FaqSection";
import { CtaSection2 } from "@/components/zaply/sections/CtaSection2";
import { LanguageProvider } from "@/lib/LanguageContext";

export default function HomePage() {
  return (
    <LanguageProvider>
      <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        {/* Hero Section - Light Gray Background */}
        <div className="bg-[#f8f9fb]">
          <div className="container mx-auto px-4 sm:px-6">
            <HeroSection />
          </div>
        </div>

        {/* Features Section - White Background */}
        <div className="bg-white w-full">
          <div className="container mx-auto px-4 sm:px-6">
            <FeaturesSection />
          </div>
        </div>

        {/* How It Works Section - Changed to White Background */}
        <div className="bg-white w-full">
          <div className="container mx-auto px-4 sm:px-6">
            <HowItWorksSection />
          </div>
        </div>

        {/* Pricing Section - Changed to Light Gray Background */}
        <div className="bg-[#f8f9fb] w-full">
          <div className="container mx-auto px-4 sm:px-6">
            <PricingSection />
          </div>
        </div>

        {/* FAQ Section - Changed to White Background */}
        <div className="bg-white w-full">
          <div className="container mx-auto px-4 sm:px-6">
            <FaqSection />
          </div>
        </div>

        {/* CTA Section - Blue Background, Full Width */}
        <CtaSection2 />
      </main>
      <Footer />
    </div>
    </LanguageProvider>
  );
}
