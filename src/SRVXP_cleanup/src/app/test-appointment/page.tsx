"use client"

import { useState } from "react"
import { createAppointmentRequest } from "@/lib/appointment-requests/api"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DashboardLayout } from "@/components/layout/dashboard-layout"

export default function TestAppointmentPage() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string>('')

  const testSubmission = async () => {
    setIsSubmitting(true)
    setResult(null)
    setError('')

    try {
      console.log('Testing appointment submission...')
      
      const testData = {
        request_type: 'general_appointment',
        patient_id: null, // Test without a patient_id first
        request_details: {
          postalCode: 'K2G 6S3',
          date: '2025-06-02',
          time: 'Dès que possible',
          healthCard: 'JETA23532523', // 12 character health card
          cardSequenceNumber: '35',
          patientName: 'Test User'
        }
      }

      console.log('Sending test data:', testData)
      
      const apiResult = await createAppointmentRequest(testData)
      
      console.log('API result:', apiResult)
      setResult(apiResult)
      
      if (!apiResult.success) {
        setError(apiResult.error || 'Unknown error')
      }
    } catch (err) {
      console.error('Test error:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">Test Appointment Submission</h1>
          <p className="text-muted-foreground">Debug appointment submission API</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>API Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testSubmission} 
              disabled={isSubmitting}
              className="w-full"
            >
              {isSubmitting ? 'Testing...' : 'Test Appointment Submission'}
            </Button>

            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <h3 className="font-semibold text-red-700">Error:</h3>
                <p className="text-red-600">{error}</p>
              </div>
            )}

            {result && (
              <div className="p-4 bg-gray-50 border rounded-md">
                <h3 className="font-semibold mb-2">API Result:</h3>
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
} 