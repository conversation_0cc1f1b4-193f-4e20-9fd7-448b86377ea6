"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { FamilyMembersProvider } from "@/lib/FamilyMembersContext";
import { LanguageProvider } from "@/lib/LanguageContext";
import { AuthProvider } from "@/lib/AuthContext";
import { TranslationLoader } from "@/components/translation-loader";
import { ProfileUpdateProvider } from "@/lib/profile-update-context";
import { ThemeProvider } from "@/components/theme-provider";

export default function ClientBody({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();
  
  // Check if the current page is landing page or auth page
  const isLandingOrAuthPage = pathname === "/" || 
                            pathname === "/landing" || 
                            pathname?.startsWith("/auth");

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <AuthProvider>
      <ProfileUpdateProvider>
        <ThemeProvider 
          attribute="class" 
          defaultTheme={isLandingOrAuthPage ? "light" : "system"} 
          enableSystem={!isLandingOrAuthPage} 
          forcedTheme={isLandingOrAuthPage ? "light" : undefined}>
          <LanguageProvider>
            <TranslationLoader>
              <FamilyMembersProvider>
                {children}
              </FamilyMembersProvider>
            </TranslationLoader>
          </LanguageProvider>
        </ThemeProvider>
      </ProfileUpdateProvider>
    </AuthProvider>
  );
}
