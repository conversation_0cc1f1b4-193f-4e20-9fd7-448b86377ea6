"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, CreditCard, ExternalLink } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useAuth } from "@/lib/AuthContext"
import { useLanguage } from "@/lib/LanguageContext"
import { useSubscriptionStore, useSubscriptionDetails } from "@/stores/useSubscriptionStore"
import { supabase } from "@/lib/supabase/client"
import { useExternalNavigation } from "@/lib/hooks/useExternalNavigation"
import { StripeReturnHand<PERSON> } from "@/components/auth/stripe-return-handler"

// Subscription content configurations
const SUBSCRIPTION_CONTENT = {
  'family-annual': {
    fr: {
      title: "Plan familiale",
      price: "134,40$ / an",
      description: "Ce que vous obtenez:",
      features: [
        "Réservations illimitées pour 5 membres de votre famille",
        "Notifications par courriel pour vos rendez-vous"
      ]
    },
    en: {
      title: "Family plan",
      price: "$134.40 / year",
      description: "What you get:",
      features: [
        "Unlimited bookings for 5 family members",
        "Email notifications for your appointments"
      ]
    }
  },
  'individual-annual': {
    fr: {
      title: "Plan individuel",
      price: "71,40$ / an",
      description: "Ce que vous obtenez:",
      features: [
        "Réservations illimitées pour une personne",
        "Notifications par courriel pour vos rendez-vous"
      ]
    },
    en: {
      title: "Individual plan",
      price: "$71.40 / year",
      description: "What you get:",
      features: [
        "Unlimited bookings for one person",
        "Email notifications for your appointments"
      ]
    }
  },
  'family-monthly': {
    fr: {
      title: "Plan familiale",
      price: "14,95$ / mois",
      description: "Ce que vous obtenez:",
      features: [
        "Réservations illimitées pour 5 personnes",
        "Notifications par courriel pour vos rendez-vous"
      ]
    },
    en: {
      title: "Family plan",
      price: "$14.95 / month",
      description: "What you get:",
      features: [
        "Unlimited bookings for 5 family members",
        "Email notifications for your appointments"
      ]
    }
  },
  'individual-monthly': {
    fr: {
      title: "Plan individuel",
      price: "7,95$ / mois",
      description: "Ce que vous obtenez:",
      features: [
        "Réservations illimitées pour une personne",
        "Notifications par courriel pour vos rendez-vous"
      ]
    },
    en: {
      title: "Individual plan",
      price: "$7.95 / month",
      description: "What you get:",
      features: [
        "Unlimited bookings for one person",
        "Email notifications for your appointments"
      ]
    }
  }
} as const;

export default function AbonnementPage() {
  const { user } = useAuth();
  const { language } = useLanguage();
  const [isLoadingPortal, setIsLoadingPortal] = useState(false);
  const [portalError, setPortalError] = useState<string | null>(null);

  // Use Zustand store for subscription details
  const fetchSubscription = useSubscriptionStore((state) => state.fetchSubscription);
  const subscriptionDetails = useSubscriptionDetails();

  const { markStripeNavigation } = useExternalNavigation()

  // Fetch subscription on component mount
  useEffect(() => {
    if (user?.id) {
      fetchSubscription(user.id);
    }
  }, [user?.id, fetchSubscription]);

  // Handle Stripe Customer Portal redirection
  const handleManageSubscription = async () => {
    setIsLoadingPortal(true);
    setPortalError(null);

    try {
      // Mark that user is about to navigate to Stripe
      markStripeNavigation();

      // Get the current session token to pass to the API
      const { data: { session } } = await supabase.auth.getSession();
      
      console.log('Session status:', { hasSession: !!session, hasToken: !!session?.access_token });
      
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      
      // Include session token in Authorization header if available
      if (session?.access_token) {
        headers['Authorization'] = `Bearer ${session.access_token}`;
        console.log('Added Authorization header');
      } else {
        console.log('No session token available');
      }

      const response = await fetch(`/api/subscription/portal?locale=${language}`, {
        method: 'GET',
        credentials: 'include',
        headers,
      });

      const data = await response.json();
      console.log('Portal API response:', { status: response.status, data });

      if (!response.ok) {
        // Handle specific error types
        if (response.status === 503 && data.error === 'Stripe Customer Portal not configured') {
          throw new Error('Customer portal is not yet configured. Please contact support for assistance.');
        } else if (response.status === 401) {
          throw new Error('Session expired. Please refresh the page and try again.');
        } else {
          throw new Error(data.error || data.details || 'Failed to create portal session');
        }
      }

      // Redirect to Stripe Customer Portal
      window.location.href = data.url;
    } catch (error) {
      console.error('Error creating portal session:', error);
      setPortalError(error instanceof Error ? error.message : 'Failed to open customer portal');
    } finally {
      setIsLoadingPortal(false);
    }
  };

  // Helper function to get subscription content key
  const getSubscriptionKey = (planType: string, billingPeriod: string): keyof typeof SUBSCRIPTION_CONTENT => {
    return `${planType}-${billingPeriod}` as keyof typeof SUBSCRIPTION_CONTENT;
  };

  // Get current subscription content
  const getSubscriptionContent = () => {
    if (!subscriptionDetails.hasSubscription || !subscriptionDetails.planType || !subscriptionDetails.billingPeriod) {
      return null;
    }

    const key = getSubscriptionKey(subscriptionDetails.planType, subscriptionDetails.billingPeriod);
    const content = SUBSCRIPTION_CONTENT[key];
    
    if (!content) {
      return null;
    }

    return content[language];
  };

  const subscriptionContent = getSubscriptionContent();

  // Loading state
  if (!subscriptionDetails.hasSubscription && !subscriptionDetails.planType) {
    return (
      <StripeReturnHandler>
        <DashboardLayout>
          <div className="space-y-6 mx-auto max-w-6xl">
            <div className="flex items-center gap-2">
              <Button asChild variant="ghost" size="icon" className="h-8 w-8">
                <Link href="/compte">
                  <ArrowLeft className="h-4 w-4" />
                  <span className="sr-only">
                    <T keyName={translationKeys.common.back} />
                  </span>
                </Link>
              </Button>
              <h1 className="text-3xl font-bold">
                <T keyName={translationKeys.subscription.modifySubscription} />
              </h1>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <p className="text-muted-foreground">
                      <T keyName={translationKeys.common.loading} />
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </DashboardLayout>
      </StripeReturnHandler>
    );
  }

  // No subscription state
  if (!subscriptionDetails.hasSubscription) {
    return (
      <StripeReturnHandler>
        <DashboardLayout>
          <div className="space-y-6 mx-auto max-w-6xl">
            <div className="flex items-center gap-2">
              <Button asChild variant="ghost" size="icon" className="h-8 w-8">
                <Link href="/compte">
                  <ArrowLeft className="h-4 w-4" />
                  <span className="sr-only">
                    <T keyName={translationKeys.common.back} />
                  </span>
                </Link>
              </Button>
              <h1 className="text-3xl font-bold">
                <T keyName={translationKeys.subscription.modifySubscription} />
              </h1>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-center py-8">
                  <div className="text-center space-y-4">
                    <p className="text-muted-foreground">
                      {language === 'fr' ? 'Aucun abonnement actif trouvé.' : 'No active subscription found.'}
                    </p>
                    <Button asChild>
                      <Link href="/pricing">
                        {language === 'fr' ? 'Voir les plans' : 'View Plans'}
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </DashboardLayout>
      </StripeReturnHandler>
    );
  }

  return (
    <StripeReturnHandler>
      <DashboardLayout>
        <div className="space-y-6 mx-auto max-w-6xl">
          <div className="flex items-center gap-2">
            <Button asChild variant="ghost" size="icon" className="h-8 w-8">
              <Link href="/compte">
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">
                  <T keyName={translationKeys.common.back} />
                </span>
              </Link>
            </Button>
            <h1 className="text-3xl font-bold">
              <T keyName={translationKeys.subscription.modifySubscription} />
            </h1>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>
                    {subscriptionContent?.title || 'Subscription Plan'}
                  </CardTitle>
                  <CardDescription>
                    {subscriptionContent?.price || 'Subscription Cost'}
                  </CardDescription>
                </div>
                <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                  <CreditCard className="h-5 w-5 text-blue-700" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">
                    {subscriptionContent?.description || 'Benefits'}
                  </h3>
                  <ul className="mt-2 grid gap-2 text-sm">
                    {subscriptionContent?.features.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <span className="h-1.5 w-1.5 rounded-full bg-blue-500" />
                        <span>{feature}</span>
                      </li>
                    )) || (
                      <li className="flex items-center gap-2">
                        <span className="h-1.5 w-1.5 rounded-full bg-blue-500" />
                        <span>
                          <T keyName={translationKeys.subscription.unlimitedAccess} />
                        </span>
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4 pt-4">
              {/* Show cancellation status if subscription is cancelled */}
              {subscriptionDetails.isCancelled && (
                <div className="w-full p-4 bg-orange-50 border border-orange-200 rounded-lg dark:bg-orange-900/20 dark:border-orange-800">
                  <p className="text-orange-800 dark:text-orange-200 text-sm font-medium">
                    {language === 'fr' ? 
                      `Votre abonnement sera annulé le ${subscriptionDetails.currentPeriodEnd?.toLocaleDateString('fr-CA')}` :
                      `Your subscription will be cancelled on ${subscriptionDetails.currentPeriodEnd?.toLocaleDateString('en-CA')}`
                    }
                  </p>
                  <p className="text-orange-600 dark:text-orange-300 text-xs mt-1">
                    {language === 'fr' ? 
                      'Vous conserverez l\'accès jusqu\'à cette date.' :
                      'You will retain access until this date.'
                    }
                  </p>
                </div>
              )}

              {/* Portal error message */}
              {portalError && (
                <div className="w-full p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-900/20 dark:border-red-800">
                  <p className="text-red-800 dark:text-red-200 text-sm">
                    {portalError}
                  </p>
                </div>
              )}

              {/* Manage Subscription Button */}
              <div className="w-full max-w-md mx-auto">
                <Button
                  className="w-full"
                  onClick={handleManageSubscription}
                  disabled={isLoadingPortal}
                >
                  {isLoadingPortal ? (
                    <>
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white mr-2"></div>
                      {language === 'fr' ? 'Ouverture...' : 'Opening...'}
                    </>
                  ) : (
                    <>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      {language === 'fr' ? 'Gérer l\'abonnement' : 'Manage Subscription'}
                    </>
                  )}
                </Button>
              </div>

              <p className="text-xs text-muted-foreground text-center max-w-md mx-auto">
                {language === 'fr' ? 
                  'Vous serez redirigé vers le portail client Stripe pour gérer votre abonnement, modifier votre méthode de paiement ou annuler votre abonnement.' :
                  'You will be redirected to the Stripe customer portal to manage your subscription, update payment methods, or cancel your subscription.'
                }
              </p>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>
                <T keyName={translationKeys.subscription.paymentHistory} />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between border-b pb-4">
                  <div className="space-y-1">
                    <p className="font-medium">
                      {subscriptionContent?.title} - {subscriptionDetails.billingPeriod === 'monthly' ? 
                        (language === 'fr' ? 'Mensuel' : 'Monthly') : 
                        (language === 'fr' ? 'Annuel' : 'Annual')
                      }
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {subscriptionDetails.currentPeriodStart?.toLocaleDateString(language === 'fr' ? 'fr-CA' : 'en-CA')}
                    </p>
                  </div>
                  <p className="font-medium">
                    {subscriptionContent?.price}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    </StripeReturnHandler>
  )
}
