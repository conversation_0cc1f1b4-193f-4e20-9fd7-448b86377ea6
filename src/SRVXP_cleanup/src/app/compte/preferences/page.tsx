"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, Globe, Loader2, CheckCircle2 } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useTranslation } from "@/components/t"
import { useLanguage } from "@/lib/LanguageContext"
import { useUserPreferences } from "@/hooks/use-user-preferences"
import { useAuth } from "@/lib/AuthContext"
import { useCombinedStore } from "@/stores/useAppStore"

export default function PreferencesPage() {
  // Get data and actions from the combined store
  const {
    user,
    preferences,
    language,
    setLanguage,
    updatePreferences: updateStorePreferences,
    isPreferencesLoading,
    fetchPreferences
  } = useCombinedStore()

  // For backward compatibility, also use the hooks
  const { language: hookLanguage, setLanguage: setAppLanguage } = useLanguage()
  const { t } = useTranslation()
  const { preferences: hookPreferences, isLoading: hookLoading, isSaving: hookSaving, saveError: hookSaveError, saveSuccess: hookSaveSuccess, updatePreferences: hookUpdatePreferences } = useUserPreferences()

  // Use store data if available, otherwise fall back to hook data
  const currentLanguage = language || hookLanguage
  const isLoading = isPreferencesLoading || hookLoading

  // For tracking UI state
  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState<Error | null>(null)
  const [saveSuccess, setSaveSuccess] = useState(false)

  // Fetch preferences when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchPreferences(user.id)
    }
  }, [user?.id, fetchPreferences])

  // For tracking UI state
  const [pendingLanguage, setPendingLanguage] = useState<"fr" | "en">(currentLanguage)
  const [hasChanges, setHasChanges] = useState(false)

  // Force sync with application settings whenever they change
  useEffect(() => {
    setPendingLanguage(currentLanguage)
  }, [currentLanguage])

  // Detect changes when user modifies the form
  useEffect(() => {
    const languageChanged = pendingLanguage !== currentLanguage
    setHasChanges(languageChanged)
  }, [pendingLanguage, currentLanguage])

  // Handle language selection change
  const handleLanguageChange = (newLanguage: string) => {
    if (newLanguage === "fr" || newLanguage === "en") {
      console.log("Language selection changed to:", newLanguage)
      setPendingLanguage(newLanguage as "fr" | "en")
    }
  }



  // Save changes to Supabase and update application state
  const handleSaveChanges = async () => {
    if (!user?.id) return

    console.log("Saving preferences:", { pendingLanguage })
    setIsSaving(true)
    setSaveError(null)
    setSaveSuccess(false)

    try {
      // Update application state immediately for better UX
      if (pendingLanguage !== currentLanguage) {
        setLanguage(pendingLanguage)
        setAppLanguage(pendingLanguage) // For backward compatibility
      }

      // Try to save using the store first
      if (updateStorePreferences) {
        try {
          // Save to database using the store
          await updateStorePreferences(user.id, {
            language: pendingLanguage
          })

          // Reset change detection
          setHasChanges(false)
          setSaveSuccess(true)
          setTimeout(() => setSaveSuccess(false), 3000)

          console.log("Preferences saved successfully using store")
          setIsSaving(false)
          return
        } catch (storeError) {
          console.error("Error saving preferences with store:", storeError)
          // Continue with fallback method
        }
      }

      // Fallback: Save to database using the hook
      await hookUpdatePreferences({
        language: pendingLanguage
      })

      // Reset change detection
      setHasChanges(false)
      setSaveSuccess(true)
      setTimeout(() => setSaveSuccess(false), 3000)

      console.log("Preferences saved successfully using hook")
    } catch (error) {
      console.error("Error saving preferences:", error)
      setSaveError(error instanceof Error ? error : new Error('Failed to save preferences'))

      // Revert to application settings on error
      setPendingLanguage(currentLanguage)
    } finally {
      setIsSaving(false)
    }
  }

  // Reset form to current application settings
  const handleCancel = () => {
    setPendingLanguage(currentLanguage)
    setHasChanges(false)
    console.log("Changes canceled, reverting to:", { language: currentLanguage })
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button asChild variant="ghost" size="icon" className="h-8 w-8">
            <Link href="/compte">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">
                <T keyName={translationKeys.common.back} />
              </span>
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">
            <T keyName={translationKeys.preferences.managePreferences} />
          </h1>
        </div>

        <div className="grid gap-6">
          {/* Success message */}
          {saveSuccess && (
            <Alert className="bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800">
              <CheckCircle2 className="h-4 w-4 text-green-600 dark:text-green-400" />
              <AlertDescription className="text-green-600 dark:text-green-400">
                <T keyName={translationKeys.preferences.changesSaved} />
              </AlertDescription>
            </Alert>
          )}

          {/* Error message */}
          {saveError && (
            <Alert variant="destructive">
              <AlertDescription>
                {saveError.message?.includes("user_preferences table doesn't exist")
                  ? "Une erreur de configuration du système s'est produite. Veuillez contacter l'administrateur."
                  : (saveError.message || <T keyName={translationKeys.preferences.errorSaving} />)}
              </AlertDescription>
            </Alert>
          )}

          {/* Language & Theme Settings */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-start gap-4">
                <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                  <Globe className="h-5 w-5 text-blue-700" />
                </div>
                <div>
                  <CardTitle>
                    <T keyName={translationKeys.preferences.preferredLanguage} />
                  </CardTitle>
                  <CardDescription className="mt-1.5">
                    <T keyName={translationKeys.preferences.languageDescription} />
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="language">
                    <T keyName={translationKeys.preferences.preferredLanguage} />
                  </Label>
                  <Select value={pendingLanguage} onValueChange={handleLanguageChange}>
                    <SelectTrigger id="language">
                      <SelectValue placeholder={t(translationKeys.preferences.preferredLanguage)} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fr">
                        <T keyName={translationKeys.preferences.french} />
                      </SelectItem>
                      <SelectItem value="en">
                        <T keyName={translationKeys.preferences.english} />
                      </SelectItem>
                    </SelectContent>
                  </Select>

                </div>


              </div>
            </CardContent>
            <CardFooter className="flex gap-2 justify-end">
              {hasChanges && (
                <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  <T keyName={translationKeys.common.cancel} />
                </Button>
              )}
              <Button
                className="min-w-32"
                onClick={handleSaveChanges}
                disabled={isSaving || !hasChanges}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    <T keyName={translationKeys.preferences.saving} />
                  </>
                ) : (
                  <T keyName={translationKeys.preferences.saveChanges} />
                )}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
