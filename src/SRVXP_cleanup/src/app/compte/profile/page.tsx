"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, Check, Mail, Phone, User, AlertTriangle } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useTranslation } from "@/components/t"
import { useUserProfile } from "@/hooks/use-user-profile"
import { useAuth } from "@/lib/AuthContext"
import { updateUserProfile } from "@/lib/user-utils"
import { useProfileUpdates } from "@/lib/profile-update-context"
import { useCombinedStore } from "@/stores/useAppStore"
import { <PERSON><PERSON>, <PERSON>ertDescription } from "@/components/ui/alert"

// Form validation helpers
function isValidEmail(email: string): boolean {
  if (!email || email.trim() === '') {
    return false
  }
  // More comprehensive email regex that requires domain with at least 2 characters after the dot
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/
  return emailRegex.test(email)
}

function validatePhone(phone: string): { valid: boolean; formattedPhone: string } {
  if (!phone || phone.trim() === '') {
    return { valid: true, formattedPhone: '' }; // Empty is valid
  }

  // Remove all non-digit characters
  const digitsOnly = phone.replace(/\D/g, '');

  // Check if we have a valid number of digits
  if (digitsOnly.length < 10 || digitsOnly.length > 15) {
    return { valid: false, formattedPhone: phone };
  }

  // Format the phone number
  let formattedPhone = phone;

  if (digitsOnly.length === 10) {
    // Format as (XXX) XXX-XXXX
    formattedPhone = `(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6)}`;
  } else if (digitsOnly.length === 11 && digitsOnly.charAt(0) === '1') {
    // Format as +1 (XXX) XXX-XXXX
    formattedPhone = `+1 (${digitsOnly.substring(1, 4)}) ${digitsOnly.substring(4, 7)}-${digitsOnly.substring(7)}`;
  }

  return { valid: true, formattedPhone };
}

// Format phone as user types
function formatPhoneOnChange(input: string): string {
  // Remove non-digits
  const digitsOnly = input.replace(/\D/g, '');

  // Format the phone number as the user types
  if (digitsOnly.length <= 3) {
    return digitsOnly;
  } else if (digitsOnly.length <= 6) {
    return `(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3)}`;
  } else {
    return `(${digitsOnly.substring(0, 3)}) ${digitsOnly.substring(3, 6)}-${digitsOnly.substring(6, 10)}`;
  }
}

export default function ProfilePage() {
  // Get data from the combined store
  const {
    profile: storeProfile,
    user,
    session,
    isProfileLoading,
    updateProfile: updateStoreProfile,
    fetchProfile
  } = useCombinedStore()

  // For backward compatibility, also use the hook
  const { profile: userProfile, isLoading: hookLoading, initials: hookInitials, phone: hookPhone, invalidateCache, isFromCache } = useUserProfile()
  const { notifyProfileUpdate } = useProfileUpdates()

  // Use store data if available, otherwise fall back to hook data
  const profile = storeProfile || userProfile
  const isLoading = isProfileLoading || hookLoading
  const initials = storeProfile ? `${storeProfile.firstName?.charAt(0) || ''}${storeProfile.lastName?.charAt(0) || ''}`.toUpperCase() : hookInitials
  const phone = storeProfile?.phone || hookPhone

  // Form state (what the user is editing)
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: ""
  })

  // Display state (what is shown in the UI header)
  const [displayProfile, setDisplayProfile] = useState({
    firstName: "",
    lastName: ""
  })

  const [saved, setSaved] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [firstNameError, setFirstNameError] = useState<string | null>(null)
  const [lastNameError, setLastNameError] = useState<string | null>(null)
  const [emailError, setEmailError] = useState<string | null>(null)
  const { t } = useTranslation()

  // Fetch profile data when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchProfile(user.id)
    }
  }, [user?.id, fetchProfile])

  // Update both states when user profile data is loaded
  useEffect(() => {
    if (user) {
      const userData = {
        firstName: profile?.firstName || "",
        lastName: profile?.lastName || "",
        email: user.email || "",
        phone: phone || profile?.phone || ""
      }

      setFormData(userData)
      setDisplayProfile({
        firstName: userData.firstName,
        lastName: userData.lastName
      })

      // Reset validation errors when loading new data
      setFirstNameError(null)
      setLastNameError(null)
      setEmailError(null)
    }
  }, [profile, user, phone])

  const handleChange = (field: string, value: string) => {
    // Format phone number as user types if the field is phone
    if (field === 'phone') {
      value = formatPhoneOnChange(value);
    }

    // First name validation
    if (field === 'firstName') {
      setFirstNameError(null);

      // Check for empty first name
      if (value.trim() === '') {
        setFirstNameError(t('account.firstNameRequired'));
      }
    }

    // Last name validation
    if (field === 'lastName') {
      setLastNameError(null);

      // Check for empty last name
      if (value.trim() === '') {
        setLastNameError(t('account.lastNameRequired'));
      }
    }

    // Email validation
    if (field === 'email') {
      setEmailError(null);

      // Check for empty email
      if (value.trim() === '') {
        setEmailError(t('account.emailCannotBeEmpty'));
      }
      // Check for invalid email format (only if not empty)
      else if (!isValidEmail(value)) {
        setEmailError(t('account.invalidEmail'));
      }
    }

    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  }

  const handleSave = async () => {
    if (!user?.id) return

    setError(null)
    setSaving(true)

    try {
      // Validate first name is not empty
      if (!formData.firstName.trim()) {
        setFirstNameError(t('account.firstNameRequired'))
        setSaving(false)
        return
      }

      // Validate last name is not empty
      if (!formData.lastName.trim()) {
        setLastNameError(t('account.lastNameRequired'))
        setSaving(false)
        return
      }

      // Validate email is not empty
      if (!formData.email.trim()) {
        setEmailError(t('account.emailCannotBeEmpty'))
        setSaving(false)
        return
      }

      // Validate email format
      if (formData.email && !isValidEmail(formData.email)) {
        setEmailError(t('account.invalidEmail'))
        setSaving(false)
        return
      }

      // Validate and format phone number
      const { valid: phoneValid, formattedPhone } = validatePhone(formData.phone);
      if (!phoneValid) {
        setError(t('account.invalidPhone') || 'Please enter a valid phone number');
        setSaving(false);
        return;
      }

      // Update form with formatted phone
      if (formattedPhone !== formData.phone) {
        setFormData(prev => ({ ...prev, phone: formattedPhone }));
      }

      // Create profile data object
      const profileData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formattedPhone  // Use the formatted phone number
      }

      // Try to update profile using the store first
      if (updateStoreProfile) {
        try {
          // Use the store to update the profile
          const storeResult = await updateStoreProfile(user.id, profileData)

          if (storeResult) {
            // Update the display profile with the form data
            setDisplayProfile({
              firstName: formData.firstName,
              lastName: formData.lastName
            })

            // Show success message
            setSaved(true)
            setTimeout(() => setSaved(false), 3000)

            // If the email was updated, show verification message
            if (formData.email !== user.email) {
              setError(t('account.emailVerificationSent') ||
                'A verification email has been sent to your new email address. Please verify it to complete the update.')
            }

            // Notify profile updates for backward compatibility
            notifyProfileUpdate(user.id)
            return
          }
        } catch (storeError) {
          console.error('Error updating profile with store:', storeError)
          // Continue with fallback method
        }
      }

      // Save changes to Supabase (fallback method)
      const result = await updateUserProfile(
        user.id,
        profileData,
        session
      )

      if (result.success) {
        // Update the display profile with the form data
        setDisplayProfile({
          firstName: formData.firstName,
          lastName: formData.lastName
        })

        // Force invalidation of the cache to ensure fresh data
        if (user.email) {
          // Pass true as the third parameter to trigger a reload of profile data
          invalidateCache(user.id, user.email, true)

          // Explicitly notify all components listening for profile updates
          notifyProfileUpdate(user.id)

          // If the email was updated, handle verification notification
          if (result.updates.includes('email')) {
            // Show success message
            setSaved(true)
            setTimeout(() => setSaved(false), 3000)

            // Alert the user about the email verification if needed
            if (formData.email !== user.email) {
              setError(t('account.emailVerificationSent') ||
                'A verification email has been sent to your new email address. Please verify it to complete the update.')
            }
          } else {
            // Show success message for non-email updates
            setSaved(true)
            setTimeout(() => setSaved(false), 3000)
          }
        } else {
          // Show success message
          setSaved(true)
          setTimeout(() => setSaved(false), 3000)
        }
      } else {
        setError(t('common.errorOccurred') || 'An error occurred while saving your profile.')
      }
    } catch (err) {
      console.error('Error saving profile:', err)
      setError(err instanceof Error ? err.message : t('common.errorOccurred') || 'An error occurred while saving your profile.')
    } finally {
      setSaving(false)
    }
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="space-y-6 mx-auto max-w-6xl">
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold">
              <T keyName={translationKeys.account.modifyProfile} />
            </h1>
          </div>
          <Card>
            <CardContent className="py-10">
              <div className="flex justify-center">
                <div className="animate-pulse text-center">
                  <div className="h-20 w-20 bg-gray-200 rounded-full mx-auto mb-4"></div>
                  <div className="h-6 w-40 bg-gray-200 rounded mx-auto mb-2"></div>
                  <div className="h-4 w-60 bg-gray-200 rounded mx-auto"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 mx-auto max-w-6xl">
        <div className="flex items-center gap-2">
          <Button asChild variant="ghost" size="icon" className="h-8 w-8">
            <Link href="/compte">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">
                <T keyName={translationKeys.common.back} />
              </span>
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">
            <T keyName={translationKeys.account.modifyProfile} />
          </h1>
        </div>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16 flex-shrink-0">
                <AvatarImage src="" alt="Avatar" />
                <AvatarFallback className="bg-blue-100 text-blue-700">{initials}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle>{displayProfile.firstName} {displayProfile.lastName}</CardTitle>
                <CardDescription className="mt-2">
                  <T keyName={translationKeys.account.editPersonalInfo} />
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="firstName">
                    <T keyName={translationKeys.account.firstName} />
                  </Label>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={(e) => handleChange("firstName", e.target.value)}
                      className={firstNameError ? "border-orange-500" : ""}
                    />
                  </div>
                  {firstNameError && (
                    <div className="flex items-center gap-1 mt-1">
                      <AlertTriangle className="h-4 w-4 text-orange-500" />
                      <p className="text-sm text-orange-500">{firstNameError}</p>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">
                    <T keyName={translationKeys.account.lastName} />
                  </Label>
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={(e) => handleChange("lastName", e.target.value)}
                      className={lastNameError ? "border-orange-500" : ""}
                    />
                  </div>
                  {lastNameError && (
                    <div className="flex items-center gap-1 mt-1">
                      <AlertTriangle className="h-4 w-4 text-orange-500" />
                      <p className="text-sm text-orange-500">{lastNameError}</p>
                    </div>
                  )}
                </div>
              </div>

              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="email">
                    <T keyName={translationKeys.account.email} />
                  </Label>
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleChange("email", e.target.value)}
                      className={emailError ? "border-orange-500" : ""}
                    />
                  </div>
                  {emailError && (
                    <div className="flex items-center gap-1 mt-1">
                      <AlertTriangle className="h-4 w-4 text-orange-500" />
                      <p className="text-sm text-orange-500">{emailError}</p>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">
                    <T keyName={translationKeys.account.phone} />
                  </Label>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleChange("phone", e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col gap-4 w-full">
            {error && (
              <Alert className="w-full border-orange-500 bg-orange-50/50 text-orange-800">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            <div className="flex justify-end w-full">
              <Button
                onClick={handleSave}
                className="flex items-center gap-1"
                disabled={saving || !!emailError || !!firstNameError || !!lastNameError || !formData.email.trim() || !formData.firstName.trim() || !formData.lastName.trim()}
              >
                {saved ? <Check className="h-4 w-4" /> : null}
                {saving ? (
                  <T keyName={translationKeys.common.saving || 'Saving...'} />
                ) : saved ? (
                  <T keyName={translationKeys.common.saved} />
                ) : (
                  <T keyName={translationKeys.common.saveChanges} />
                )}
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  )
}
