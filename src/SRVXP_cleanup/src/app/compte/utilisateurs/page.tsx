"use client"

import { useState, useRef, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, Loader2, <PERSON><PERSON><PERSON>, Save, Trash2, User, <PERSON> } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import { useFamilyMembers } from "@/lib/FamilyMembersContext"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useLanguage } from "@/lib/LanguageContext"
import { DateTimePickerV2 } from "@/components/date-time-picker-v2"
import { useCombinedStore } from "@/stores/useAppStore"

export default function UtilisateursPage() {
  const { familyMembers, updateTempFamilyMember, toggleEditing, saveMemberChanges, deleteMember, isLoading: familyMembersLoading, error: familyMembersError } = useFamilyMembers();
  const { language } = useLanguage();
  const locale = language === "fr" ? fr : undefined;
  const [datePickerKeys, setDatePickerKeys] = useState<{[id: number]: number}>({});
  const [validationErrors, setValidationErrors] = useState<{[id: number]: boolean}>({});

  // Get subscription data from the combined store
  const { subscription, fetchSubscription, user, isSubscriptionLoading } = useCombinedStore();

  // Fetch subscription data when component mounts
  useEffect(() => {
    if (user?.id) {
      fetchSubscription(user.id);
    }
  }, [user?.id, fetchSubscription]);

  // Determine if user has a family plan
  const isFamilyPlan = subscription?.plan_type === 'family';

  // Determine if we're loading data
  const isLoading = familyMembersLoading || isSubscriptionLoading;
  const error = familyMembersError;

  const handleHealthCardChange = (id: number, value: string) => {
    // Only allow uppercase letters and limit to 4 characters
    const formattedValue = value.toUpperCase().replace(/[^A-Z]/g, '').substring(0, 4)
    updateTempFamilyMember(id, { tempHealthCard: formattedValue })
  }

  const handleSave = (id: number) => {
    const success = saveMemberChanges(id);

    // Update validation state
    setValidationErrors(prev => ({
      ...prev,
      [id]: !success
    }));

    // Clear validation error after 3 seconds if there was an error
    if (!success) {
      setTimeout(() => {
        setValidationErrors(prev => ({
          ...prev,
          [id]: false
        }));
      }, 3000);
    }
  }

  const handleDelete = (id: number) => {
    // First, ensure tempBirthDate is explicitly set to undefined
    updateTempFamilyMember(id, { tempBirthDate: undefined });
    // Update the key for this member's date picker to force remounting
    setDatePickerKeys(prev => ({
      ...prev,
      [id]: (prev[id] || 0) + 1
    }));
    // Clear any validation errors for this member
    setValidationErrors(prev => ({
      ...prev,
      [id]: false
    }));
    // Then call deleteMember which will now close the form
    deleteMember(id);
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 mx-auto max-w-6xl">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            <span className="ml-2 text-lg">
              <T keyName={translationKeys.common.loading} />
            </span>
          </div>
        ) : error ? (
          <div className="p-4 border border-red-200 rounded-md bg-red-50 text-red-700">
            <p>Error: {error.message}</p>
            <p>
              <T keyName={translationKeys.errors.tryAgainLater} />
            </p>
          </div>
        ) : (
          <>
            <div className="flex items-center gap-2">
          <Button asChild variant="ghost" size="icon" className="h-8 w-8">
            <Link href="/compte">
              <ArrowLeft className="h-4 w-4" />
              <span className="sr-only">
                <T keyName={translationKeys.common.back} />
              </span>
            </Link>
          </Button>
          <h1 className="text-3xl font-bold">
            <T keyName={isFamilyPlan
              ? translationKeys.users.manageAccountUsers
              : translationKeys.users.manageProfile} />
          </h1>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              <T keyName={isFamilyPlan
                ? translationKeys.users.familyMembers
                : translationKeys.users.userProfile} />
            </CardTitle>
            <CardDescription>
              <T keyName={isFamilyPlan
                ? translationKeys.users.familyMembersDescription
                : translationKeys.users.userProfileDescription} />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-5">
              {/* For family plan, show all 5 slots. For individual plan, show only the first slot */}
              {(isFamilyPlan
                ? (familyMembers.length === 5 ? familyMembers : [...Array(5)].map((_, i) =>
                    familyMembers.find(m => m.id === i + 1) || {
                      id: i + 1,
                      firstName: "",
                      lastName: "",
                      healthCard: "",
                      birthDate: undefined,
                      editing: false
                    }
                  ))
                : [familyMembers[0] || {
                    id: 1,
                    firstName: "",
                    lastName: "",
                    healthCard: "",
                    birthDate: undefined,
                    editing: false
                  }]
              ).map((member) => (
                <div key={member.id} className="rounded-lg border bg-card p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 min-h-[2.5rem] min-w-[2.5rem] rounded-full bg-blue-100 flex items-center justify-center shrink-0">
                        <User className="h-5 w-5 text-blue-700" />
                      </div>
                      <div>
                        {member.firstName || member.lastName ? (
                          <h3 className="font-medium">{`${member.firstName} ${member.lastName}`}</h3>
                        ) : (
                          <h3 className="font-medium text-muted-foreground">
                            <T keyName={isFamilyPlan
                              ? translationKeys.users.addMember
                              : translationKeys.users.addYourInfo} />
                          </h3>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleEditing(member.id)}
                      className="flex items-center gap-1.5"
                    >
                      {member.editing ? (
                        <>
                          <X className="h-4 w-4" />
                          <span>
                            <T keyName={translationKeys.users.cancel} />
                          </span>
                        </>
                      ) : (
                        <>
                          <Pencil className="h-4 w-4" />
                          <span>
                            <T keyName={translationKeys.users.edit} />
                          </span>
                        </>
                      )}
                    </Button>
                  </div>

                  {member.editing ? (
                    <div className="space-y-4">
                      <div className="grid gap-4 sm:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor={`firstName-${member.id}`}>
                            <T keyName={translationKeys.users.firstName} />
                          </Label>
                          <Input
                            id={`firstName-${member.id}`}
                            value={member.tempFirstName}
                            onChange={(e) => updateTempFamilyMember(member.id, { tempFirstName: e.target.value })}
                            className={validationErrors[member.id] && !member.tempFirstName ? "border-red-500 focus-visible:ring-red-500" : ""}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`lastName-${member.id}`}>
                            <T keyName={translationKeys.users.lastName} />
                          </Label>
                          <Input
                            id={`lastName-${member.id}`}
                            value={member.tempLastName}
                            onChange={(e) => updateTempFamilyMember(member.id, { tempLastName: e.target.value })}
                            className={validationErrors[member.id] && !member.tempLastName ? "border-red-500 focus-visible:ring-red-500" : ""}
                          />
                        </div>
                      </div>
                      <div className="grid gap-4 sm:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor={`healthCard-${member.id}`}>
                            <T keyName={translationKeys.users.healthCardPrefix} />
                          </Label>
                          <Input
                            id={`healthCard-${member.id}`}
                            value={member.tempHealthCard}
                            onChange={(e) => handleHealthCardChange(member.id, e.target.value)}
                            maxLength={4}
                            className={cn(
                              "uppercase",
                              validationErrors[member.id] && (!member.tempHealthCard || member.tempHealthCard.length < 4) ? "border-red-500 focus-visible:ring-red-500" : ""
                            )}
                          />
                          <p className="text-xs text-muted-foreground">
                            <T keyName={translationKeys.users.healthCardDescription} />
                          </p>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`birthDate-${member.id}`}>
                            <T keyName={translationKeys.users.birthDate} />
                          </Label>
                          <div className={validationErrors[member.id] && !member.tempBirthDate ? "border rounded border-red-500" : ""}>
                            <DateTimePickerV2
                              key={`date-picker-${member.id}-${datePickerKeys[member.id] || 0}`}
                              date={member.tempBirthDate}
                              onSelect={(date) => updateTempFamilyMember(member.id, { tempBirthDate: date })}
                              locale={locale}
                              fromYear={1900}
                              toYear={2025}
                            />
                          </div>
                        </div>
                      </div>
                      {validationErrors[member.id] && (
                        <div className="text-sm text-red-500 bg-red-50 dark:bg-red-950 p-2 rounded border border-red-200 dark:border-red-800 mb-2">
                          <T keyName={translationKeys.users.validationError} fallback="Please enter first name, last name, health card (4 characters), and birth date to save."/>
                        </div>
                      )}
                      <div className="flex justify-between items-center">
                        <Button
                          variant="outline"
                          onClick={() => handleDelete(member.id)}
                          className="flex items-center gap-1 border-red-500 text-red-500 hover:bg-red-50 hover:text-red-500 dark:hover:bg-red-950 dark:border-red-400 dark:text-red-400 dark:hover:text-red-400"
                        >
                          <Trash2 className="h-4 w-4" />
                          <T keyName={translationKeys.common.delete} />
                        </Button>
                        <Button
                          onClick={() => handleSave(member.id)}
                          className="flex items-center gap-1"
                        >
                          <Save className="h-4 w-4" />
                          <T keyName={translationKeys.users.save} />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    member.firstName || member.lastName ? (
                      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 text-sm">
                        {member.healthCard && (
                          <div className="flex flex-col">
                            <span className="text-muted-foreground">
                              <T keyName={translationKeys.users.healthCard} />
                            </span>
                            <span className="mt-1">TRAF-XXXX-XXXX</span>
                          </div>
                        )}
                        {member.birthDate && (
                          <div className="flex flex-col">
                            <span className="text-muted-foreground">
                              <T keyName={translationKeys.users.birthDate} />
                            </span>
                            <span className="mt-1">{format(member.birthDate, 'PPP', { locale })}</span>
                          </div>
                        )}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        <T keyName={isFamilyPlan
                          ? translationKeys.users.editMemberPrompt
                          : translationKeys.users.addYourInfoPrompt} />
                      </p>
                    )
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
          </>
        )}
      </div>
    </DashboardLayout>
  )
}
