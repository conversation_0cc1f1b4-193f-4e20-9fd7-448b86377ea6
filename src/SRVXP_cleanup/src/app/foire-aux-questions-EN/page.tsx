import { Navbar } from "@/components/zaply/layout/Navbar";
import { Footer } from "@/components/zaply/layout/Footer";
import { Metadata } from "next";
import { FaqFullPageEN } from "@/components/zaply/sections/FaqFullPageEN";

export const metadata: Metadata = {
  title: "Frequently Asked Questions | Sans rendez-vous express",
  description: "Find answers to your questions about Sans rendez-vous express and our service for quickly finding medical appointments.",
};

export default function FaqPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <main className="flex-grow">
        {/* FAQ Full Page - White Background */}
        <div className="bg-white w-full pt-4 pb-16">
          <div className="container mx-auto px-4 sm:px-6">
            <FaqFullPageEN />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
