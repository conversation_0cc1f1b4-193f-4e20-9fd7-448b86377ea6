import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  console.log('=== Simple Test API - POST route called ===')
  
  try {
    // Simple request parsing
    console.log('Parsing request data...')
    const requestData = await request.json()
    console.log('Request data received:', requestData)

    // Simple response
    const response = {
      success: true,
      message: 'Simple test endpoint working',
      receivedData: requestData,
      timestamp: new Date().toISOString()
    }

    console.log('Sending response:', response)
    return NextResponse.json(response)

  } catch (error) {
    console.error('Error in simple test endpoint:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  console.log('=== Simple Test API - GET route called ===')
  
  return NextResponse.json({
    success: true,
    message: 'Simple GET endpoint working',
    timestamp: new Date().toISOString()
  })
} 