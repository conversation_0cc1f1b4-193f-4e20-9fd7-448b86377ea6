import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    console.log('=== SESSION CHECK DEBUG START ===')
    
    const supabase = await createClient()
    
    // Debug all the ways auth can come in
    const authHeader = request.headers.get('authorization')
    const cookieHeader = request.headers.get('cookie')
    
    console.log('Request details:', {
      hasAuthHeader: !!authHeader,
      authHeaderPreview: authHeader ? authHeader.substring(0, 30) + '...' : 'none',
      hasCookies: !!cookieHeader,
      cookiePreview: cookieHeader ? cookieHeader.substring(0, 50) + '...' : 'none',
      userAgent: request.headers.get('user-agent')?.substring(0, 50) + '...',
      origin: request.headers.get('origin'),
    })
    
    let results: any = {}
    
    // Try Bearer token auth
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      console.log('Testing Bearer token auth...')
      const { data, error } = await supabase.auth.getUser(token)
      results.bearerAuth = {
        success: !!data?.user,
        userId: data?.user?.id,
        email: data?.user?.email,
        error: error?.message,
      }
    }
    
    // Try cookie auth
    console.log('Testing cookie auth...')
    const { data: cookieData, error: cookieError } = await supabase.auth.getUser()
    results.cookieAuth = {
      success: !!cookieData?.user,
      userId: cookieData?.user?.id,
      email: cookieData?.user?.email,
      error: cookieError?.message,
    }
    
    // Try getting session
    console.log('Testing session retrieval...')
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession()
    results.session = {
      hasSession: !!sessionData?.session,
      hasAccessToken: !!sessionData?.session?.access_token,
      tokenPreview: sessionData?.session?.access_token ? 
        sessionData.session.access_token.substring(0, 20) + '...' : 'none',
      error: sessionError?.message,
    }
    
    console.log('=== SESSION CHECK DEBUG END ===')
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      results,
      recommendation: results.bearerAuth?.success ? 'Bearer auth working' :
                     results.cookieAuth?.success ? 'Cookie auth working' :
                     'No authentication method working'
    })
  } catch (error) {
    console.error('Session check error:', error)
    return NextResponse.json(
      { error: 'Session check failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 