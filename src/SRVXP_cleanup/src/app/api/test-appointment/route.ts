import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

function createResponse(data: any, status: number = 200) {
  return NextResponse.json(data, { status })
}

export async function POST(request: NextRequest) {
  console.log('=== Test Appointment API - POST route called ===')
  
  try {
    // Simple JSON parsing test
    console.log('Parsing request data...')
    const requestData = await request.json()
    console.log('Successfully parsed request data:', requestData)

    // Get authenticated user
    const supabase = await createClient()
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return createResponse({ error: 'Authentication required' }, 401)
    }

    console.log('User authenticated:', user.id)

    // Extract appointment data
    const {
      patientFirstName,
      patientLastName,
      patientHealthCard,
      patientHealthCardSequence,
      patientDateOfBirth,
      patientPostalCode,
      appointmentDate,
      appointmentTimePreference,
      patient_id
    } = requestData

    console.log('Extracted data:', {
      patientFirstName,
      patientLastName,
      patientHealthCard: patientHealthCard ? `${patientHealthCard.substring(0, 4)}****` : null,
      patientHealthCardSequence,
      patientDateOfBirth,
      patientPostalCode,
      appointmentDate,
      appointmentTimePreference,
      patient_id
    })

    // Simple validation
    if (!patientFirstName || !patientLastName || !patientHealthCard) {
      return createResponse({ error: 'Missing required fields' }, 400)
    }

    // Insert into database
    const appointmentRequest = {
      user_id: user.id,
      patient_id: patient_id || null,
      status: 'pending',
      patient_first_name: patientFirstName,
      patient_last_name: patientLastName,
      patient_health_card: patientHealthCard,
      patient_health_card_sequence: patientHealthCardSequence,
      patient_date_of_birth: patientDateOfBirth,
      patient_postal_code: patientPostalCode,
      appointment_date: appointmentDate,
      appointment_time_preference: appointmentTimePreference
    }

    console.log('Inserting appointment request...')
    const { data, error } = await supabase
      .from('appointment_requests')
      .insert(appointmentRequest)
      .select()
      .single()

    if (error) {
      console.error('Database error:', error)
      return createResponse({ error: 'Failed to create appointment request', details: error.message }, 500)
    }

    console.log('Appointment created successfully:', data.id)
    return createResponse({ success: true, data })

  } catch (error) {
    console.error('Unexpected error:', error)
    return createResponse({ error: 'An unexpected error occurred', details: error instanceof Error ? error.message : 'Unknown error' }, 500)
  }
}
