import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createClient } from '@/lib/supabase/server'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
})

export async function GET(request: NextRequest) {
  try {
    console.log('=== PORTAL SESSION DEBUG START ===')
    
    // Extract locale from URL parameters
    const { searchParams } = new URL(request.url)
    const locale = searchParams.get('locale')
    console.log('Requested locale:', locale)
    
    // Create Supabase client with server-side cookie handling
    const supabase = await createClient()
    
    // Debug headers
    const authHeader = request.headers.get('authorization')
    console.log('Request headers:', {
      hasAuthHeader: !!authHeader,
      authHeaderType: authHeader ? authHeader.substring(0, 20) + '...' : 'none',
      cookies: request.headers.get('cookie') ? 'present' : 'none',
    })
    
    // Try to get user from session token in Authorization header if cookies don't work
    let user = null
    let authError = null

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      console.log('Attempting auth with Bearer token...')
      const { data, error } = await supabase.auth.getUser(token)
      user = data?.user
      authError = error
      console.log('Bearer auth result:', { hasUser: !!user, error: error?.message })
    } else {
      // Fall back to cookie-based auth
      console.log('Attempting auth with cookies...')
      const { data, error } = await supabase.auth.getUser()
      user = data?.user
      authError = error
      console.log('Cookie auth result:', { hasUser: !!user, error: error?.message })
    }
    
    if (authError || !user) {
      console.log('Authentication failed:', { authError: authError?.message, hasAuthHeader: !!authHeader })
      return NextResponse.json(
        { error: 'Authentication required', details: 'No valid session found' },
        { status: 401 }
      )
    }

    console.log('User authenticated:', { userId: user.id, email: user.email })

    let customerId: string | null = null

    // First, try to get the user's subscription to find their Stripe customer ID
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('customer_id, status, plan_type, billing_period')
      .eq('user_id', user.id)

    console.log('Subscription query result:', { 
      hasSubscriptions: !!subscription, 
      count: subscription?.length || 0,
      error: subscriptionError?.message 
    })

    if (subscription && subscription.length > 0) {
      // Find active subscription or most recent one
      const activeSubscription = subscription.find(sub => sub.status === 'active') || subscription[0]
      customerId = activeSubscription.customer_id
      console.log('Found customer_id from subscription:', customerId)
    }

    // If no subscription in database, try to find Stripe customer by email
    if (!customerId && user.email) {
      console.log('No subscription found in database, searching Stripe customers by email:', user.email)
      
      try {
        const customers = await stripe.customers.list({
          email: user.email,
          limit: 1,
        })

        if (customers.data.length > 0) {
          customerId = customers.data[0].id
          console.log('Found Stripe customer by email:', customerId)
        } else {
          console.log('No Stripe customer found for email:', user.email)
        }
      } catch (stripeError) {
        console.error('Error searching Stripe customers:', stripeError)
      }
    }

    // If still no customer ID, create a new customer
    if (!customerId && user.email) {
      console.log('Creating new Stripe customer for:', user.email)
      
      try {
        const customer = await stripe.customers.create({
          email: user.email,
          metadata: {
            supabase_user_id: user.id,
          },
        })
        customerId = customer.id
        console.log('Created new Stripe customer:', customerId)
      } catch (stripeError) {
        console.error('Error creating Stripe customer:', stripeError)
        return NextResponse.json(
          { error: 'Failed to create customer', details: 'Could not create or find Stripe customer' },
          { status: 500 }
        )
      }
    }

    if (!customerId) {
      return NextResponse.json(
        { error: 'No customer found', details: 'Unable to find or create Stripe customer' },
        { status: 404 }
      )
    }

    console.log('Creating portal session for customer:', customerId)

    // Create Stripe billing portal session
    try {
      // Prepare portal session options
      const portalOptions: any = {
        customer: customerId,
        return_url: `${process.env.NEXT_PUBLIC_APP_URL}/compte/abonnement`,
      }
      
      // Add locale if provided and valid
      if (locale && (locale === 'fr' || locale === 'en')) {
        portalOptions.locale = locale
        console.log('Setting Stripe portal locale to:', locale)
      }

      const session = await stripe.billingPortal.sessions.create(portalOptions)

      console.log('Portal session created successfully')
      return NextResponse.json({ url: session.url })
    } catch (stripeError: any) {
      console.error('Stripe portal session error:', stripeError)
      
      // Handle specific Stripe configuration error
      if (stripeError.type === 'StripeInvalidRequestError' && 
          stripeError.message?.includes('No configuration provided')) {
        return NextResponse.json(
          { 
            error: 'Stripe Customer Portal not configured', 
            details: 'The Stripe Customer Portal needs to be configured in your Stripe dashboard. Please contact support.',
            helpUrl: 'https://dashboard.stripe.com/test/settings/billing/portal'
          },
          { status: 503 }
        )
      }
      
      // Generic Stripe error
      return NextResponse.json(
        { error: 'Stripe error', details: stripeError.message || 'Unknown Stripe error' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Error creating portal session:', error)
    console.log('=== PORTAL SESSION DEBUG END ===')
    return NextResponse.json(
      { error: 'Failed to create portal session', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
} 