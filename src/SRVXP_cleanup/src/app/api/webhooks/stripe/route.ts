import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createClient } from '@/lib/supabase/client'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-03-31.basil',
})

const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = request.headers.get('stripe-signature')!

  let event: Stripe.Event

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret)
  } catch (err) {
    console.error('Webhook signature verification failed:', err)
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    )
  }

  const supabase = createClient()

  try {
    // Log the webhook event to database
    await supabase.from('webhook_events').insert({
      event_type: event.type,
      type: 'stripe',
      stripe_event_id: event.id,
      data: event.data,
    })

    // Handle different event types
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object as Stripe.Subscription)
        break

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object as Stripe.Subscription)
        break

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object as Stripe.Invoice)
        break

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object as Stripe.Invoice)
        break

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

async function handleSubscriptionCreated(subscription: Stripe.Subscription) {
  const supabase = createClient()

  // Extract metadata
  const metadata = subscription.metadata || {}
  const userId = metadata.user_id

  if (!userId) {
    console.error('No user_id in subscription metadata')
    return
  }

  // Get plan details from the subscription
  const priceId = subscription.items.data[0]?.price.id
  const planType = determinePlanType(priceId)
  const billingPeriod = subscription.items.data[0]?.price.recurring?.interval === 'year' ? 'annual' : 'monthly'

  try {
    const { error } = await supabase.from('subscriptions').insert({
      stripe_id: subscription.id,
      user_id: userId,
      customer_id: subscription.customer as string,
      price_id: priceId,
      stripe_price_id: priceId,
      plan_type: planType,
      billing_period: billingPeriod,
      status: subscription.status,
      current_period_start: subscription.current_period_start,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
      amount: subscription.items.data[0]?.price.unit_amount || 0,
      currency: subscription.items.data[0]?.price.currency || 'cad',
      interval: subscription.items.data[0]?.price.recurring?.interval || 'month',
      started_at: subscription.start_date || subscription.created,
      metadata: subscription.metadata,
    })

    if (error) {
      console.error('Error creating subscription:', error)
    }
  } catch (error) {
    console.error('Error in handleSubscriptionCreated:', error)
  }
}

async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const supabase = createClient()

  try {
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: subscription.status,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        cancel_at_period_end: subscription.cancel_at_period_end,
        canceled_at: subscription.canceled_at,
        ended_at: subscription.ended_at,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_id', subscription.id)

    if (error) {
      console.error('Error updating subscription:', error)
    } else {
      console.log(`Subscription ${subscription.id} updated successfully`)
    }
  } catch (error) {
    console.error('Error in handleSubscriptionUpdated:', error)
  }
}

async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const supabase = createClient()

  try {
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'cancelled',
        ended_at: subscription.ended_at || Math.floor(Date.now() / 1000),
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_id', subscription.id)

    if (error) {
      console.error('Error deleting subscription:', error)
    } else {
      console.log(`Subscription ${subscription.id} deleted successfully`)
    }
  } catch (error) {
    console.error('Error in handleSubscriptionDeleted:', error)
  }
}

async function handlePaymentSucceeded(invoice: Stripe.Invoice) {
  // Handle successful payment - could update subscription status, send confirmation emails, etc.
  console.log(`Payment succeeded for invoice ${invoice.id}`)
}

async function handlePaymentFailed(invoice: Stripe.Invoice) {
  // Handle failed payment - could update subscription status, send notification emails, etc.
  console.log(`Payment failed for invoice ${invoice.id}`)
}

function determinePlanType(priceId: string | null): 'individual' | 'family' | 'free' {
  if (!priceId) return 'free'
  
  // Map price IDs to plan types based on your pricing configuration
  const individualPrices = [
    'price_1R0CkxElTneJ74CJARjKlS9U', // Individual Monthly
    'price_1REN04ElTneJ74CJVejJzVDH'   // Individual Annual
  ]
  
  const familyPrices = [
    'price_1R0ClKElTneJ74CJO45trpKh', // Family Monthly
    'price_1REN0TElTneJ74CJDRnv2MXk'    // Family Annual
  ]

  if (individualPrices.includes(priceId)) {
    return 'individual'
  } else if (familyPrices.includes(priceId)) {
    return 'family'
  }

  return 'free'
} 