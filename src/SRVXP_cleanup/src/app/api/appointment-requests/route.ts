import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Helper function to handle API responses safely
function createResponse(data: any, status: number = 200) {
  try {
    return NextResponse.json(data, { status })
  } catch (error) {
    console.error('Error creating response:', error)
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    })
  }
}

export async function POST(request: NextRequest) {
  console.log('=== Appointment Request API - POST route called ===')
  
  try {
    console.log('Creating Supabase client...')
    const supabase = await createClient()

    // Get the current user with error handling
    console.log('Getting current user...')
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError) {
      console.error('Error getting user:', userError)
      return createResponse(
        { error: 'Authentication failed', errorType: 'authentication_error' },
        401
      )
    }

    console.log('User authentication check:', { userId: user?.id, userEmail: user?.email })

    if (!user) {
      console.log('Authentication failed - no user found')
      return createResponse(
        { error: 'Authentication required', errorType: 'authentication_required' },
        401
      )
    }

    // Check if user has an active subscription
    console.log('Checking user subscription...')
    const { data: subscriptions, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .in('plan_type', ['individual', 'family'])
      .maybeSingle()

    console.log('Subscription check result:', { subscriptions, subscriptionError })

    if (subscriptionError) {
      console.error('Error checking subscription:', subscriptionError)
      return createResponse(
        { error: 'Error checking subscription status', errorType: 'subscription_error' },
        500
      )
    }

    if (!subscriptions) {
      console.log('No active subscription found')
      return createResponse(
        { error: 'Subscription required to create appointment requests', errorType: 'no_subscription' },
        403
      )
    }

    // Parse request body with simple error handling
    console.log('Parsing request data...')
    let requestData
    try {
      requestData = await request.json()
      console.log('Successfully parsed request data')

      if (!requestData || typeof requestData !== 'object') {
        console.error('Invalid request data format:', typeof requestData)
        return createResponse(
          { error: 'Invalid request data format' },
          400
        )
      }
    } catch (error) {
      console.error('Error parsing request JSON:', error)
      return createResponse(
        { error: 'Invalid JSON format in request body' },
        400
      )
    }
    
    console.log('Request data received:', requestData)

    // Extract appointment data from the new direct format
    const {
      patientFirstName,
      patientLastName,
      patientHealthCard,
      patientHealthCardSequence,
      patientDateOfBirth,
      patientPostalCode,
      appointmentDate,
      appointmentTimePreference,
      patient_id
    } = requestData

    console.log('Processing appointment request with direct format')
    console.log('Patient data:', {
      patientFirstName,
      patientLastName,
      patientHealthCard: patientHealthCard ? `${patientHealthCard.substring(0, 4)}****` : null,
      patientHealthCardSequence,
      patientDateOfBirth,
      patientPostalCode,
      appointmentDate,
      appointmentTimePreference,
      patient_id
    })

    // Validate required fields
    const missingFields = []
    if (!patientFirstName) missingFields.push('patientFirstName')
    if (!patientLastName) missingFields.push('patientLastName')
    if (!patientHealthCard) missingFields.push('patientHealthCard')
    if (!patientHealthCardSequence) missingFields.push('patientHealthCardSequence')
    if (!patientPostalCode) missingFields.push('patientPostalCode')
    if (!appointmentDate) missingFields.push('appointmentDate')
    if (!appointmentTimePreference) missingFields.push('appointmentTimePreference')

    if (missingFields.length > 0) {
      console.error('Missing required fields:', missingFields)
      return createResponse(
        { error: `Missing required fields: ${missingFields.join(', ')}` },
        400
      )
    }

    // Validate health card format (Quebec format: AAAA12341234)
    if (!/^[A-Z]{4}[0-9]{8}$/.test(patientHealthCard)) {
      console.error('Invalid health card format:', patientHealthCard)
      return createResponse(
        { error: 'Invalid health card format. Expected format: AAAA12341234' },
        400
      )
    }

    // Validate health card sequence format (2 digits)
    if (!/^[0-9]{2}$/.test(patientHealthCardSequence)) {
      console.error('Invalid health card sequence format:', patientHealthCardSequence)
      return createResponse(
        { error: 'Invalid health card sequence format. Expected 2 digits.' },
        400
      )
    }

    // Validate postal code format (Canadian A1A 1A1)
    if (!/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(patientPostalCode)) {
      console.error('Invalid postal code format:', patientPostalCode)
      return createResponse(
        { error: 'Invalid postal code format. Expected format: A1A 1A1' },
        400
      )
    }

    // Validate appointment time preference
    const validTimePreferences = ['asap', 'morning', 'afternoon', 'evening']
    if (!validTimePreferences.includes(appointmentTimePreference)) {
      console.error('Invalid appointment time preference:', appointmentTimePreference)
      return createResponse(
        { error: `Invalid appointment time preference. Must be one of: ${validTimePreferences.join(', ')}` },
        400
      )
    }

    // Validate appointment date (must be in the future)
    const appointmentDateObj = new Date(appointmentDate)
    const today = new Date()
    today.setHours(0, 0, 0, 0) // Reset time to start of day for comparison

    if (appointmentDateObj < today) {
      console.error('Appointment date is in the past:', appointmentDate)
      return createResponse(
        { error: 'Appointment date must be in the future' },
        400
      )
    }

    // Validate date of birth if provided
    let dateOfBirth = null
    if (patientDateOfBirth) {
      dateOfBirth = new Date(patientDateOfBirth)
      if (isNaN(dateOfBirth.getTime())) {
        console.error('Invalid date of birth format:', patientDateOfBirth)
        return createResponse(
          { error: 'Invalid date of birth format' },
          400
        )
      }
    }

    console.log('All validations passed, proceeding with database insertion')

    // Prepare data for insertion with explicit fields only
    const appointmentRequest = {
      user_id: user.id,
      patient_id: patient_id || null,
      status: 'pending',

      // Explicit fields for worker convenience
      patient_first_name: patientFirstName,
      patient_last_name: patientLastName,
      patient_health_card: patientHealthCard,
      patient_health_card_sequence: patientHealthCardSequence,
      patient_date_of_birth: patientDateOfBirth,
      patient_postal_code: patientPostalCode,
      appointment_date: appointmentDate,
      appointment_time_preference: appointmentTimePreference
    }

    console.log('Inserting appointment request into database...')
    // Insert the appointment request
    const { data, error } = await supabase
      .from('appointment_requests')
      .insert(appointmentRequest)
      .select()
      .single()

    if (error) {
      console.error('Error creating appointment request:', error)

      // Handle specific errors
      if (error.message.includes('Free users cannot create appointment requests')) {
        return createResponse(
          { error: 'Subscription required to create appointment requests', errorType: 'no_subscription' },
          403
        )
      }

      if (error.message.includes('Only family plan members can book for others')) {
        return createResponse(
          { error: 'Family plan required to book for family members' },
          403
        )
      }

      if (error.message.includes('Patient does not belong to this user')) {
        return createResponse(
          { error: 'Invalid patient selection' },
          400
        )
      }

      if (error.message.includes('patient_postal_code_format')) {
        return createResponse(
          { error: 'Invalid postal code format' },
          400
        )
      }

      if (error.message.includes('patient_health_card_format')) {
        return createResponse(
          { error: 'Invalid health card format' },
          400
        )
      }

      if (error.message.includes('patient_health_card_sequence_format')) {
        return createResponse(
          { error: 'Invalid health card sequence format' },
          400
        )
      }

      return createResponse(
        { error: 'Failed to create appointment request' },
        500
      )
    }

    console.log('Appointment request created successfully:', data?.id)
    return createResponse({ success: true, data })

  } catch (error) {
    console.error('Unexpected error creating appointment request:', error)
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('timeout') || error.message.includes('aborted')) {
        return createResponse(
          { error: 'Request timed out. Please try again.' },
          408
        )
      }
      
      if (error.message.includes('Stream is already ended') || error.message.includes('ConnResetException')) {
        return createResponse(
          { error: 'Connection error. Please try again.' },
          503
        )
      }
    }
    
    return createResponse(
      { error: 'An unexpected error occurred' },
      500
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('=== Appointment Request API - GET route called ===')
    const supabase = await createClient()

    // Get the current user with error handling
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError) {
      console.error('Error getting user:', userError)
      return createResponse(
        { error: 'Authentication failed' },
        401
      )
    }

    if (!user) {
      return createResponse(
        { error: 'Authentication required' },
        401
      )
    }

    // Get query parameters
    const url = new URL(request.url)
    const status = url.searchParams.get('status')
    const limit = parseInt(url.searchParams.get('limit') || '10')

    // Build query
    let query = supabase
      .from('appointment_requests')
      .select(`
        *,
        family_members(first_name, last_name, health_card, birth_date)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(limit)

    // Add status filter if provided
    if (status) {
      query = query.eq('status', status)
    }

    console.log('Executing query with parameters:', { status, limit, userId: user.id })

    // Execute query
    const { data, error } = await query

    if (error) {
      console.error('Error fetching appointment requests:', error)
      return createResponse(
        { error: 'Failed to fetch appointment requests' },
        500
      )
    }

    console.log(`Successfully fetched ${data?.length || 0} appointment requests`)
    return createResponse({ data })

  } catch (error) {
    console.error('Unexpected error fetching appointment requests:', error)
    
    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('timeout') || error.message.includes('aborted')) {
        return createResponse(
          { error: 'Request timed out. Please try again.' },
          408
        )
      }
      
      if (error.message.includes('Stream is already ended') || error.message.includes('ConnResetException')) {
        return createResponse(
          { error: 'Connection error. Please try again.' },
          503
        )
      }
    }
    
    return createResponse(
      { error: 'An unexpected error occurred' },
      500
    )
  }
}
