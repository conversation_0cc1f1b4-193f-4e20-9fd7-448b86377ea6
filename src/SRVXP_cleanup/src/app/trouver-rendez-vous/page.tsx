"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Calendar as CalendarI<PERSON>, Clock, Info, User, CheckCircle } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { cn, formatHealthCardNumber, isHealthCardComplete } from "@/lib/utils"
import { format, isBefore, startOfDay } from "date-fns"
import { fr } from "date-fns/locale"
import { useF<PERSON>ly<PERSON><PERSON>bers, FamilyMember } from "@/lib/FamilyMembersContext"
import { useAuth } from "@/lib/AuthContext"
import Image from "next/image"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"
import { useTranslation } from "@/components/t"
import { createAppointmentRequest } from "@/lib/appointment-requests/api"
import { useSubscriptionDetails } from "@/stores/useSubscriptionStore"

// Key for localStorage to persist form data
const FORM_DATA_KEY = "trouver-rendez-vous-form-data"

interface FormData {
  date?: string
  time: string[]
  postalCode: string
  selectedMember: string
  healthCardLastDigits: string
  cardSequenceNumber: string
}

export default function TrouverRendezVousPage() {
  const { familyMembers } = useFamilyMembers()
  const { user, status } = useAuth()
  const subscriptionDetails = useSubscriptionDetails()
  const language = user?.user_metadata?.language || "fr"
  const [date, setDate] = useState<Date>()
  const [time, setTime] = useState<string[]>([])
  const [postalCode, setPostalCode] = useState<string>("")
  const [postalError, setPostalError] = useState<string>("")
  const [postalWarning, setPostalWarning] = useState<string>("")
  const [today, setToday] = useState<Date>(startOfDay(new Date()))
  const [selectedMember, setSelectedMember] = useState<string>("")
  const [healthCardLastDigits, setHealthCardLastDigits] = useState<string>("")
  const [cardSequenceNumber, setCardSequenceNumber] = useState<string>("")
  const [selectedFamilyMember, setSelectedFamilyMember] = useState<FamilyMember | null>(null)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const { t } = useTranslation()

  // Add submission states
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [submitError, setSubmitError] = useState("")

  // Debug effect to track re-renders and auth status changes
  useEffect(() => {
    console.log('TrouverRendezVous: Component re-rendered', {
      authStatus: status,
      userId: user?.id,
      familyMembersCount: familyMembers.length,
      hasFormData: !!(date || time.length > 0 || postalCode || selectedMember || healthCardLastDigits || cardSequenceNumber)
    })
  }, [status, user?.id, familyMembers.length, date, time.length, postalCode, selectedMember, healthCardLastDigits, cardSequenceNumber])
  
  // Debug effect specifically for auth status changes
  useEffect(() => {
    console.log('TrouverRendezVous: Auth status changed to:', status)
  }, [status])

  // Save form data to localStorage
  const saveFormData = (data: Partial<FormData>) => {
    if (typeof window === 'undefined') return
    
    try {
      const existingData = JSON.parse(localStorage.getItem(FORM_DATA_KEY) || '{}')
      const updatedData = { ...existingData, ...data }
      localStorage.setItem(FORM_DATA_KEY, JSON.stringify(updatedData))
      console.log('Form data saved to localStorage:', updatedData)
    } catch (error) {
      console.warn('Failed to save form data to localStorage:', error)
    }
  }

  // Load form data from localStorage
  const loadFormData = (): FormData | null => {
    if (typeof window === 'undefined') return null
    
    try {
      const data = localStorage.getItem(FORM_DATA_KEY)
      const parsedData = data ? JSON.parse(data) : null
      if (parsedData) {
        console.log('Form data loaded from localStorage:', parsedData)
      }
      return parsedData
    } catch (error) {
      console.warn('Failed to load form data from localStorage:', error)
      return null
    }
  }

  // Clear form data from localStorage
  const clearFormData = () => {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.removeItem(FORM_DATA_KEY)
      console.log('Form data cleared from localStorage')
    } catch (error) {
      console.warn('Failed to clear form data from localStorage:', error)
    }
  }

  // Load persisted form data on component mount
  useEffect(() => {
    const savedData = loadFormData()
    if (savedData) {
      // Restore form fields from localStorage
      if (savedData.date) {
        try {
          setDate(new Date(savedData.date))
        } catch (error) {
          console.warn('Failed to parse saved date:', error)
        }
      }
      if (savedData.time) setTime(Array.isArray(savedData.time) ? savedData.time : [savedData.time].filter(Boolean))
      if (savedData.postalCode) setPostalCode(savedData.postalCode)
      if (savedData.selectedMember) setSelectedMember(savedData.selectedMember)
      if (savedData.healthCardLastDigits) setHealthCardLastDigits(savedData.healthCardLastDigits)
      if (savedData.cardSequenceNumber) setCardSequenceNumber(savedData.cardSequenceNumber)
    }
  }, [])

  // Format postal code to match "A1A 1A1" pattern
  const formatPostalCode = (value: string) => {
    // Remove non-alphanumeric characters
    let formatted = value.replace(/[^a-zA-Z0-9]/g, "").toUpperCase()

    // Limit to 6 characters
    formatted = formatted.slice(0, 6)

    // Insert space after the 3rd character
    if (formatted.length > 3) {
      formatted = formatted.slice(0, 3) + " " + formatted.slice(3)
    }

    // Check if postal code is valid and complete (A1A 1A1)
    const isFullyValidPostalCode = /^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(formatted)

    // Show warning if not fully valid and has at least one character
    if (formatted.length > 0 && !isFullyValidPostalCode) {
      setPostalWarning(t(translationKeys.findAppointment.postalFormatWarning))
    } else {
      setPostalWarning("")
    }

    // Only set error for form validation if the field is filled but invalid
    if (formatted.length > 0 && !isFullyValidPostalCode && formatted.length >= 7) {
      setPostalError(t(translationKeys.findAppointment.invalidPostalFormat))
    } else {
      setPostalError("")
    }

    return formatted
  }

  const handlePostalCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPostalCode(e.target.value)
    setPostalCode(formatted)
    saveFormData({ postalCode: formatted })
  }

  const handleHealthCardLastDigitsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatHealthCardNumber(e.target.value, 8);
    setHealthCardLastDigits(formatted);
    saveFormData({ healthCardLastDigits: formatted })

    // Clear error when user types
    if (formErrors.healthCardLastDigits && isHealthCardComplete(formatted)) {
      setFormErrors({...formErrors, healthCardLastDigits: ""});
    }
  }

  const handleSequenceNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Keep only digits, max 2
    const onlyDigits = e.target.value.replace(/\D/g, "").substring(0, 2)
    setCardSequenceNumber(onlyDigits)
    saveFormData({ cardSequenceNumber: onlyDigits })
  }

  const handleDateChange = (newDate: Date | undefined) => {
    setDate(newDate)
    saveFormData({ date: newDate ? newDate.toISOString() : undefined })
    if (formErrors.date) {
      const newErrors = {...formErrors}
      delete newErrors.date
      setFormErrors(newErrors)
    }
  }

  const handleTimeChange = (timeOption: string, checked: boolean) => {
    const asapOption = t(translationKeys.findAppointment.asap)
    let newTime: string[] = []

    if (timeOption === asapOption) {
      // If ASAP is selected, clear all other options and only keep ASAP
      newTime = checked ? [asapOption] : []
    } else {
      // For other options, first remove ASAP if it was selected
      const currentTimeWithoutAsap = time.filter(t => t !== asapOption)
      
      if (checked) {
        // Add the new option
        newTime = [...currentTimeWithoutAsap, timeOption]
      } else {
        // Remove the option
        newTime = currentTimeWithoutAsap.filter(t => t !== timeOption)
      }
    }

    setTime(newTime)
    saveFormData({ time: newTime })
    if (formErrors.time) {
      const newErrors = {...formErrors}
      delete newErrors.time
      setFormErrors(newErrors)
    }
  }

  const handleSelectedMemberChange = (value: string) => {
    setSelectedMember(value)
    saveFormData({ selectedMember: value })
    if (formErrors.selectedMember) {
      const newErrors = {...formErrors}
      delete newErrors.selectedMember
      setFormErrors(newErrors)
    }
  }

  // Update selected family member when the id changes
  useEffect(() => {
    if (selectedMember) {
      const member = familyMembers.find(m => m.id.toString() === selectedMember)
      setSelectedFamilyMember(member || null)

      // Reset health card last digits and sequence number when changing person
      setHealthCardLastDigits("")
      setCardSequenceNumber("")
      saveFormData({ healthCardLastDigits: "", cardSequenceNumber: "" })

      // Clear related errors
      const newErrors = {...formErrors}
      delete newErrors.healthCardLastDigits
      setFormErrors(newErrors)
    } else {
      setSelectedFamilyMember(null)
    }
  }, [selectedMember, familyMembers])

  // Validate the form and return true if valid
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    if (!date) {
      errors.date = t(translationKeys.findAppointment.selectDateError)
    }

    if (time.length === 0) {
      errors.time = t(translationKeys.findAppointment.selectTimeError)
    }

    if (!postalCode) {
      errors.postalCode = t(translationKeys.findAppointment.enterPostalError)
    } else if (postalError) {
      errors.postalCode = postalError
    } else if (!/^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(postalCode)) {
      errors.postalCode = t(translationKeys.findAppointment.invalidPostalError)
    }

    if (!selectedMember) {
      errors.selectedMember = t(translationKeys.findAppointment.selectPersonError)
    }

    // Validate health card info if a family member is selected
    if (selectedMember && !isHealthCardComplete(healthCardLastDigits)) {
      errors.healthCardLastDigits = t(translationKeys.findAppointment.healthCardDigitsError);
    }

    if (selectedMember && cardSequenceNumber.length !== 2) {
      errors.cardSequenceNumber = t(translationKeys.findAppointment.sequenceNumberError);
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Implement the form submission function
  async function handleSearch(e: React.FormEvent) {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setSubmitError("")

    try {
      console.log('Form submission started with selectedMember:', selectedMember)
      console.log('Available family members:', familyMembers.map(m => ({ id: m.id, name: `${m.lastName}, ${m.firstName}` })))

      // Find the selected family member using the selectedMember ID
      const selectedFamilyMemberData = familyMembers.find(m => m.id.toString() === selectedMember)
      
      if (!selectedFamilyMemberData) {
        console.error('Selected family member not found. selectedMember:', selectedMember)
        console.error('Available family member IDs:', familyMembers.map(m => m.id.toString()))
        setSubmitError(`Membre de famille sélectionné introuvable. Veuillez sélectionner à nouveau une personne dans la liste.`)
        return
      }

      if (!user?.id) {
        setSubmitError("Utilisateur non authentifié. Veuillez vous reconnecter.")
        return
      }
      
      console.log('Selected family member:', selectedFamilyMemberData)
      
      // Parse name from family member selection (following the user's example format: "last_name, first_name")
      const patientFirstName = selectedFamilyMemberData.firstName || ""
      const patientLastName = selectedFamilyMemberData.lastName || ""
      
      console.log('Patient names:', { patientFirstName, patientLastName })

      // Build complete health card number by combining prefix and last digits
      // From the user's example: JETA23532523 (4 prefix + 8 digits) = JETA + 23532523
      const healthCardPrefix = selectedFamilyMemberData.healthCard || ""
      const healthCardLastDigits8 = healthCardLastDigits.replace('-', '').replace(/\D/g, '') // Remove any non-digits
      const completeHealthCard = healthCardPrefix + healthCardLastDigits8

      console.log('Health card info:', { 
        prefix: healthCardPrefix, 
        lastDigits: healthCardLastDigits8, 
        complete: completeHealthCard 
      })

      // Validate health card format
      if (completeHealthCard.length !== 12) {
        setSubmitError(`Format de carte d'assurance maladie invalide. La carte complète doit avoir 12 caractères (${completeHealthCard.length} caractères détectés).`)
        return
      }

      // Convert French time preferences to English for the database
      const timePreferenceMap: { [key: string]: string } = {
        'Dès que possible': 'asap',
        'As soon as possible': 'asap',
        'Matinée': 'morning',
        'Morning': 'morning',
        'Après-midi': 'afternoon',
        'Afternoon': 'afternoon',
        'Soirée': 'evening',
        'Evening': 'evening'
      }

      // Convert the first time preference (most important one) to English
      const appointmentTimePreference = time.length > 0 
        ? timePreferenceMap[time[0]] || 'asap' 
        : 'asap'

      console.log('Time preference:', { original: time, mapped: appointmentTimePreference })

      // Format date for the database (ISO date format)
      const appointmentDate = date ? format(date, 'yyyy-MM-dd') : null
      
      // Get birth date from family member if available
      const patientDateOfBirth = selectedFamilyMemberData.birthDate 
        ? format(new Date(selectedFamilyMemberData.birthDate), 'yyyy-MM-dd')
        : null
      
      console.log('Dates:', { appointmentDate, patientDateOfBirth })

      if (!appointmentDate) {
        setSubmitError("Date de rendez-vous requise.")
        return
      }
      
      // Prepare request data using the new API format
      const appointmentData = {
        patientFirstName,
        patientLastName,
        patientHealthCard: completeHealthCard,
        patientHealthCardSequence: cardSequenceNumber,
        patientDateOfBirth,
        patientPostalCode: postalCode,
        appointmentDate,
        appointmentTimePreference
      }

      console.log('Final appointment data:', appointmentData)

      // Determine patient_id based on subscription plan
      // For individual plans: patient_id should be null (self-appointment)
      // For family plans: patient_id should be the family member's ID
      let patientId: string | null = null

      if (subscriptionDetails.planType === 'family') {
        // Family plan users can book for family members
        patientId = selectedFamilyMemberData.supabaseId || selectedMember
      } else {
        // Individual plan users can only book for themselves (patient_id = null)
        patientId = null
      }

      console.log('Subscription plan:', subscriptionDetails.planType)
      console.log('Using patient ID:', patientId, 'User ID:', user.id)

      // Call the new direct API
      const result = await createAppointmentRequest(user.id, patientId, appointmentData)

      console.log('API result:', result)

      if (result.success) {
        setSubmitSuccess(true)
        // Clear form data from localStorage
        localStorage.removeItem(FORM_DATA_KEY)
        // Reset form
        setDate(undefined)
        setTime([])
        setPostalCode("")
        setSelectedMember("")
        setHealthCardLastDigits("")
        setCardSequenceNumber("")
        setFormErrors({})
      } else {
        let errorMessage = result.error || "Une erreur est survenue lors de l'envoi de votre demande."
        
        // Handle specific database policy errors
        if (errorMessage.includes('Only family plan members can book for others')) {
          errorMessage = language === 'fr' 
            ? "Votre plan actuel ne permet pas de réserver des rendez-vous. Veuillez contacter le support ou mettre à niveau votre plan."
            : "Your current plan does not allow booking appointments. Please contact support or upgrade your plan."
        }
        
        console.error('API error:', errorMessage)
        setSubmitError(errorMessage)
      }
    } catch (error) {
      console.error('Error submitting appointment request:', error)
      const errorMessage = error instanceof Error ? error.message : "Une erreur est survenue lors de l'envoi de votre demande. Veuillez réessayer."
      setSubmitError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const disablePastDates = (date: Date) => {
    return isBefore(date, today)
  }

  // Get valid family members (non-empty first or last name)
  const validFamilyMembers = familyMembers.filter(
    member => member.firstName || member.lastName
  )

  // Button disabled state
  const isFormValid = (): boolean => {
    // Check if postal code matches the A1A 1A1 format
    const isValidPostalCode = /^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$/.test(postalCode);

    return !!(
      date &&
      time.length > 0 &&
      isValidPostalCode &&
      selectedMember &&
      (selectedMember ? isHealthCardComplete(healthCardLastDigits) && cardSequenceNumber.length === 2 : true)
    )
  }

  // Add success message display logic
  if (submitSuccess) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <Card>
            <CardContent className="p-8">
              <div className="flex flex-col items-center text-center space-y-6">
                <div className="h-16 w-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-10 w-10 text-green-600" />
                </div>
                <div className="space-y-2">
                  <h2 className="text-2xl font-bold text-green-800">
                    <T keyName={translationKeys.findAppointment.thankYou} />
                  </h2>
                  <p className="text-muted-foreground">
                    <T keyName={translationKeys.findAppointment.confirmationMessage} />
                  </p>
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/mes-rendez-vous">
                    <Button variant="default">
                      <T keyName={translationKeys.findAppointment.viewRequests} />
                    </Button>
                  </Link>
                  <Button 
                    variant="outline" 
                    onClick={() => setSubmitSuccess(false)}
                  >
                    Nouvelle demande
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">
            <T keyName={translationKeys.findAppointment.title} />
          </h1>
          <p className="text-muted-foreground">
            <T keyName={translationKeys.findAppointment.description} />
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              <T keyName={translationKeys.findAppointment.searchCriteria} />
            </CardTitle>
            <CardDescription>
              <T keyName={translationKeys.findAppointment.requiredFields} />
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSearch} className="space-y-6">
              {/* Show error message if submission failed */}
              {submitError && (
                <div className="p-4 border border-red-200 rounded-md bg-red-50">
                  <p className="text-sm text-red-600">{submitError}</p>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="family-member">
                  <T keyName={translationKeys.findAppointment.appointmentFor} />
                </Label>
                <Select
                  value={selectedMember}
                  onValueChange={handleSelectedMemberChange}
                >
                  <SelectTrigger className={cn("w-full", formErrors.selectedMember && "border-red-500")}>
                    <SelectValue placeholder={t(translationKeys.findAppointment.selectPerson)} />
                  </SelectTrigger>
                  <SelectContent>
                    {validFamilyMembers.map((member) => (
                      <SelectItem key={member.id} value={member.id.toString()}>
                        <div className="flex items-center gap-2">
                          <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                            <User className="h-3 w-3 text-blue-700" />
                          </div>
                          <span>{member.lastName}{member.lastName && member.firstName ? ', ' : ''}{member.firstName}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formErrors.selectedMember && (
                  <p className="text-xs text-red-500">{formErrors.selectedMember}</p>
                )}
                <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                  <Info className="h-3 w-3" />
                  <span>
                    {language === "fr" ? (
                      <>
                        Les personnes dans cette liste sont gérées dans la section <Link href="/compte/utilisateurs" className="text-primary hover:underline">"Gérer les utilisateurs du compte"</Link>
                      </>
                    ) : (
                      <>
                        The people listed here are managed in the <Link href="/compte/utilisateurs" className="text-primary hover:underline">"Manage account users"</Link> section
                      </>
                    )}
                  </span>
                </p>
              </div>

              {selectedFamilyMember && (
                <div className="space-y-4 p-4 border rounded-md bg-muted/40">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center">
                      <User className="h-3 w-3 text-blue-700" />
                    </div>
                    <h3 className="font-medium">
                      <T keyName={translationKeys.findAppointment.healthCardOf} /> {selectedFamilyMember.firstName}
                    </h3>
                  </div>

                  <div className="grid gap-6 sm:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="health-card-last-digits">
                        <T keyName={translationKeys.findAppointment.lastDigits} />
                      </Label>
                      <div className="flex items-center gap-1">
                        <div className="bg-muted rounded px-2 py-1.5 font-mono text-sm border">
                          {selectedFamilyMember.healthCard}
                        </div>
                        <Input
                          id="health-card-last-digits"
                          placeholder="xxxx-xxxx"
                          value={healthCardLastDigits}
                          onChange={handleHealthCardLastDigitsChange}
                          required={!!selectedMember}
                          className={`flex-grow font-mono ${formErrors.healthCardLastDigits ? "border-red-500" : healthCardLastDigits.length > 0 && healthCardLastDigits.length < 8 ? "border-orange-500" : ""}`}
                        />
                      </div>
                      {formErrors.healthCardLastDigits ? (
                        <p className="text-xs text-red-500">{formErrors.healthCardLastDigits}</p>
                      ) : healthCardLastDigits.length > 0 && healthCardLastDigits.length < 8 && (
                        <p className="text-xs text-orange-500">
                          <T keyName={translationKeys.findAppointment.enterEightDigits} />
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground">
                        <T keyName={translationKeys.findAppointment.format} />
                      </p>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Label htmlFor="sequence-number">
                          <T keyName={translationKeys.findAppointment.sequenceNumber} />
                        </Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-6 w-6 p-0 rounded-md">
                              <Info className="h-4 w-4 text-muted-foreground hover:text-foreground transition-colors" />
                              <span className="sr-only">
                                <T keyName={translationKeys.findAppointment.sequenceInfo} />
                              </span>
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="p-0 max-w-[450px] w-auto border-0 bg-transparent shadow-none">
                            <div className="overflow-hidden rounded-lg border bg-background shadow-lg">
                              <div className="p-2">
                                <Image
                                  src="/ramq.png"
                                  alt="Carte RAMQ"
                                  width={400}
                                  height={250}
                                  className="rounded-md"
                                />
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                      <Input
                        id="sequence-number"
                        placeholder="01-99"
                        value={cardSequenceNumber}
                        onChange={handleSequenceNumberChange}
                        required
                        className={`font-mono ${formErrors.cardSequenceNumber ? "border-red-500" : ""}`}
                      />
                      {formErrors.cardSequenceNumber ? (
                        <p className="text-xs text-red-500">{formErrors.cardSequenceNumber}</p>
                      ) : (
                        <p className="text-xs text-muted-foreground">
                          <T keyName={translationKeys.findAppointment.enterTwoDigits} />
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Birth Date Display */}
                  {selectedFamilyMember.birthDate && (
                    <div className="space-y-2">
                      <Label>
                        <T keyName={translationKeys.users.birthDate} />:
                      </Label>
                      <div className="bg-muted rounded px-2 py-1.5 font-mono text-sm border">
                        {format(selectedFamilyMember.birthDate, 'PPP', { locale: language === "fr" ? fr : undefined })}
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="space-y-1">
                <Label htmlFor="postal-code">
                  <T keyName={translationKeys.findAppointment.postalCode} />
                </Label>
                <Input
                  id="postal-code"
                  placeholder={t(translationKeys.findAppointment.postalExample)}
                  value={postalCode}
                  onChange={handlePostalCodeChange}
                  required
                  maxLength={7}
                  className={formErrors.postalCode ? "border-red-500" : postalWarning ? "border-orange-500" : ""}
                />
                {formErrors.postalCode ? (
                  <p className="text-sm text-red-500 mt-1">{formErrors.postalCode}</p>
                ) : postalWarning ? (
                  <p className="text-xs text-orange-500">{postalWarning}</p>
                ) : null}
                <p className="text-xs text-muted-foreground mt-1">
                  <T keyName={translationKeys.findAppointment.postalCodeDescription} />
                </p>
              </div>

              <div className="grid gap-6 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="date">
                    <T keyName={translationKeys.findAppointment.fromDate} />
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="date"
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !date && "text-muted-foreground",
                          formErrors.date && "border-red-500"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {date ? format(date, 'PPP', { locale: fr }) : t(translationKeys.findAppointment.selectDate)}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={date}
                        onSelect={handleDateChange}
                        initialFocus
                        locale={fr}
                        disabled={disablePastDates}
                        fromDate={today}
                      />
                    </PopoverContent>
                  </Popover>
                  {formErrors.date && (
                    <p className="text-xs text-red-500">{formErrors.date}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="time">
                    <T keyName={translationKeys.findAppointment.appointmentTime} />
                  </Label>
                  <div className={cn("space-y-3 p-4 border rounded-md", formErrors.time && "border-red-500")}>
                    {[
                      { value: t(translationKeys.findAppointment.asap), key: "asap" },
                      { value: t(translationKeys.findAppointment.morning), key: "morning" },
                      { value: t(translationKeys.findAppointment.afternoon), key: "afternoon" },
                      { value: t(translationKeys.findAppointment.evening), key: "evening" }
                    ].map((option) => {
                      const isAsap = option.key === "asap"
                      const asapSelected = time.includes(t(translationKeys.findAppointment.asap))
                      const isDisabled = !isAsap && asapSelected
                      const isChecked = time.includes(option.value)

                      return (
                        <div key={option.key} className="flex items-center space-x-3">
                          <Checkbox
                            id={`time-${option.key}`}
                            checked={isChecked}
                            disabled={isDisabled}
                            onCheckedChange={(checked) => handleTimeChange(option.value, checked as boolean)}
                            className={cn(
                              "transition-all duration-200",
                              isDisabled && "opacity-50 cursor-not-allowed"
                            )}
                          />
                          <Label
                            htmlFor={`time-${option.key}`}
                            className={cn(
                              "flex-1 text-sm font-medium leading-none peer-disabled:cursor-not-allowed transition-all duration-200",
                              isDisabled ? "text-muted-foreground opacity-50" : "cursor-pointer",
                              isChecked && !isDisabled && "text-blue-700"
                            )}
                          >
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              {option.value}
                            </div>
                          </Label>
                        </div>
                      )
                    })}
                    
                    {time.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-muted-foreground/20">
                        <div className="text-xs text-muted-foreground mb-2">
                          {language === "fr" ? "Sélections:" : "Selected:"}
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {time.map((selectedTime) => (
                            <span
                              key={selectedTime}
                              className="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-md border border-blue-200"
                            >
                              <Clock className="h-3 w-3" />
                              {selectedTime}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                  {formErrors.time && (
                    <p className="text-xs text-red-500">{formErrors.time}</p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    {language === "fr" 
                      ? "Sélectionnez une ou plusieurs préférences. 'Dès que possible' exclut les autres options."
                      : "Select one or more preferences. 'As soon as possible' excludes other options."
                    }
                  </p>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full sm:w-auto"
                size="lg"
                disabled={!isFormValid() || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-t-2 border-white mr-2"></div>
                    Envoi en cours...
                  </>
                ) : (
                  <T keyName={translationKeys.findAppointment.submitRequest} />
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
