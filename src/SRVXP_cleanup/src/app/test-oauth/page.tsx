"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestOAuthPage() {
  const { user, session, status, signInWithGoogle, signOut, refresh } = useAuth()
  const [logs, setLogs] = useState<string[]>([])
  const [cookieInfo, setCookieInfo] = useState<string>('')
  const [storageInfo, setStorageInfo] = useState<string>('')
  const [appointmentLoading, setAppointmentLoading] = useState(false)

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setLogs(prev => [...prev, `[${timestamp}] ${message}`])
    console.log(message)
  }

  const checkCookies = () => {
    if (typeof window !== 'undefined') {
      const cookies = document.cookie.split(';').map(c => c.trim()).filter(c => c)
      const authCookies = cookies.filter(c => 
        c.includes('supabase') || 
        c.includes('auth') || 
        c.includes('oauth') ||
        c.includes('session')
      )
      setCookieInfo(authCookies.join('\n') || 'No auth-related cookies found')
      addLog(`Found ${authCookies.length} auth-related cookies`)
    }
  }

  const checkStorage = () => {
    if (typeof window !== 'undefined') {
      const authKeys = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && (key.includes('auth') || key.includes('supabase') || key.includes('session'))) {
          authKeys.push(`${key}: ${localStorage.getItem(key)?.substring(0, 100)}...`)
        }
      }
      setStorageInfo(authKeys.join('\n') || 'No auth-related localStorage items found')
      addLog(`Found ${authKeys.length} auth-related localStorage items`)
    }
  }

  const handleGoogleSignIn = async () => {
    addLog('Initiating Google OAuth...')
    try {
      const result = await signInWithGoogle()
      if (result.success) {
        addLog('Google OAuth initiated successfully')
      } else {
        addLog(`Google OAuth failed: ${result.error?.message}`)
      }
    } catch (error) {
      addLog(`Error during Google OAuth: ${error}`)
    }
  }

  const handleSignOut = async () => {
    addLog('Signing out...')
    try {
      await signOut()
      addLog('Sign out successful')
    } catch (error) {
      addLog(`Error during sign out: ${error}`)
    }
  }

  const handleRefresh = async () => {
    addLog('Refreshing auth state...')
    try {
      await refresh()
      addLog('Auth state refreshed')
    } catch (error) {
      addLog(`Error refreshing auth state: ${error}`)
    }
  }

  const simulateStripeNavigation = () => {
    addLog('Simulating Stripe navigation...')
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('stripe_redirect', 'true')
      sessionStorage.setItem('external_return', 'true')
      addLog('Stripe navigation flags set')
    }
  }

  const clearLogs = () => {
    setLogs([])
  }

  const testSimpleEndpoint = async () => {
    setAppointmentLoading(true)
    addLog('🔧 Testing simple endpoint...')
    
    try {
      const testData = { message: 'Hello from test', timestamp: Date.now() }
      
      addLog('📋 Sending simple request...')
      addLog(`Request data: ${JSON.stringify(testData)}`)

      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
        addLog('❌ Simple request aborted due to timeout (15 seconds)')
      }, 15000) // Shorter timeout for simple test

      const response = await fetch('/api/test-simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      addLog(`📡 Simple response status: ${response.status}`)
      
      const responseData = await response.json()
      addLog(`📋 Simple response data: ${JSON.stringify(responseData, null, 2)}`)

      if (response.ok) {
        addLog('✅ Simple endpoint test successful!')
      } else {
        addLog(`❌ Simple endpoint test failed: ${responseData.error}`)
      }

    } catch (error: any) {
      if (error.name === 'AbortError') {
        addLog('❌ Simple request timed out')
      } else {
        addLog(`❌ Error during simple test: ${error.message}`)
        console.error('Full error:', error)
      }
    } finally {
      setAppointmentLoading(false)
    }
  }

  const testAppointmentSubmission = async () => {
    setAppointmentLoading(true)
    addLog('🩺 Starting appointment submission test...')

    try {
      if (!user) {
        addLog('❌ No authenticated user found')
        return
      }

      // Import the createAppointmentRequest function
      const { createAppointmentRequest } = await import('@/lib/appointment-requests/api')

      // Create test appointment data using the new format
      const testAppointmentData = {
        patientFirstName: 'Test',
        patientLastName: 'User',
        patientHealthCard: 'ABCD12345678',
        patientHealthCardSequence: '01',
        patientDateOfBirth: '1990-01-01',
        patientPostalCode: 'H1H 1H1',
        appointmentDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 days from now
        appointmentTimePreference: 'morning'
      }

      addLog('📋 Sending appointment request using secure Supabase client with RLS protection...')
      addLog(`Request data: ${JSON.stringify(testAppointmentData, null, 2)}`)
      addLog(`User ID: ${user.id}`)
      addLog(`Patient ID: null (self-appointment test)`)

      // Call the API function which uses the secure API route
      const result = await createAppointmentRequest(user.id, null, testAppointmentData)

      addLog(`📋 API result: ${JSON.stringify(result, null, 2)}`)

      if (result.success) {
        addLog('✅ Appointment submission successful!')
        addLog(`✅ Created appointment with ID: ${result.id}`)
      } else {
        addLog(`❌ Appointment submission failed: ${result.error}`)
      }

    } catch (error: any) {
      addLog(`❌ Error during appointment submission: ${error.message}`)
      console.error('Full error:', error)
    } finally {
      setAppointmentLoading(false)
    }
  }

  useEffect(() => {
    addLog('OAuth test page loaded')
    checkCookies()
    checkStorage()
  }, [])

  useEffect(() => {
    addLog(`Auth status changed: ${status}`)
    if (user) {
      addLog(`User: ${user.email}`)
    }
  }, [status, user])

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">🔍 Google OAuth Test</h1>
      
      <div className="grid gap-6">
        {/* Authentication Status */}
        <Card>
          <CardHeader>
            <CardTitle>🔐 Authentication Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <strong>Status:</strong> <span className={`px-2 py-1 rounded ${
                  status === 'authenticated' ? 'bg-green-100 text-green-800' :
                  status === 'loading' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>{status}</span>
              </div>
              {user && (
                <div>
                  <strong>User:</strong> {user.email}
                </div>
              )}
              {session && (
                <div>
                  <strong>Session:</strong> Active (expires: {new Date(session.expires_at! * 1000).toLocaleString()})
                </div>
              )}
              <div className="flex gap-2">
                <Button onClick={handleGoogleSignIn} disabled={status === 'authenticated'}>
                  Test Google OAuth
                </Button>
                <Button onClick={handleSignOut} disabled={status !== 'authenticated'} variant="outline">
                  Sign Out
                </Button>
                <Button onClick={handleRefresh} variant="outline">
                  Refresh Auth
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Session Persistence Test */}
        <Card>
          <CardHeader>
            <CardTitle>🔄 Session Persistence Test</CardTitle>
            <CardDescription>Test session persistence after external navigation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={simulateStripeNavigation} variant="outline">
                Simulate Stripe Navigation
              </Button>
              <p className="text-sm text-gray-600">
                This simulates navigating to Stripe and back. After clicking, refresh the page to test session restoration.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Cookie Information */}
        <Card>
          <CardHeader>
            <CardTitle>🍪 Cookie Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={checkCookies} variant="outline">Refresh Cookies</Button>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                {cookieInfo || 'No cookie information available'}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Local Storage */}
        <Card>
          <CardHeader>
            <CardTitle>💾 Local Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={checkStorage} variant="outline">Refresh Storage</Button>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">
                {storageInfo || 'No storage information available'}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Test Log */}
        <Card>
          <CardHeader>
            <CardTitle>📝 Test Log</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={clearLogs} variant="outline">Clear Log</Button>
              <pre className="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-60">
                {logs.join('\n') || 'No logs yet'}
              </pre>
            </div>
          </CardContent>
        </Card>

        {/* Appointment Submission */}
        <Card>
          <CardHeader>
            <CardTitle>🩺 Appointment Submission</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={testAppointmentSubmission} disabled={appointmentLoading} variant="outline">
                {appointmentLoading ? 'Submitting...' : 'Submit Test Appointment'}
              </Button>
              <p className="text-sm text-gray-600">
                This simulates submitting a test appointment request.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Simple Endpoint Test */}
        <Card>
          <CardHeader>
            <CardTitle>🔧 Simple Endpoint Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Button onClick={testSimpleEndpoint} disabled={appointmentLoading} variant="outline">
                {appointmentLoading ? 'Testing...' : 'Test Simple Endpoint'}
              </Button>
              <p className="text-sm text-gray-600">
                This simulates testing a simple endpoint.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
