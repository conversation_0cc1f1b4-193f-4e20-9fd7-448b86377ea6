import Link from "next/link"
import { HelpCircle, LifeBuoy, Mail } from "lucide-react"
import { DashboardLayout } from "@/components/layout/dashboard-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { T } from "@/components/t"
import { translationKeys } from "@/lib/translations"

export default function AidePage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            <T keyName={translationKeys.help.needHelp} />
          </h1>
          <p className="text-muted-foreground mt-1.5">
            <T keyName={translationKeys.help.helpDescription} />
          </p>
        </div>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
          <Card>
            <CardHeader className="flex flex-row items-start space-y-0 pb-2">
              <div className="flex items-center gap-2">
                <HelpCircle className="h-5 w-5 text-blue-600" />
                <CardTitle>
                  <T keyName={translationKeys.help.faq} />
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-4">
                <T keyName={translationKeys.help.faqDescription} />
              </CardDescription>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium">
                    <T keyName={translationKeys.help.howToBookAppointment} />
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    <T keyName={translationKeys.help.howToBookDescription} />
                  </p>
                </div>
                <Separator />
                <div>
                  <h3 className="font-medium">
                    <T keyName={translationKeys.help.howToCancelAppointment} />
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    <T keyName={translationKeys.help.howToCancelDescription} />
                  </p>
                </div>
                <Separator />
                <div>
                  <h3 className="font-medium">
                    <T keyName={translationKeys.help.howToChangePlan} />
                  </h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    <T keyName={translationKeys.help.howToChangePlanDescription} />
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-start space-y-0 pb-2">
              <div className="flex items-center gap-2">
                <LifeBuoy className="h-5 w-5 text-blue-600" />
                <CardTitle>
                  <T keyName={translationKeys.help.customerSupport} />
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <CardDescription className="mb-4">
                <T keyName={translationKeys.help.supportDescription} />
              </CardDescription>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <h3 className="font-medium">
                      <T keyName={translationKeys.help.email} />
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1">
                      <T keyName={translationKeys.help.supportEmail} />
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      <T keyName={translationKeys.help.responseTime} />
                    </p>
                  </div>
                </div>
              </div>
              <Button className="w-full mt-6" asChild>
                <a href="mailto:<EMAIL>">
                  <T keyName={translationKeys.help.contactSupport} />
                </a>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
