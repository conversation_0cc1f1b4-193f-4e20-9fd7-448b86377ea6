"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const FORM_DATA_KEY = "test-persistence-form-data"

export default function TestPersistencePage() {
  const [testValue, setTestValue] = useState<string>("")
  const [savedValue, setSavedValue] = useState<string>("")

  // Save to localStorage
  const saveToStorage = (value: string) => {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem(FORM_DATA_KEY, value)
      console.log('Saved to localStorage:', value)
    } catch (error) {
      console.warn('Failed to save to localStorage:', error)
    }
  }

  // Load from localStorage
  const loadFromStorage = (): string => {
    if (typeof window === 'undefined') return ""
    
    try {
      const data = localStorage.getItem(FORM_DATA_KEY) || ""
      if (data) {
        console.log('Loaded from localStorage:', data)
      }
      return data
    } catch (error) {
      console.warn('Failed to load from localStorage:', error)
      return ""
    }
  }

  // Load persisted data on mount
  useEffect(() => {
    const saved = loadFromStorage()
    setTestValue(saved)
    setSavedValue(saved)
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setTestValue(value)
    saveToStorage(value)
  }

  const refreshSavedValue = () => {
    const saved = loadFromStorage()
    setSavedValue(saved)
  }

  const clearStorage = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(FORM_DATA_KEY)
      setTestValue("")
      setSavedValue("")
      console.log('Cleared localStorage')
    }
  }

  const simulatePageRefresh = () => {
    window.location.reload()
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-3xl font-bold mb-6">Form Persistence Test</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Test Form</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label htmlFor="test-input" className="block text-sm font-medium mb-2">
              Test Input (automatically saved to localStorage)
            </label>
            <Input
              id="test-input"
              placeholder="Type something here..."
              value={testValue}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="text-sm text-gray-600">
            <p><strong>Current value:</strong> {testValue || "(empty)"}</p>
            <p><strong>Saved value:</strong> {savedValue || "(empty)"}</p>
          </div>
          
          <div className="flex gap-2">
            <Button onClick={refreshSavedValue} variant="outline">
              Refresh Saved Value
            </Button>
            <Button onClick={clearStorage} variant="outline">
              Clear Storage
            </Button>
            <Button onClick={simulatePageRefresh} variant="outline">
              Simulate Page Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Type something in the input field above</li>
            <li>Switch to another application (like Finder)</li>
            <li>Come back to the browser</li>
            <li>Verify the text is still there</li>
            <li>Click "Simulate Page Refresh" to test persistence across page loads</li>
          </ol>
          
          <div className="mt-4 p-3 bg-blue-50 rounded-md">
            <p className="text-sm text-blue-800">
              <strong>Expected behavior:</strong> The input value should persist when switching between 
              applications and even after page refreshes, demonstrating that localStorage persistence 
              works independently of component re-renders.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 