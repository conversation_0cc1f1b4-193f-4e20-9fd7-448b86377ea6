"use client";

import { supabase } from "../supabase/client";
import { FamilyMember } from "../FamilyMembersContext";

interface FamilyMemberData {
  id?: string;
  user_id: string;
  first_name: string;
  last_name: string;
  health_card: string;
  health_card_prefix: string;
  birth_date: string | null;
  position: number;
}

/**
 * Fetch all family members for a user
 * @param userId The user's ID
 * @returns Promise with array of family members (always 5 slots)
 */
export async function getFamilyMembers(userId: string): Promise<FamilyMember[]> {
  try {
    const { data, error } = await supabase
      .from("family_members")
      .select("*")
      .eq("user_id", userId)
      .order("position", { ascending: true });

    if (error) {
      throw error;
    }

    // Convert database records to application format
    const existingMembers = data.map((item: FamilyMemberData) => ({
      id: item.position, // Use position as ID (1-5)
      firstName: item.first_name || "",
      lastName: item.last_name || "",
      // Use health_card_prefix if available, otherwise fall back to health_card for backward compatibility
      healthCard: item.health_card_prefix || item.health_card || "",
      birthDate: item.birth_date ? new Date(item.birth_date) : undefined,
      editing: false,
      supabaseId: item.id, // Store the Supabase ID for updates
    }));
    
    // Ensure we always have 5 slots by creating a result array with 5 elements
    const result: FamilyMember[] = [];
    
    // Fill slots 1-5
    for (let i = 1; i <= 5; i++) {
      // Find if we have an existing member for this position
      const existingMember = existingMembers.find(m => m.id === i);
      
      if (existingMember) {
        // Use the existing member
        result.push(existingMember);
      } else {
        // Create an empty slot
        result.push({
          id: i,
          firstName: "",
          lastName: "",
          healthCard: "",
          birthDate: undefined,
          editing: false
        });
      }
    }
    
    return result;
  } catch (error) {
    console.error("Error fetching family members:", error);
    throw error;
  }
}

/**
 * Save a single family member to Supabase
 * @param userId The user's ID
 * @param member The family member to save
 * @param position The position in the list (1-5)
 * @returns Promise with the result
 */
export async function saveFamilyMember(
  userId: string,
  member: FamilyMember,
  position: number
): Promise<{ success: boolean; id?: string }> {
  try {
    // Extract first 4 characters of health card as prefix
    const healthCardPrefix = member.healthCard ? member.healthCard.substring(0, 4).toUpperCase() : '';
    
    const memberData: FamilyMemberData = {
      user_id: userId,
      first_name: member.firstName,
      last_name: member.lastName,
      health_card: member.healthCard, // Store full health card for backward compatibility
      health_card_prefix: healthCardPrefix, // Store prefix separately for appointment booking
      birth_date: member.birthDate?.toISOString() || null,
      position,
    };

    // If we have a Supabase ID, update the record
    if (member.supabaseId) {
      const { error } = await supabase
        .from("family_members")
        .update(memberData)
        .eq("id", member.supabaseId);

      if (error) throw error;
      
      return { success: true, id: member.supabaseId };
    } else {
      // Otherwise, insert a new record
      const { data, error } = await supabase
        .from("family_members")
        .insert(memberData)
        .select("id")
        .single();

      if (error) throw error;
      
      return { success: true, id: data.id };
    }
  } catch (error) {
    console.error("Error saving family member:", error);
    return { success: false };
  }
}

/**
 * Save all family members for a user
 * @param userId The user's ID
 * @param members Array of family members
 * @returns Promise with the result
 */
export async function saveAllFamilyMembers(
  userId: string,
  members: FamilyMember[]
): Promise<{ success: boolean }> {
  try {
    // Process members with data only (non-empty)
    const membersWithData = members.filter(
      member => member.firstName || member.lastName || member.healthCard || member.birthDate
    );
    
    // Save each member
    const results = await Promise.all(
      membersWithData.map((member, index) => 
        saveFamilyMember(userId, member, index + 1)
      )
    );
    
    // Check if all saves were successful
    const allSuccess = results.every(result => result.success);
    
    return { success: allSuccess };
  } catch (error) {
    console.error("Error saving all family members:", error);
    return { success: false };
  }
}

/**
 * Delete a family member from Supabase
 * @param memberId The member's Supabase ID
 * @returns Promise with the result
 */
export async function deleteFamilyMember(memberId: string): Promise<{ success: boolean }> {
  try {
    const { error } = await supabase
      .from("family_members")
      .delete()
      .eq("id", memberId);

    if (error) throw error;
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting family member:", error);
    return { success: false };
  }
}