/**
 * Auth Store Integration
 * 
 * This file provides integration between the Zustand user store and the existing AuthContext.
 * It allows for a gradual migration from context-based auth to store-based auth.
 */

'use client'

import React, { useEffect, createContext, useContext, ReactNode } from 'react'
import { useUserStore } from '@/stores/useUserStore'
import { AuthStatus } from '@/lib/AuthContext'
import { Session, User } from '@supabase/supabase-js'

// Create a context for the integration
interface AuthIntegrationContextValue {
  isStoreEnabled: boolean
  enableStore: () => void
  disableStore: () => void
}

const AuthIntegrationContext = createContext<AuthIntegrationContextValue>({
  isStoreEnabled: false,
  enableStore: () => {},
  disableStore: () => {}
})

export const useAuthIntegration = () => useContext(AuthIntegrationContext)

interface AuthIntegrationProviderProps {
  children: ReactNode
  initialEnabled?: boolean
}

/**
 * Provider component that manages the integration between AuthContext and UserStore
 */
export function AuthIntegrationProvider({ 
  children, 
  initialEnabled = false 
}: AuthIntegrationProviderProps) {
  const [isStoreEnabled, setIsStoreEnabled] = React.useState(initialEnabled)
  
  const enableStore = () => setIsStoreEnabled(true)
  const disableStore = () => setIsStoreEnabled(false)
  
  return (
    <AuthIntegrationContext.Provider value={{ isStoreEnabled, enableStore, disableStore }}>
      {children}
    </AuthIntegrationContext.Provider>
  )
}

/**
 * Hook that provides the same interface as useAuth but uses the Zustand store
 * This allows for a gradual migration from context to store
 */
export function useAuthFromStore() {
  const user = useUserStore(state => state.user)
  const session = useUserStore(state => state.session)
  const status = useUserStore(state => state.status)
  const signIn = useUserStore(state => state.signIn)
  const signInWithGoogle = useUserStore(state => state.signInWithGoogle)
  const signOut = useUserStore(state => state.signOut)
  const refresh = useUserStore(state => state.refresh)
  
  // Initial auth check
  useEffect(() => {
    refresh()
  }, [refresh])
  
  return {
    user,
    session,
    status,
    signIn,
    signInWithGoogle,
    signOut,
    refresh
  }
}

/**
 * Component that synchronizes the AuthContext with the UserStore
 * This ensures that both state management systems have the same data
 */
export function AuthStoreSynchronizer() {
  const { isStoreEnabled } = useAuthIntegration()
  
  // Import the original useAuth from AuthContext
  const { useAuth } = require('@/lib/AuthContext')
  const { 
    user: contextUser, 
    session: contextSession, 
    status: contextStatus 
  } = useAuth()
  
  // Get store actions
  const setUser = useUserStore(state => state.setUser)
  const setSession = useUserStore(state => state.setSession)
  const setStatus = useUserStore(state => state.setStatus)
  
  // Sync context to store if store is enabled
  useEffect(() => {
    if (isStoreEnabled) {
      setUser(contextUser)
      setSession(contextSession)
      setStatus(contextStatus as AuthStatus)
    }
  }, [isStoreEnabled, contextUser, contextSession, contextStatus, setUser, setSession, setStatus])
  
  return null
}

/**
 * HOC that wraps a component to provide auth from either context or store
 * based on the integration settings
 */
export function withAuth<P extends object>(Component: React.ComponentType<P & { 
  user: User | null
  session: Session | null
  status: AuthStatus
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: Error }>
  signInWithGoogle: () => Promise<{ success: boolean; error?: Error }>
  signOut: (redirectUrl?: string) => Promise<void>
  refresh: () => Promise<void>
}>) {
  return function WithAuthComponent(props: P) {
    const { isStoreEnabled } = useAuthIntegration()
    
    // Use either store or context based on settings
    const storeAuth = useAuthFromStore()
    const { useAuth } = require('@/lib/AuthContext')
    const contextAuth = useAuth()
    
    const auth = isStoreEnabled ? storeAuth : contextAuth
    
    return <Component {...props} {...auth} />
  }
}
