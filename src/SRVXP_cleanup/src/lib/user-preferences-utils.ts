"use client";

import { supabase } from "./supabase/client";

export interface UserPreferences {
  language: "fr" | "en";
  theme: "light" | "dark" | "system";
}

/**
 * Saves the user's preferences to Supabase
 * 
 * @param userId The user's ID
 * @param preferences The preferences to save
 * @returns Promise with the result of the operation
 */
export async function saveUserPreferences(
  userId: string,
  preferences: UserPreferences
): Promise<{ success: boolean; error?: Error }> {
  try {
    // Update preferences directly in the users table
    const { error: updateError } = await supabase
      .from('users')
      .update({
        language: preferences.language,
        theme: preferences.theme,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (updateError) {
      throw updateError;
    }

    return { success: true };
  } catch (error) {
    console.error("Error saving user preferences:", error);
    return { success: false, error: error as Error };
  }
}

export async function getUserPreferences(
  userId: string
): Promise<UserPreferences | null> {
  try {
    // Query the users table for preferences
    const { data, error } = await supabase
      .from('users')
      .select('language, theme')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      throw error;
    }

    if (!data || (!data.language && !data.theme)) {
      return {
        language: "fr" as "fr" | "en",
        theme: "light" as "light" | "dark" | "system"
      };
    }

    return {
      language: data.language as "fr" | "en" || "fr",
      theme: data.theme as "light" | "dark" | "system" || "light"
    };
  } catch (error) {
    console.error("Error getting user preferences:", error);
    return null;
  }
}