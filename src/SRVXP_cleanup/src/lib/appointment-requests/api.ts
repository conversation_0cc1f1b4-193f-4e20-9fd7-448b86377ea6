"use client"

import { supabase } from "../supabase/client"

interface AppointmentRequestData {
  patient_first_name: string;
  patient_last_name: string;
  patient_health_card: string;
  patient_health_card_sequence: string;
  patient_date_of_birth: string | null;
  patient_postal_code: string;
  appointment_date: string;
  appointment_time_preference: string;
  user_id: string;
  patient_id: string | null;
}

/**
 * Create a new appointment request in Supabase
 * @param userId The user's ID
 * @param familyMemberId The family member's ID
 * @param appointmentData The appointment request data
 * @returns Promise with the result
 */
export async function createAppointmentRequest(
  userId: string,
  familyMemberId: string | null,
  appointmentData: {
    patientFirstName: string;
    patientLastName: string;
    patientHealthCard: string;
    patientHealthCardSequence: string;
    patientDateOfBirth: string | null;
    patientPostalCode: string;
    appointmentDate: string;
    appointmentTimePreference: string;
  }
): Promise<{ success: boolean; id?: string; error?: string }> {
  try {
    console.log('Creating appointment request via direct Supabase client (secure)');
    console.log('Appointment data:', appointmentData);
    console.log('Patient ID:', familyMemberId);

    // Use direct Supabase client with RLS protection
    // RLS policies ensure users can only create appointments for themselves or their family members
    const requestData: AppointmentRequestData = {
      user_id: userId,
      patient_id: familyMemberId,
      patient_first_name: appointmentData.patientFirstName,
      patient_last_name: appointmentData.patientLastName,
      patient_health_card: appointmentData.patientHealthCard,
      patient_health_card_sequence: appointmentData.patientHealthCardSequence,
      patient_date_of_birth: appointmentData.patientDateOfBirth,
      patient_postal_code: appointmentData.patientPostalCode,
      appointment_date: appointmentData.appointmentDate,
      appointment_time_preference: appointmentData.appointmentTimePreference,
    };

    console.log('Inserting to Supabase with RLS protection:', requestData);

    const { data, error } = await supabase
      .from("appointment_requests")
      .insert(requestData)
      .select("id")
      .single();

    if (error) {
      console.error('Supabase insert error:', error);

      // Handle specific database errors
      if (error.message.includes('Free users cannot create appointment requests')) {
        return { success: false, error: 'Subscription required to create appointment requests' };
      }

      if (error.message.includes('Only family plan members can book for others')) {
        return { success: false, error: 'Family plan required to book for family members' };
      }

      if (error.message.includes('Patient does not belong to this user')) {
        return { success: false, error: 'Invalid patient selection' };
      }

      throw error;
    }

    console.log('Successfully created appointment request with ID:', data.id);
    return { success: true, id: data.id };
  } catch (error) {
    console.error("Error creating appointment request:", error);
    const errorMessage = error instanceof Error ? error.message : "Une erreur est survenue lors de la création de la demande.";
    return { success: false, error: errorMessage };
  }
}

/**
 * Get appointment requests for the current user
 * @param status Optional status filter
 * @param limit Maximum number of requests to return
 * @returns Promise with the result
 */
export async function getAppointmentRequests(
  status?: string,
  limit: number = 10
): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    // Build URL with query parameters
    const url = new URL('/api/appointment-requests', window.location.origin)
    if (status) url.searchParams.append('status', status)
    url.searchParams.append('limit', limit.toString())

    const response = await fetch(url.toString())
    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'Failed to fetch appointment requests'
      }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('Error fetching appointment requests:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Cancel an appointment request
 * @param requestId The appointment request ID
 * @param reason Optional cancellation reason
 * @returns Promise with the result
 */
export async function cancelAppointmentRequest(
  requestId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // First, get the appointment request data to transfer
    const { data: appointmentData, error: fetchError } = await supabase
      .from('appointment_requests')
      .select('*')
      .eq('id', requestId)
      .single()

    if (fetchError) {
      console.error('Error fetching appointment request for cancellation:', fetchError)
      throw new Error(`Failed to fetch appointment request: ${fetchError.message || 'Unknown error'}`)
    }

    if (!appointmentData) {
      throw new Error('Appointment request not found')
    }

    // Create a cancelled appointment record with all the appointment data
    const { error: cancelError } = await supabase
      .from('appointments_cancelled')
      .insert({
        appointment_request_id: requestId,
        cancellation_reason: reason || 'Cancelled by user',
        cancelled_by: 'user',
        // Copy all appointment data
        user_id: appointmentData.user_id,
        patient_id: appointmentData.patient_id,
        patient_first_name: appointmentData.patient_first_name,
        patient_last_name: appointmentData.patient_last_name,
        patient_health_card: appointmentData.patient_health_card,
        patient_health_card_sequence: appointmentData.patient_health_card_sequence,
        patient_date_of_birth: appointmentData.patient_date_of_birth,
        patient_postal_code: appointmentData.patient_postal_code,
        appointment_date: appointmentData.appointment_date,
        appointment_time_preference: appointmentData.appointment_time_preference,
        search_radius: appointmentData.search_radius,
        created_at: appointmentData.created_at,
        updated_at: appointmentData.updated_at
      })

    if (cancelError) {
      console.error('Error creating cancelled appointment record:', cancelError)
      throw new Error(`Failed to create cancelled appointment record: ${cancelError.message || 'Unknown error'}`)
    }

    // Now delete the appointment request since we've transferred the data
    const { error: deleteError } = await supabase
      .from('appointment_requests')
      .delete()
      .eq('id', requestId)

    if (deleteError) {
      console.error('Error deleting appointment request:', deleteError)
      throw new Error(`Failed to delete appointment request: ${deleteError.message || 'Unknown error'}`)
    }

    return { success: true }
  } catch (error) {
    console.error('Error cancelling appointment request:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
