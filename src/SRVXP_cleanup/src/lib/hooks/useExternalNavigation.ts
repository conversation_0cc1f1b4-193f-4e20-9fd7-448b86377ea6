import { useEffect, useCallback, useRef } from 'react'
import { useAuth } from '@/lib/AuthContext'

/**
 * Hook to handle session restoration when returning from external sites like Stripe
 */
export const useExternalNavigation = () => {
  const { refresh } = useAuth()
  const hasProcessedReturn = useRef(false)
  const lastRefreshTime = useRef(0)
  const REFRESH_DEBOUNCE_MS = 5000 // 5 seconds debounce

  // Function to detect if we're returning from Stripe
  const detectStripeReturn = useCallback(() => {
    if (typeof window === 'undefined') return false
    
    const urlParams = new URLSearchParams(window.location.search)
    const pathname = window.location.pathname
    const referrer = document.referrer
    
    // More specific detection - only trigger on actual returns from Stripe
    const hasStripeSessionId = urlParams.has('session_id')
    const isFromStripeReferrer = referrer.includes('stripe.com') || referrer.includes('checkout.stripe.com')
    const hasStripeRedirectFlag = sessionStorage.getItem('stripe_redirect') === 'true'
    
    // Only consider it a Stripe return if we have specific indicators
    return hasStripeSessionId || isFromStripeReferrer || hasStripeRedirectFlag
  }, [])

  // Function to mark when user is about to navigate to Stripe
  const markStripeNavigation = useCallback(() => {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('stripe_redirect', 'true')
      sessionStorage.setItem('stripe_redirect_time', Date.now().toString())
      console.log('Marked Stripe navigation')
    }
  }, [])

  // Function to clear Stripe navigation markers
  const clearStripeNavigation = useCallback(() => {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem('stripe_redirect')
      sessionStorage.removeItem('stripe_redirect_time')
      console.log('Cleared Stripe navigation markers')
    }
  }, [])

  // Debounced refresh function
  const debouncedRefresh = useCallback(async () => {
    const now = Date.now()
    if (now - lastRefreshTime.current < REFRESH_DEBOUNCE_MS) {
      console.log('Refresh debounced, too recent')
      return
    }
    
    lastRefreshTime.current = now
    
    try {
      console.log('Executing debounced auth refresh...')
      await refresh()
    } catch (error) {
      console.error('Error during debounced refresh:', error)
    }
  }, [refresh])

  // Enhanced page visibility handler
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !hasProcessedReturn.current) {
        const isStripeReturn = detectStripeReturn()
        
        if (isStripeReturn) {
          console.log('Stripe return detected on visibility change, refreshing auth state...')
          hasProcessedReturn.current = true
          
          // Clear the stripe navigation marker
          clearStripeNavigation()
          
          // Delay refresh to ensure page is fully loaded
          setTimeout(() => {
            debouncedRefresh()
          }, 1000)
        }
      }
    }

    const handleWindowFocus = () => {
      if (!hasProcessedReturn.current) {
        const isStripeReturn = detectStripeReturn()
        
        if (isStripeReturn) {
          console.log('Window focus with Stripe return detected, refreshing auth state...')
          hasProcessedReturn.current = true
          
          // Clear the stripe navigation marker
          clearStripeNavigation()
          
          // Delay refresh to ensure proper focus
          setTimeout(() => {
            debouncedRefresh()
          }, 1000)
        }
      }
    }

    // Enhanced beforeunload handler to detect external navigation
    const handleBeforeUnload = () => {
      // Only mark if user is actually on subscription page and navigating away
      if (window.location.pathname.includes('/compte/abonnement') && 
          !hasProcessedReturn.current) {
        markStripeNavigation()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleWindowFocus)
    window.addEventListener('beforeunload', handleBeforeUnload)

    // Initial check on mount - but only once
    if (!hasProcessedReturn.current && detectStripeReturn()) {
      console.log('Stripe return detected on mount, refreshing auth state...')
      hasProcessedReturn.current = true
      clearStripeNavigation()
      
      // Delay to prevent immediate refresh loops
      setTimeout(() => {
        debouncedRefresh()
      }, 500)
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleWindowFocus)
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [detectStripeReturn, markStripeNavigation, clearStripeNavigation, debouncedRefresh])

  return {
    detectStripeReturn,
    markStripeNavigation,
    clearStripeNavigation,
  }
} 