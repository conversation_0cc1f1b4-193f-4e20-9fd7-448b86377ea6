// This file contains type definitions and keys for translations

// Define language type
export type Language = "fr" | "en";

// Type for a translation key
export type TranslationKey = string;

// Type for a complete translation dictionary
export type TranslationDictionary = {
  [key: TranslationKey]: string;
};

import { createTranslationKey } from './translation-utils';

// Create a keys object for consistent key access across the app
export const translationKeys = {
  errors: {
    tryAgainLater: createTranslationKey('errors', 'tryAgainLater'),
    saveFailed: createTranslationKey('errors', 'saveFailed'),
    notFound: createTranslationKey('errors', 'notFound'),
    unauthorized: createTranslationKey('errors', 'unauthorized'),
    generalError: createTranslationKey('errors', 'generalError'),
  },
  common: {
    save: createTranslationKey('common', 'save'),
    cancel: createTranslationKey('common', 'cancel'),
    confirm: createTranslationKey('common', 'confirm'),
    delete: createTranslationKey('common', 'delete'),
    loading: createTranslationKey('common', 'loading'),
    search: createTranslationKey('common', 'search'),
    logout: createTranslationKey('common', 'logout'),
    loggingOut: createTranslationKey('common', 'loggingOut'),
    close: createTranslationKey('common', 'close'),
    active: createTranslationKey('common', 'active'),
    inactive: createTranslationKey('common', 'inactive'),
    submit: createTranslationKey('common', 'submit'),
    submitting: createTranslationKey('common', 'submitting'),
    processing: createTranslationKey('common', 'processing'),
    newRequest: createTranslationKey('common', 'newRequest'),
    required: createTranslationKey('common', 'required'),
    yes: createTranslationKey('common', 'yes'),
    no: createTranslationKey('common', 'no'),
    continue: createTranslationKey('common', 'continue'),
    manage: createTranslationKey('common', 'manage'),
    modify: createTranslationKey('common', 'modify'),
    back: createTranslationKey('common', 'back'),
    saved: createTranslationKey('common', 'saved'),
    saving: createTranslationKey('common', 'saving'),
    saveChanges: createTranslationKey('common', 'saveChanges'),
    errorOccurred: createTranslationKey('common', 'errorOccurred'),
  },
  auth: {
    signIn: createTranslationKey('auth', 'signIn'),
    signUp: createTranslationKey('auth', 'signUp'),
    email: createTranslationKey('auth', 'email'),
    password: createTranslationKey('auth', 'password'),
    confirmPassword: createTranslationKey('auth', 'confirmPassword'),
    forgotPassword: createTranslationKey('auth', 'forgotPassword'),
    resetPassword: createTranslationKey('auth', 'resetPassword'),
    enterCredentials: createTranslationKey('auth', 'enterCredentials'),
    createAccount: createTranslationKey('auth', 'createAccount'),
    alreadyHaveAccount: createTranslationKey('auth', 'alreadyHaveAccount'),
    noAccount: createTranslationKey('auth', 'noAccount'),
    passwordReset: createTranslationKey('auth', 'passwordReset'),
    passwordResetInstructions: createTranslationKey('auth', 'passwordResetInstructions'),
    passwordResetSent: createTranslationKey('auth', 'passwordResetSent'),
    successfulReset: createTranslationKey('auth', 'successfulReset'),
    firstName: createTranslationKey('auth', 'firstName'),
    lastName: createTranslationKey('auth', 'lastName'),
    errors: {
      invalidCredentials: createTranslationKey('auth.errors', 'invalidCredentials'),
      passwordsDontMatch: createTranslationKey('auth.errors', 'passwordsDontMatch'),
      emailInUse: createTranslationKey('auth.errors', 'emailInUse'),
      invalidEmail: createTranslationKey('auth.errors', 'invalidEmail'),
      passwordTooShort: createTranslationKey('auth.errors', 'passwordTooShort'),
      generalError: createTranslationKey('auth.errors', 'generalError'),
    },
  },
  nav: {
    home: createTranslationKey('nav', 'home'),
    dashboard: createTranslationKey('nav', 'dashboard'),
    appointments: createTranslationKey('nav', 'appointments'),
    findAppointment: createTranslationKey('nav', 'findAppointment'),
    calendar: createTranslationKey('nav', 'calendar'),
    help: createTranslationKey('nav', 'help'),
    account: createTranslationKey('nav', 'account'),
    mobileNavigation: createTranslationKey('nav', 'mobileNavigation'),
    needHelp: createTranslationKey('nav', 'needHelp'),
  },
  account: {
    profile: createTranslationKey('account', 'profile'),
    preferences: createTranslationKey('account', 'preferences'),
    subscription: createTranslationKey('account', 'subscription'),
    users: createTranslationKey('account', 'users'),
    manageUsers: createTranslationKey('account', 'manageUsers'),
    yourAccount: createTranslationKey('account', 'yourAccount'),
    individualPlan: createTranslationKey('account', 'individualPlan'),
    individualPlanMonthly: createTranslationKey('account', 'individualPlanMonthly'),
    individualPlanAnnual: createTranslationKey('account', 'individualPlanAnnual'),
    familyPlan: createTranslationKey('account', 'familyPlan'),
    familyPlanMonthly: createTranslationKey('account', 'familyPlanMonthly'),
    familyPlanAnnual: createTranslationKey('account', 'familyPlanAnnual'),
    manageInformation: createTranslationKey('account', 'manageInformation'),
    personalInfoDescription: createTranslationKey('account', 'personalInfoDescription'),
    modifyProfile: createTranslationKey('account', 'modifyProfile'),
    modifySubscription: createTranslationKey('account', 'modifySubscription'),
    subscriptionDescription: createTranslationKey('account', 'subscriptionDescription'),
    manageSubscription: createTranslationKey('account', 'manageSubscription'),
    appearanceLanguage: createTranslationKey('account', 'appearanceLanguage'),
    appearanceDescription: createTranslationKey('account', 'appearanceDescription'),
    modifyPreferences: createTranslationKey('account', 'modifyPreferences'),
    manageAccountUsers: createTranslationKey('account', 'manageAccountUsers'),
    manageUsersDescription: createTranslationKey('account', 'manageUsersDescription'),
    manageProfile: createTranslationKey('account', 'manageProfile'),
    manageProfileDescription: createTranslationKey('account', 'manageProfileDescription'),
    manageProfileButton: createTranslationKey('account', 'manageProfileButton'),
    subscribePlan: createTranslationKey('account', 'subscribePlan'),
    choosePlan: createTranslationKey('account', 'choosePlan'),
    editPersonalInfo: createTranslationKey('account', 'editPersonalInfo'),
    firstName: createTranslationKey('account', 'firstName'),
    lastName: createTranslationKey('account', 'lastName'),
    email: createTranslationKey('account', 'email'),
    phone: createTranslationKey('account', 'phone'),
    invalidEmail: createTranslationKey('account', 'invalidEmail'),
    invalidPhone: createTranslationKey('account', 'invalidPhone'),
    emailCannotBeEmpty: createTranslationKey('account', 'emailCannotBeEmpty'),
    firstNameRequired: createTranslationKey('account', 'firstNameRequired'),
    lastNameRequired: createTranslationKey('account', 'lastNameRequired'),
    emailVerificationSent: createTranslationKey('account', 'emailVerificationSent'),
  },
  home: {
    greeting: createTranslationKey('home', 'greeting'),
    welcome: createTranslationKey('home', 'welcome'),
    findAppointmentTitle: createTranslationKey('home', 'findAppointmentTitle'),
    findAppointmentDesc: createTranslationKey('home', 'findAppointmentDesc'),
    viewRequests: createTranslationKey('home', 'viewRequests'),
    manageAppointmentsDesc: createTranslationKey('home', 'manageAppointmentsDesc'),
    manageUsersDesc: createTranslationKey('home', 'manageUsersDesc'),
    manageProfileTitle: createTranslationKey('home', 'manageProfileTitle'),
    manageProfileDesc: createTranslationKey('home', 'manageProfileDesc'),
    manageProfileButton: createTranslationKey('home', 'manageProfileButton'),
  },
  appointments: {
    title: createTranslationKey('appointments', 'title'),
    description: createTranslationKey('appointments', 'description'),
    requestsTitle: createTranslationKey('appointments', 'requestsTitle'),
    all: createTranslationKey('appointments', 'all'),
    inProgress: createTranslationKey('appointments', 'inProgress'),
    completed: createTranslationKey('appointments', 'completed'),
    noRequests: createTranslationKey('appointments', 'noRequests'),
    noRequestsInProgress: createTranslationKey('appointments', 'noRequestsInProgress'),
    noRequestsCompleted: createTranslationKey('appointments', 'noRequestsCompleted'),
    noRequestsCancelled: createTranslationKey('appointments', 'noRequestsCancelled'),
    postalCode: createTranslationKey('appointments', 'postalCode'),
    sentOn: createTranslationKey('appointments', 'sentOn'),
    pending: createTranslationKey('appointments', 'pending'),
    done: createTranslationKey('appointments', 'done'),
    cancelAppointment: createTranslationKey('appointments', 'cancelAppointment'),
    cancelConfirmation: createTranslationKey('appointments', 'cancelConfirmation'),
    cancelConfirmationText: createTranslationKey('appointments', 'cancelConfirmationText'),
    noContinue: createTranslationKey('appointments', 'noContinue'),
    yesCancel: createTranslationKey('appointments', 'yesCancel'),
    viewAll: createTranslationKey('appointments', 'viewAll'),
  },
  meta: {
    title: createTranslationKey('meta', 'title'),
    description: createTranslationKey('meta', 'description'),
  },
  subscription: {
    modifySubscription: createTranslationKey('subscription', 'modifySubscription'),
    individualPlan: createTranslationKey('subscription', 'individualPlan'),
    monthlyCost: createTranslationKey('subscription', 'monthlyCost'),
    benefits: createTranslationKey('subscription', 'benefits'),
    unlimitedAccess: createTranslationKey('subscription', 'unlimitedAccess'),
    emailNotifications: createTranslationKey('subscription', 'emailNotifications'),
    familyProfiles: createTranslationKey('subscription', 'familyProfiles'),
    modifyPlan: createTranslationKey('subscription', 'modifyPlan'),
    cancelPlan: createTranslationKey('subscription', 'cancelPlan'),
    paymentHistory: createTranslationKey('subscription', 'paymentHistory'),
    monthlySubscription: createTranslationKey('subscription', 'monthlySubscription'),
    march: createTranslationKey('subscription', 'march'),
    february: createTranslationKey('subscription', 'february'),
    january: createTranslationKey('subscription', 'january'),
    cost: createTranslationKey('subscription', 'cost'),
    changePlan: createTranslationKey('subscription', 'changePlan'),
    changePlanDescription: createTranslationKey('subscription', 'changePlanDescription'),
    confirmChange: createTranslationKey('subscription', 'confirmChange'),
    cancelConfirmation: createTranslationKey('subscription', 'cancelConfirmation'),
    cancelWarning: createTranslationKey('subscription', 'cancelWarning'),
    yesCancel: createTranslationKey('subscription', 'yesCancel'),
    noCancel: createTranslationKey('subscription', 'noCancel'),
    // Success page translations
    status: createTranslationKey('subscription', 'status'),
    verifyingPayment: createTranslationKey('subscription', 'verifyingPayment'),
    success: createTranslationKey('subscription', 'success'),
    successMessage: createTranslationKey('subscription', 'successMessage'),
    details: createTranslationKey('subscription', 'details'),
    plan: createTranslationKey('subscription', 'plan'),
    billing: createTranslationKey('subscription', 'billing'),
    amount: createTranslationKey('subscription', 'amount'),
    currentPeriod: createTranslationKey('subscription', 'currentPeriod'),
    nextSteps: createTranslationKey('subscription', 'nextSteps'),
    goToDashboard: createTranslationKey('subscription', 'goToDashboard'),
    manageAccount: createTranslationKey('subscription', 'manageAccount'),
    error: createTranslationKey('subscription', 'error'),
    errorMessage: createTranslationKey('subscription', 'errorMessage'),
    needHelp: createTranslationKey('subscription', 'needHelp'),
    returnToPlans: createTranslationKey('subscription', 'returnToPlans'),
    contactSupport: createTranslationKey('subscription', 'contactSupport'),
    canceledCheckout: createTranslationKey('subscription', 'canceledCheckout'),
    processingSubscription: createTranslationKey('subscription', 'processingSubscription'),
    noSessionId: createTranslationKey('subscription', 'noSessionId'),
    notLoggedIn: createTranslationKey('subscription', 'notLoggedIn'),
  },
  preferences: {
    managePreferences: createTranslationKey('preferences', 'managePreferences'),
    languageAppearance: createTranslationKey('preferences', 'languageAppearance'),
    customizeInterface: createTranslationKey('preferences', 'customizeInterface'),
    preferredLanguage: createTranslationKey('preferences', 'preferredLanguage'),
    french: createTranslationKey('preferences', 'french'),
    english: createTranslationKey('preferences', 'english'),
    languageDescription: createTranslationKey('preferences', 'languageDescription'),
    appTheme: createTranslationKey('preferences', 'appTheme'),
    light: createTranslationKey('preferences', 'light'),
    dark: createTranslationKey('preferences', 'dark'),
    themeDescription: createTranslationKey('preferences', 'themeDescription'),
    saveChanges: createTranslationKey('preferences', 'saveChanges'),
    saving: createTranslationKey('preferences', 'saving'),
    changesSaved: createTranslationKey('preferences', 'changesSaved'),
    errorSaving: createTranslationKey('preferences', 'errorSaving'),
  },
  users: {
    manageAccountUsers: createTranslationKey('users', 'manageAccountUsers'),
    manageProfile: createTranslationKey('users', 'manageProfile'),
    familyMembers: createTranslationKey('users', 'familyMembers'),
    userProfile: createTranslationKey('users', 'userProfile'),
    familyMembersDescription: createTranslationKey('users', 'familyMembersDescription'),
    userProfileDescription: createTranslationKey('users', 'userProfileDescription'),
    addYourInfo: createTranslationKey('users', 'addYourInfo'),
    addYourInfoPrompt: createTranslationKey('users', 'addYourInfoPrompt'),
    cancel: createTranslationKey('users', 'cancel'),
    firstName: createTranslationKey('users', 'firstName'),
    lastName: createTranslationKey('users', 'lastName'),
    healthCardPrefix: createTranslationKey('users', 'healthCardPrefix'),
    healthCardDescription: createTranslationKey('users', 'healthCardDescription'),
    birthDate: createTranslationKey('users', 'birthDate'),
    save: createTranslationKey('users', 'save'),
    edit: createTranslationKey('users', 'edit'),
    healthCard: createTranslationKey('users', 'healthCard'),
    addMember: createTranslationKey('users', 'addMember'),
    editMemberPrompt: createTranslationKey('users', 'editMemberPrompt'),
    selectDate: createTranslationKey('users', 'selectDate'),
    validationError: createTranslationKey('users', 'validationError'),
  },
  help: {
    needHelp: createTranslationKey('help', 'needHelp'),
    helpDescription: createTranslationKey('help', 'helpDescription'),
    faq: createTranslationKey('help', 'faq'),
    faqDescription: createTranslationKey('help', 'faqDescription'),
    howToBookAppointment: createTranslationKey('help', 'howToBookAppointment'),
    howToBookDescription: createTranslationKey('help', 'howToBookDescription'),
    howToCancelAppointment: createTranslationKey('help', 'howToCancelAppointment'),
    howToCancelDescription: createTranslationKey('help', 'howToCancelDescription'),
    howToChangePlan: createTranslationKey('help', 'howToChangePlan'),
    howToChangePlanDescription: createTranslationKey('help', 'howToChangePlanDescription'),
    customerSupport: createTranslationKey('help', 'customerSupport'),
    supportDescription: createTranslationKey('help', 'supportDescription'),
    email: createTranslationKey('help', 'email'),
    supportEmail: createTranslationKey('help', 'supportEmail'),
    responseTime: createTranslationKey('help', 'responseTime'),
    contactSupport: createTranslationKey('help', 'contactSupport'),
  },
  landing: {
    hero: {
      title: createTranslationKey('landing.hero', 'title'),
      subtitle: createTranslationKey('landing.hero', 'subtitle'),
      findAppointment: createTranslationKey('landing.hero', 'findAppointment'),
      learnMore: createTranslationKey('landing.hero', 'learnMore'),
      imageAlt: createTranslationKey('landing.hero', 'imageAlt'),
    },
    features: {
      sameDay: {
        title: createTranslationKey('landing.features.sameDay', 'title'),
        description: createTranslationKey('landing.features.sameDay', 'description'),
      },
      nearbyClinic: {
        title: createTranslationKey('landing.features.nearbyClinic', 'title'),
        description: createTranslationKey('landing.features.nearbyClinic', 'description'),
      },
      anywhereInQuebec: {
        title: createTranslationKey('landing.features.anywhereInQuebec', 'title'),
        description: createTranslationKey('landing.features.anywhereInQuebec', 'description'),
      },
    },
    howItWorks: {
      title: createTranslationKey('landing.howItWorks', 'title'),
      customAppointments: {
        title: createTranslationKey('landing.howItWorks.customAppointments', 'title'),
        description: createTranslationKey('landing.howItWorks.customAppointments', 'description'),
      },
      easyManagement: {
        title: createTranslationKey('landing.howItWorks.easyManagement', 'title'),
        description: createTranslationKey('landing.howItWorks.easyManagement', 'description'),
      },
      imageAlt: createTranslationKey('landing.howItWorks', 'imageAlt'),
    },
    pricing: {
      title: createTranslationKey('landing.pricing', 'title'),
      description: createTranslationKey('landing.pricing', 'description'),
      period: {
        monthly: createTranslationKey('landing.pricing.period', 'monthly'),
        annually: createTranslationKey('landing.pricing.period', 'annually'),
      },
      individual: {
        title: createTranslationKey('landing.pricing.individual', 'title'),
        description: createTranslationKey('landing.pricing.individual', 'description'),
        features: createTranslationKey('landing.pricing.individual', 'features'),
        annualSavings: createTranslationKey('landing.pricing.individual', 'annualSavings'),
      },
      family: {
        title: createTranslationKey('landing.pricing.family', 'title'),
        description: createTranslationKey('landing.pricing.family', 'description'),
        features: createTranslationKey('landing.pricing.family', 'features'),
        annualSavings: createTranslationKey('landing.pricing.family', 'annualSavings'),
      },
      choosePlan: createTranslationKey('landing.pricing', 'choosePlan'),
      included: createTranslationKey('landing.pricing', 'included'),
      manageSubscription: createTranslationKey('landing.pricing', 'manageSubscription'),
      feature1: createTranslationKey('landing.pricing', 'feature1'),
      feature2: createTranslationKey('landing.pricing', 'feature2'),
      feature3: createTranslationKey('landing.pricing', 'feature3'),
    feature4: createTranslationKey('landing.pricing', 'feature4'),
    },
    faq: {
      title: createTranslationKey('landing.faq', 'title'),
      viewFullFaq: createTranslationKey('landing.faq', 'viewFullFaq'),
      questions: [
        {
          question: createTranslationKey('landing.faq.questions.0', 'question'),
          answer: createTranslationKey('landing.faq.questions.0', 'answer'),
        },
        {
          question: createTranslationKey('landing.faq.questions.1', 'question'),
          answer: createTranslationKey('landing.faq.questions.1', 'answer'),
        },
        {
          question: createTranslationKey('landing.faq.questions.2', 'question'),
          answer: createTranslationKey('landing.faq.questions.2', 'answer'),
        },
        {
          question: createTranslationKey('landing.faq.questions.3', 'question'),
          answer: createTranslationKey('landing.faq.questions.3', 'answer'),
        },
        {
          question: createTranslationKey('landing.faq.questions.4', 'question'),
          answer: createTranslationKey('landing.faq.questions.4', 'answer'),
        },
      ],
    },
    cta: {
      title: createTranslationKey('landing.cta', 'title'),
      subtitle: createTranslationKey('landing.cta', 'subtitle'),
      buttonText: createTranslationKey('landing.cta', 'buttonText'),
      imageAlt: createTranslationKey('landing.cta', 'imageAlt'),
    },
    navbar: {
      title: createTranslationKey('landing.navbar', 'title'),
      signIn: createTranslationKey('landing.navbar', 'signIn'),
      signUp: createTranslationKey('landing.navbar', 'signUp'),
      service: createTranslationKey('landing.navbar', 'service'),
      pricing: createTranslationKey('landing.navbar', 'pricing'),
      faq: createTranslationKey('landing.navbar', 'faq'),
    },
    footer: {
      description: createTranslationKey('landing.footer', 'description'),
      contactUs: createTranslationKey('landing.footer', 'contactUs'),
      privacyPolicy: createTranslationKey('landing.footer', 'privacyPolicy'),
      termsOfUse: createTranslationKey('landing.footer', 'termsOfUse'),
      termsOfSale: createTranslationKey('landing.footer', 'termsOfSale'),
      copyright: createTranslationKey('landing.footer', 'copyright'),
    },
  },

  findAppointment: {
    title: createTranslationKey('findAppointment', 'title'),
    description: createTranslationKey('findAppointment', 'description'),
    searchCriteria: createTranslationKey('findAppointment', 'searchCriteria'),
    requiredFields: createTranslationKey('findAppointment', 'requiredFields'),
    appointmentFor: createTranslationKey('findAppointment', 'appointmentFor'),
    selectPerson: createTranslationKey('findAppointment', 'selectPerson'),
    managedInUsersSection: createTranslationKey('findAppointment', 'managedInUsersSection'),
    healthCard: createTranslationKey('findAppointment', 'healthCard'),
    healthCardOf: createTranslationKey('findAppointment', 'healthCardOf'),
    lastDigits: createTranslationKey('findAppointment', 'lastDigits'),
    enterEightDigits: createTranslationKey('findAppointment', 'enterEightDigits'),
    format: createTranslationKey('findAppointment', 'format'),
    sequenceNumber: createTranslationKey('findAppointment', 'sequenceNumber'),
    sequenceInfo: createTranslationKey('findAppointment', 'sequenceInfo'),
    enterTwoDigits: createTranslationKey('findAppointment', 'enterTwoDigits'),
    postalCode: createTranslationKey('findAppointment', 'postalCode'),
    postalExample: createTranslationKey('findAppointment', 'postalExample'),
    invalidPostalFormat: createTranslationKey('findAppointment', 'invalidPostalFormat'),
    postalFormatWarning: createTranslationKey('findAppointment', 'postalFormatWarning'),
    postalCodeDescription: createTranslationKey('findAppointment', 'postalCodeDescription'),
    fromDate: createTranslationKey('findAppointment', 'fromDate'),
    selectDate: createTranslationKey('findAppointment', 'selectDate'),
    appointmentTime: createTranslationKey('findAppointment', 'appointmentTime'),
    chooseTime: createTranslationKey('findAppointment', 'chooseTime'),
    morning: createTranslationKey('findAppointment', 'morning'),
    afternoon: createTranslationKey('findAppointment', 'afternoon'),
    evening: createTranslationKey('findAppointment', 'evening'),
    asap: createTranslationKey('findAppointment', 'asap'),
    submitRequest: createTranslationKey('findAppointment', 'submitRequest'),
    thankYou: createTranslationKey('findAppointment', 'thankYou'),
    confirmationMessage: createTranslationKey('findAppointment', 'confirmationMessage'),
    viewRequests: createTranslationKey('findAppointment', 'viewRequests'),
    // Form validation messages
    selectDateError: createTranslationKey('findAppointment', 'selectDateError'),
    selectTimeError: createTranslationKey('findAppointment', 'selectTimeError'),
    enterPostalError: createTranslationKey('findAppointment', 'enterPostalError'),
    invalidPostalError: createTranslationKey('findAppointment', 'invalidPostalError'),
    selectPersonError: createTranslationKey('findAppointment', 'selectPersonError'),
    healthCardDigitsError: createTranslationKey('findAppointment', 'healthCardDigitsError'),
    sequenceNumberError: createTranslationKey('findAppointment', 'sequenceNumberError'),
    noSubscription: createTranslationKey('findAppointment', 'noSubscription'),
  },
};