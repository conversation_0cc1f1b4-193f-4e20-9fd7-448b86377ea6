// Utility functions for translation management with lazy loading

import { Language } from './translations';

// Cache for translations
let translationCache: Record<Language, Record<string, string> | null> = {
  en: null,
  fr: null,
};

/**
 * Load translation file asynchronously
 * @param lang - The language to load
 * @returns Promise resolving to a dictionary of translations
 */
export async function loadTranslations(lang: Language): Promise<Record<string, string>> {
  // Return from cache if available
  if (translationCache[lang]) {
    return translationCache[lang] as Record<string, string>;
  }

  try {
    // Fetch the translation file
    const response = await fetch(`/translations/${lang}.json`);
    
    if (!response.ok) {
      throw new Error(`Failed to load ${lang} translations: ${response.statusText}`);
    }
    
    const translations = await response.json();
    
    // Cache the translations
    translationCache[lang] = translations;
    
    return translations;
  } catch (error) {
    console.error(`Error loading ${lang} translations:`, error);
    // Return empty object in case of error
    return {};
  }
}

/**
 * Clear the translation cache for testing or forced reloads
 * @param lang - Optional language to clear, or all if not specified
 */
export function clearTranslationCache(lang?: Language): void {
  if (lang) {
    translationCache[lang] = null;
  } else {
    translationCache = { en: null, fr: null };
  }
}

/**
 * Transform nested key path into flat key
 * @param section - The section of the translation
 * @param key - The key within the section
 * @returns A flat key in the format "section.key"
 */
export function createTranslationKey(section: string, key: string): string {
  return `${section}.${key}`;
}