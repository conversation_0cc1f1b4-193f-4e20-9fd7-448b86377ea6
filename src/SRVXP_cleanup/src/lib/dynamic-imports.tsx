"use client";

import dynamic from "next/dynamic";
import { Suspense, ComponentType, ReactNode, useState, useEffect } from "react";
import { FullPageLoading } from "@/components/ui/loading-spinner";

// Loading fallback component
export function DefaultLoadingFallback() {
  return <FullPageLoading />;
}

/**
 * Creates a dynamically imported component with Suspense handling
 *
 * @param importFn - Function that returns the component import
 * @param options - Options for dynamic loading
 * @returns Dynamically loaded component
 */
export function dynamicComponent<T>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  {
    ssr = false,
    loading = DefaultLoadingFallback,
    displayName
  }: {
    ssr?: boolean;
    loading?: () => ReactNode;
    displayName?: string;
  } = {}
) {
  const Component = dynamic(importFn, {
    loading,
    ssr,
  });

  // Create a wrapper component for better debugging
  const DynamicComponentWithSuspense = (props: T & { children?: ReactNode }) => (
    <Suspense fallback={loading ? loading() : <DefaultLoadingFallback />}>
      <Component {...props} />
    </Suspense>
  );

  // Set display name for debugging
  if (displayName) {
    DynamicComponentWithSuspense.displayName = `Dynamic(${displayName})`;
  }

  return DynamicComponentWithSuspense;
}

/**
 * Helper function for creating delayed loading components to prevent loading flicker on fast connections
 */
export function createDelayedLoader(delay: number = 300) {
  return function DelayedLoader() {
    // Only show loading indicator after delay
    const [showLoader, setShowLoader] = useState(false);

    useEffect(() => {
      const timer = setTimeout(() => {
        setShowLoader(true);
      }, delay);

      return () => clearTimeout(timer);
    }, []);

    if (!showLoader) {
      return null;
    }

    return <FullPageLoading />;
  };
}
