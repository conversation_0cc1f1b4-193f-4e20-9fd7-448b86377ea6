/**
 * Language Store Integration
 * 
 * This file provides integration between the Zustand language store and the existing LanguageContext.
 * It allows for a gradual migration from context-based language management to store-based language management.
 */

'use client'

import React, { useEffect, createContext, useContext, ReactNode } from 'react'
import { useLanguageStore, useTranslations } from '@/stores/useLanguageStore'
import { Language, TranslationDictionary } from '@/lib/translations'

// Create a context for the integration
interface LanguageIntegrationContextValue {
  isStoreEnabled: boolean
  enableStore: () => void
  disableStore: () => void
}

const LanguageIntegrationContext = createContext<LanguageIntegrationContextValue>({
  isStoreEnabled: false,
  enableStore: () => {},
  disableStore: () => {}
})

export const useLanguageIntegration = () => useContext(LanguageIntegrationContext)

interface LanguageIntegrationProviderProps {
  children: ReactNode
  initialEnabled?: boolean
}

/**
 * Provider component that manages the integration between LanguageContext and LanguageStore
 */
export function LanguageIntegrationProvider({ 
  children, 
  initialEnabled = false 
}: LanguageIntegrationProviderProps) {
  const [isStoreEnabled, setIsStoreEnabled] = React.useState(initialEnabled)
  
  const enableStore = () => setIsStoreEnabled(true)
  const disableStore = () => setIsStoreEnabled(false)
  
  return (
    <LanguageIntegrationContext.Provider value={{ isStoreEnabled, enableStore, disableStore }}>
      {children}
    </LanguageIntegrationContext.Provider>
  )
}

/**
 * Hook that provides the same interface as useLanguage but uses the Zustand store
 * This allows for a gradual migration from context to store
 */
export function useLanguageFromStore() {
  const {
    language,
    translations,
    isLoading,
    setLanguage,
    translate,
    t
  } = useTranslations()
  
  // Initial translations load
  useEffect(() => {
    if (Object.keys(translations).length === 0) {
      useLanguageStore.getState().loadTranslations()
    }
  }, [translations])
  
  return {
    language,
    translations,
    setLanguage,
    translate,
    isLoading,
    t
  }
}

/**
 * Component that synchronizes the LanguageContext with the LanguageStore
 * This ensures that both state management systems have the same data
 */
export function LanguageStoreSynchronizer() {
  const { isStoreEnabled } = useLanguageIntegration()
  
  // Import the original useLanguage from LanguageContext
  const { useLanguage } = require('@/lib/LanguageContext')
  const { 
    language: contextLanguage, 
    translations: contextTranslations 
  } = useLanguage()
  
  // Get store actions
  const setLanguage = useLanguageStore((state) => state.setLanguage)
  const setTranslations = useLanguageStore((state) => state.setTranslations)
  
  // Sync context to store if store is enabled
  useEffect(() => {
    if (isStoreEnabled) {
      // Update language in store if different from context
      const storeLanguage = useLanguageStore.getState().language
      if (contextLanguage !== storeLanguage) {
        setLanguage(contextLanguage)
      }
      
      // Update translations in store
      setTranslations(contextTranslations)
    }
  }, [isStoreEnabled, contextLanguage, contextTranslations, setLanguage, setTranslations])
  
  return null
}

/**
 * HOC that wraps a component to provide language from either context or store
 * based on the integration settings
 */
export function withLanguage<P extends object>(Component: React.ComponentType<P & { 
  language: Language
  translations: TranslationDictionary
  setLanguage: (language: Language) => void
  translate: (key: string) => string
  isLoading: boolean
}>) {
  return function WithLanguageComponent(props: P) {
    const { isStoreEnabled } = useLanguageIntegration()
    
    // Use either store or context based on settings
    const storeLanguage = useLanguageFromStore()
    const { useLanguage } = require('@/lib/LanguageContext')
    const contextLanguage = useLanguage()
    
    const language = isStoreEnabled ? storeLanguage : contextLanguage
    
    return <Component {...props} {...language} />
  }
}

/**
 * Custom hook that provides the same interface as useTranslation but uses either
 * the store or context based on integration settings
 */
export function useIntegratedTranslation() {
  const { isStoreEnabled } = useLanguageIntegration()
  
  // Use either store or context based on settings
  const { t: storeT, language: storeLanguage, isLoading: storeIsLoading } = useTranslations()
  const { useTranslation } = require('@/components/t')
  const { t: contextT, language: contextLanguage, isLoading: contextIsLoading } = useTranslation()
  
  return {
    t: isStoreEnabled ? storeT : contextT,
    language: isStoreEnabled ? storeLanguage : contextLanguage,
    isLoading: isStoreEnabled ? storeIsLoading : contextIsLoading
  }
}
