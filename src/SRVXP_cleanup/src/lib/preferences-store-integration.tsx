/**
 * Preferences Store Integration
 * 
 * This file provides integration between the Zustand preferences store and the existing preferences hooks.
 * It allows for a gradual migration from hook-based preferences management to store-based preferences management.
 */

'use client'

import React, { useEffect, createContext, useContext, ReactNode } from 'react'
import { usePreferencesStore, UserPreferences } from '@/stores/usePreferencesStore'
import { useUserStore } from '@/stores/useUserStore'
import { useTheme } from 'next-themes'

// Create a context for the integration
interface PreferencesIntegrationContextValue {
  isStoreEnabled: boolean
  enableStore: () => void
  disableStore: () => void
}

const PreferencesIntegrationContext = createContext<PreferencesIntegrationContextValue>({
  isStoreEnabled: false,
  enableStore: () => {},
  disableStore: () => {}
})

export const usePreferencesIntegration = () => useContext(PreferencesIntegrationContext)

interface PreferencesIntegrationProviderProps {
  children: ReactNode
  initialEnabled?: boolean
}

/**
 * Provider component that manages the integration between preferences hooks and PreferencesStore
 */
export function PreferencesIntegrationProvider({ 
  children, 
  initialEnabled = false 
}: PreferencesIntegrationProviderProps) {
  const [isStoreEnabled, setIsStoreEnabled] = React.useState(initialEnabled)
  
  const enableStore = () => setIsStoreEnabled(true)
  const disableStore = () => setIsStoreEnabled(false)
  
  return (
    <PreferencesIntegrationContext.Provider value={{ isStoreEnabled, enableStore, disableStore }}>
      {children}
    </PreferencesIntegrationContext.Provider>
  )
}

/**
 * Hook that provides the same interface as useUserPreferences but uses the Zustand store
 * This allows for a gradual migration from hooks to store
 */
export function usePreferencesFromStore() {
  const {
    theme,
    language,
    isLoading,
    isSaving,
    error: storeError,
    saveError,
    saveSuccess,
    fetchPreferences,
    updatePreferences,
    setTheme,
    setLanguage,
    userId
  } = usePreferencesStore.use.usePreferences()
  
  // Get the next-themes hook for theme management
  const { theme: nextTheme, setTheme: setNextTheme } = useTheme()
  
  // Initial preferences fetch
  useEffect(() => {
    if (userId) {
      fetchPreferences(userId)
    }
  }, [userId, fetchPreferences])
  
  // Sync theme with next-themes
  useEffect(() => {
    if (theme && theme !== 'system') {
      setNextTheme(theme)
    }
  }, [theme, setNextTheme])
  
  // Create a preferences object that matches the existing hook interface
  const preferences: UserPreferences = {
    theme,
    language
  }
  
  return {
    preferences,
    isLoading,
    isSaving,
    saveError,
    saveSuccess,
    updatePreferences: (newPreferences: UserPreferences) => {
      if (userId) {
        return updatePreferences(userId, newPreferences)
      }
      return Promise.resolve(false)
    }
  }
}

/**
 * Component that synchronizes the preferences hooks with the PreferencesStore
 * This ensures that both state management systems have the same data
 */
export function PreferencesStoreSynchronizer() {
  const { isStoreEnabled } = usePreferencesIntegration()
  
  // Import the original useUserPreferences hook
  const { useUserPreferences } = require('@/hooks/use-user-preferences')
  const { 
    preferences: hookPreferences
  } = useUserPreferences()
  
  // Get store actions
  const setPreferences = usePreferencesStore(state => state.setPreferences)
  const userId = useUserStore(state => state.user?.id)
  
  // Sync hook data to store if store is enabled
  useEffect(() => {
    if (isStoreEnabled && hookPreferences && userId) {
      setPreferences(hookPreferences)
    }
  }, [isStoreEnabled, hookPreferences, setPreferences, userId])
  
  return null
}

/**
 * HOC that wraps a component to provide preferences data from either hooks or store
 * based on the integration settings
 */
export function withPreferences<P extends object>(Component: React.ComponentType<P & { 
  preferences: UserPreferences | null
  isLoading: boolean
  isSaving: boolean
  saveError: Error | null
  saveSuccess: boolean
  updatePreferences: (preferences: UserPreferences) => Promise<boolean>
}>) {
  return function WithPreferencesComponent(props: P) {
    const { isStoreEnabled } = usePreferencesIntegration()
    
    // Use either store or hooks based on settings
    const storePreferences = usePreferencesFromStore()
    const { useUserPreferences } = require('@/hooks/use-user-preferences')
    const hookPreferences = useUserPreferences()
    
    const preferences = isStoreEnabled ? storePreferences : hookPreferences
    
    return <Component {...props} {...preferences} />
  }
}
