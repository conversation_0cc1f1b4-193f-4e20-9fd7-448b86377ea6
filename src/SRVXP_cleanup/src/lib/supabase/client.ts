import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  console.warn('Warning: Supabase environment variables are missing. Using placeholder values.')
}

// Cookie configuration optimized for OAuth session persistence
const cookieOptions = typeof window !== 'undefined' ? {
  cookies: {
    getAll() {
      if (typeof document === 'undefined') return []
      return document.cookie
        .split(';')
        .map(cookie => cookie.trim().split('='))
        .filter(([name]) => name)
        .map(([name, value]) => ({ name, value: decodeURIComponent(value || '') }))
    },
    setAll(cookiesToSet: Array<{ name: string; value: string; options?: any }>) {
      if (typeof document === 'undefined') return
      cookiesToSet.forEach(({ name, value, options = {} }) => {
        const cookieOptions = {
          path: '/',
          maxAge: 60 * 60 * 24 * 30, // 30 days
          sameSite: 'lax',
          secure: process.env.NODE_ENV === 'production',
          // Ensure cookies work with localhost domain
          domain: process.env.NODE_ENV === 'production' ? undefined : undefined, // Don't set domain for localhost
          ...options
        }

        const cookieString = [
          `${name}=${encodeURIComponent(value)}`,
          `Path=${cookieOptions.path}`,
          `Max-Age=${cookieOptions.maxAge}`,
          `SameSite=${cookieOptions.sameSite}`,
          cookieOptions.secure ? 'Secure' : '',
          cookieOptions.domain ? `Domain=${cookieOptions.domain}` : ''
        ].filter(Boolean).join('; ')

        document.cookie = cookieString
      })
    },
  },
} : {}

// Create browser client with proper cookie handling for OAuth
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey, cookieOptions)

// Export a function to create a new client (useful for hooks)
export function createClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey, cookieOptions)
}
