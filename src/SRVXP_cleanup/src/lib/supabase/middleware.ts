import { createServerClient } from '@supabase/ssr'
import { NextRequest, NextResponse } from 'next/server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => {
            const cookieOptions = {
              ...options,
              // Ensure cookies can be accessed by client-side code for OAuth
              httpOnly: false,
              // Use secure in production
              secure: process.env.NODE_ENV === 'production',
              // Allow cross-site requests for OAuth
              sameSite: 'lax' as const,
              // Set longer expiration for OAuth sessions
              maxAge: options?.maxAge || 60 * 60 * 24 * 30, // 30 days
              path: '/',
              // Don't set domain for localhost to avoid cross-domain issues
              domain: process.env.NODE_ENV === 'production' ? undefined : undefined,
            }

            // Set cookie on both request and response
            request.cookies.set(name, value)
            supabaseResponse.cookies.set(name, value, cookieOptions)
          })
        },
      },
    }
  )

  // Try to get the current session
  let session = null
  let sessionError = null

  try {
    console.log('Middleware: Attempting to get session...')
    const { data, error } = await supabase.auth.getSession()
    session = data?.session
    sessionError = error

    if (session) {
      console.log('Middleware: Session found for user:', session.user.email)
    } else {
      console.log('Middleware: No session found')

      // Check if this might be an external return or OAuth callback that needs session restoration
      const isExternalReturn = request.nextUrl.searchParams.has('session_id') ||
                              request.headers.get('referer')?.includes('stripe.com') ||
                              request.headers.get('referer')?.includes('checkout.stripe.com')

      // Check for OAuth success flag
      const hasOAuthSuccess = request.cookies.get('oauth_success')?.value === 'true'

      // Check if this is right after OAuth callback or on OAuth success page
      const isPostOAuth = request.nextUrl.pathname === '/dashboard' &&
                         request.headers.get('referer')?.includes('/auth/callback')

      // Check if we're on the OAuth success page
      const isOAuthSuccessPage = request.nextUrl.pathname === '/auth/oauth-success'

      if (isExternalReturn || hasOAuthSuccess || isPostOAuth || isOAuthSuccessPage) {
        console.log('External return or OAuth detected, attempting session refresh...', {
          isExternalReturn,
          hasOAuthSuccess,
          isPostOAuth,
          isOAuthSuccessPage
        })

        try {
          const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession()
          if (refreshData?.session && !refreshError) {
            session = refreshData.session
            sessionError = null
            console.log('Session refreshed successfully for external return/OAuth')

            // Clear OAuth success flag if it was used
            if (hasOAuthSuccess) {
              supabaseResponse.cookies.delete('oauth_success')
            }
          } else {
            console.log('Failed to refresh session:', refreshError)
          }
        } catch (refreshError) {
          console.error('Session refresh error:', refreshError)
        }
      }
    }
  } catch (error) {
    console.error('Error in middleware session check:', error)
    sessionError = error
  }

  // Add session info to response headers for debugging
  if (session) {
    supabaseResponse.headers.set('X-User-ID', session.user.id)
    supabaseResponse.headers.set('X-Session-Exists', 'true')
  } else {
    supabaseResponse.headers.set('X-Session-Exists', 'false')
  }

  // Ensure proper cache headers for auth-related routes
  supabaseResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
  supabaseResponse.headers.set('Pragma', 'no-cache')
  supabaseResponse.headers.set('Expires', '0')

  return { response: supabaseResponse, session }
}
