import { cn } from "@/lib/utils"

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl"
  className?: string
}

interface FullPageLoadingProps {
  className?: string
}

/**
 * Simple loading spinner component
 */
export function LoadingSpinner({ size = "md", className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8",
    xl: "h-12 w-12"
  }

  return (
    <div 
      className={cn(
        "animate-spin rounded-full border-2 border-blue-200 border-t-blue-600",
        sizeClasses[size],
        className
      )}
    />
  )
}

/**
 * Full page loading component with white background and centered blue spinner
 * This is the standardized loading component for the entire application
 */
export function FullPageLoading({ className }: FullPageLoadingProps) {
  return (
    <div className={cn(
      "fixed inset-0 bg-white z-50 flex items-center justify-center",
      className
    )}>
      <LoadingSpinner size="xl" />
    </div>
  )
}

/**
 * Inline loading component for smaller sections
 */
export function InlineLoading() {
  return (
    <div className="flex items-center justify-center py-8">
      <LoadingSpinner size="lg" />
    </div>
  )
} 