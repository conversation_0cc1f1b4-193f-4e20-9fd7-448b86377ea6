/**
 * App Initializer Component
 * 
 * This component initializes the app state when the app loads.
 * It ensures that all necessary data is loaded before rendering the app.
 */

'use client'

import { ReactNode, useEffect, useState } from 'react'
import { useInitializeApp } from '@/stores/useAppStore'
import { FullPageLoading } from '@/components/ui/loading-spinner'

interface AppInitializerProps {
  children: ReactNode
  fallback?: ReactNode
}

export function AppInitializer({ children, fallback }: AppInitializerProps) {
  const { initializeApp, isLoading } = useInitializeApp()
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeApp()
        setIsInitialized(true)
      } catch (err) {
        console.error('Failed to initialize app:', err)
        setError(err instanceof Error ? err : new Error('Unknown error initializing app'))
        // Still mark as initialized to avoid infinite loading
        setIsInitialized(true)
      }
    }
    
    initialize()
  }, [initializeApp])
  
  // Show loading state while initializing
  if (!isInitialized || isLoading) {
    return fallback || <FullPageLoading />
  }
  
  // Show error state if initialization failed
  if (error) {
    return (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="flex flex-col items-center space-y-4 p-4 text-center">
          <div className="rounded-full bg-destructive p-3 text-destructive-foreground">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
              <line x1="12" y1="9" x2="12" y2="13"></line>
              <line x1="12" y1="17" x2="12.01" y2="17"></line>
            </svg>
          </div>
          <h2 className="text-xl font-bold">Failed to initialize app</h2>
          <p className="text-muted-foreground">{error.message}</p>
          <button 
            className="mt-4 rounded bg-primary px-4 py-2 text-primary-foreground"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    )
  }
  
  // Render children when initialized
  return <>{children}</>
}
