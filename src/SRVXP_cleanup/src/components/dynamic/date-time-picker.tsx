"use client";

import { dynamicComponent } from "@/lib/dynamic-imports";
import { DateTimePickerV2Props } from "@/components/date-time-picker-v2";

// Load the date-time picker component dynamically
export const DynamicDateTimePicker = dynamicComponent<DateTimePickerV2Props>(
  () => import("@/components/date-time-picker-v2").then(mod => ({ default: mod.DateTimePickerV2 })),
  {
    ssr: false,
    displayName: "DateTimePickerV2",
  }
);
