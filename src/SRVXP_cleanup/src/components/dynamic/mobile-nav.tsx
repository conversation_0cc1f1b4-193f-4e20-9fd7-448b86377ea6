"use client";

import { dynamicComponent } from "@/lib/dynamic-imports";

// Define the props type for MobileNav if any
interface MobileNavProps {}

// Load the mobile navigation component dynamically
export const DynamicMobileNav = dynamicComponent<MobileNavProps>(
  () => import("@/components/layout/mobile-nav").then(mod => ({ default: mod.MobileNav })),
  {
    ssr: false,
    displayName: "MobileNav",
  }
);
