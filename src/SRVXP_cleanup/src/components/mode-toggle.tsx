"use client"

import { useTheme } from "next-themes"
import { Moon, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, Too<PERSON>ipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useAuth } from "@/lib/AuthContext"
import { useUserPreferences } from "@/hooks/use-user-preferences"

export function ModeToggle() {
  const { theme, setTheme } = useTheme()
  const { user } = useAuth()
  const { updatePreferences } = useUserPreferences()

  const toggleTheme = () => {
    const newTheme = theme === "dark" ? "light" : "dark"
    
    // Update theme immediately for better UX
    setTheme(newTheme)
    
    // If user is logged in, also save preference to database
    if (user) {
      updatePreferences({
        language: localStorage.getItem("language") as "fr" | "en" || "fr",
        theme: newTheme as "light" | "dark" | "system"
      })
      .catch(error => console.error("Failed to save theme preference:", error))
    }
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            onClick={toggleTheme}
            className="h-9 w-9 rounded-full flex items-center justify-center border border-border bg-background hover:bg-accent hover:text-accent-foreground"
          >
            <Sun className="h-[20px] w-[20px] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-[20px] w-[20px] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
            <span className="sr-only">Basculer le thème</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{theme === "dark" ? "Passer au mode clair" : "Passer au mode sombre"}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
