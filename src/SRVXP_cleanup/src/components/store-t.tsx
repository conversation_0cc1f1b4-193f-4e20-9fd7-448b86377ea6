/**
 * Store-based Translation Components
 * 
 * These components provide the same interface as the existing T component
 * but use the Zustand language store instead of the LanguageContext.
 */

'use client'

import { useTranslations } from '@/stores/useLanguageStore'
import { memo } from 'react'
import { useIntegratedTranslation } from '@/lib/language-store-integration'

interface TranslateProps {
  keyName: string
  params?: Record<string, string | number>
}

/**
 * StoreT component - Uses the language store directly
 * This component should be used when you want to explicitly use the store
 */
export const StoreT = memo(function StoreT({ keyName, params }: TranslateProps) {
  const { t, isLoading } = useTranslations()
  
  // If translations are still loading, show the key
  if (isLoading) {
    return <>{keyName}</>
  }
  
  return <>{t(keyName, params)}</>
})

/**
 * IntegratedT component - Uses either the store or context based on integration settings
 * This component should be used for gradual migration
 */
export const IntegratedT = memo(function IntegratedT({ keyName, params }: TranslateProps) {
  const { t, isLoading } = useIntegratedTranslation()
  
  // If translations are still loading, show the key
  if (isLoading) {
    return <>{keyName}</>
  }
  
  return <>{t(keyName, params)}</>
})

/**
 * Hook that provides translation functions from the store
 */
export const useStoreTranslation = () => {
  const { t, language, isLoading } = useTranslations()
  
  return { t, language, isLoading }
}
