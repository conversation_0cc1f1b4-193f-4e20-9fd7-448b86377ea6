"use client";

import { useEffect, ReactNode } from "react";
import { useAuth } from "@/lib/AuthContext";
import { useRouter } from "next/navigation";
import { FullPageLoading } from "@/components/ui/loading-spinner";

interface ProtectedRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

/**
 * Component to protect routes that require authentication
 * Redirects to signin page if not authenticated
 */
export function ProtectedRoute({ 
  children, 
  redirectTo = "/auth/sign-in" 
}: ProtectedRouteProps) {
  const { status, refresh } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // If auth state is loading, do nothing yet
    if (status === "loading") return;

    // If not authenticated, redirect to login
    if (status === "unauthenticated") {
      const currentPath = window.location.pathname;
      const redirect = encodeURIComponent(currentPath);
      // Redirect with the current path as a parameter
      router.push(`${redirectTo}?redirectedFrom=${redirect}`);
    }
  }, [status, router, redirectTo]);

  // Handle visibility change to recheck auth when user returns to tab
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        // Only refresh auth if there are specific indicators that auth might have changed
        const shouldRefresh = checkIfAuthRefreshNeeded()
        
        if (shouldRefresh) {
          refresh();
        }
      }
    };

    // Function to determine if auth refresh is needed
    const checkIfAuthRefreshNeeded = () => {
      // Check if user was away for a significant time (more than 5 minutes)
      const lastActiveTime = sessionStorage.getItem('last_active_time')
      const now = Date.now()
      const fiveMinutes = 5 * 60 * 1000
      
      if (lastActiveTime) {
        const timeSinceActive = now - parseInt(lastActiveTime)
        if (timeSinceActive < fiveMinutes) {
          return false // User wasn't away long enough to warrant refresh
        }
      }
      
      // Check for specific indicators that auth might have changed
      const hasStripeRedirect = sessionStorage.getItem('stripe_redirect') === 'true'
      const hasExternalReturn = sessionStorage.getItem('external_return') === 'true'
      const hasOAuthCallback = window.location.pathname.includes('/auth/callback')
      const hasOAuthParams = window.location.search.includes('code=') || window.location.search.includes('session_id')
      
      return hasStripeRedirect || hasExternalReturn || hasOAuthCallback || hasOAuthParams
    }

    // Track when user becomes inactive
    const handleVisibilityHidden = () => {
      if (document.visibilityState === "hidden") {
        sessionStorage.setItem('last_active_time', Date.now().toString())
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange);
    document.addEventListener("visibilitychange", handleVisibilityHidden);
    
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      document.removeEventListener("visibilitychange", handleVisibilityHidden);
    };
  }, [refresh]);

  // Show loading state while checking authentication
  if (status === "loading") {
    return <FullPageLoading />;
  }

  // If authenticated, render children
  return status === "authenticated" ? <>{children}</> : null;
}