"use client";

import Link from "next/link";
import Image from "next/image";
import { Mail } from "lucide-react";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";
import { useLanguage } from "@/lib/LanguageContext";

export function Footer() {
  const { language } = useLanguage();
  return (
    <footer className="border-t border-gray-200 py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 sm:gap-12">
          {/* Logo and Description */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center mb-6">
              <div className="w-8 h-8 flex items-center justify-center">
                <Image
                  src="/zaply-images/srvxp_logorevised.svg"
                  alt="Logo"
                  width={32}
                  height={32}
                  priority
                  suppressHydrationWarning
                  className="text-brandBlue fill-current"
                />
              </div>
              <span className="ml-4 text-xl font-bold text-[#212242]">
                <T keyName={translationKeys.landing.navbar.title} />
              </span>
            </Link>
            <p className="text-gray-600 mb-6 w-full sm:max-w-[66.7%]">
              <T keyName={translationKeys.landing.footer.description} />
            </p>
          </div>

          {/* Links moved to the right */}
          <div className="lg:justify-self-end">
            <ul className="space-y-3">
              <li>
                <Link href="mailto:<EMAIL>" className="text-gray-600 hover:text-primary transition-colors flex items-center">
                  <Mail className="h-4 w-4 mr-2" />
                  <T keyName={translationKeys.landing.footer.contactUs} />
                </Link>
              </li>
              <li><Link href={language === 'en' ? "/politique-de-confidentialite-EN" : "/politique-de-confidentialite"} className="text-gray-600 hover:text-primary transition-colors"><T keyName={translationKeys.landing.footer.privacyPolicy} /></Link></li>
              <li><Link href={language === 'en' ? "/conditions-utilisation-EN" : "/conditions-utilisation"} className="text-gray-600 hover:text-primary transition-colors"><T keyName={translationKeys.landing.footer.termsOfUse} /></Link></li>
              <li><Link href={language === 'en' ? "/conditions-generales-de-vente-EN" : "/conditions-generales-de-vente"} className="text-gray-600 hover:text-primary transition-colors"><T keyName={translationKeys.landing.footer.termsOfSale} /></Link></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-200 mt-12 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-gray-600 text-sm mb-4 sm:mb-0">
            <T keyName={translationKeys.landing.footer.copyright} />
          </p>
        </div>
      </div>
    </footer>
  );
}
