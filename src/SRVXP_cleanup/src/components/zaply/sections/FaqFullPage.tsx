"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/zaply/ui/accordion";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { useState, useEffect } from "react";

interface FaqItem {
  question: string;
  answer: string;
}

export function FaqFullPage() {
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Original FAQs from the home page
  const originalFaqs: FaqItem[] = [
    {
      question: "Qu'est-ce que Sans rendez-vous express propose exactement?",
      answer: "Sans rendez-vous express est un service qui vous aide à trouver rapidement des rendez-vous médicaux dans des cliniques sans rendez-vous près de chez vous. Notre plateforme connecte les patients avec des médecins disponibles pour des consultations d'ici 24 à 48 heures."
    },
    {
      question: "Quel est le délai d'attente pour obtenir un rendez-vous?",
      answer: "Chez Sans rendez-vous express, nous nous efforçons de trouver un rendez-vous avec un médecin le plus rapidement possible pour tous nos utilisateurs. En général, la plupart des rendez-vous sont trouvés en moins de 48 heures. Pour les grandes municipalités, la majorité des rendez-vous sont trouvés en moins de 24 heures."
    },
    {
      question: "Quelles sont les informations nécessaires pour prendre un rendez-vous?",
      answer: "Pour prendre un rendez-vous, vous devrez fournir vos informations de base (nom, prénom, date de naissance), vos coordonnées (téléphone, email) et votre numéro d'assurance maladie. Ces informations nous permettent de vous trouver le rendez-vous le plus approprié."
    },
    {
      question: "Comment est-ce que les rendez-vous sont réservés?",
      answer: "Après avoir soumis votre demande, notre système recherche automatiquement les disponibilités dans les cliniques participantes selon vos critères. Une fois qu'un rendez-vous correspondant est trouvé, vous recevez une confirmation par email et/ou SMS avec tous les détails du rendez-vous."
    },
    {
      question: "Qui peut utiliser ce service?",
      answer: "Notre service est accessible à tous les résidents du Québec possédant une carte d'assurance maladie valide. Nous servons les adultes et les enfants, que ce soit pour des problèmes de santé ponctuels ou des suivis médicaux réguliers."
    }
  ];

  // Double the FAQs by including original + duplicates (for temporary use)
  const faqs: FaqItem[] = [
    ...originalFaqs,
    {
      question: "Comment choisissez-vous les cliniques?",
      answer: "Les cliniques sont choisies en fonction du code postal que l'utilisateur a sélectionné pour effectuer sa recherche, ainsi que du périmètre de recherche qu'il est prêt à parcourir. Notre plateforme utilise ces informations pour identifier les cliniques les plus proches de l'emplacement spécifié par l'utilisateur. Cela garantit que les options de cliniques présentées correspondent aux besoins de localisation et de déplacement de chaque utilisateur, offrant ainsi une expérience de recherche personnalisée et pratique."
    },
    {
      question: "Est-ce que je peux choisir une clinique en particulier?",
      answer: "Malheureusement, l'option de choisir une clinique en particulier n'est pas disponible sur notre plateforme. Les cliniques sont sélectionnées en fonction du code postal que vous avez choisi pour effectuer votre recherche, ainsi que du périmètre de recherche que vous êtes prêt à parcourir. Cela garantit que les cliniques proposées correspondent aux critères de localisation que vous avez spécifiés"
    },
    {
      question: "Est-ce que je peux changer ma demande de rendez-vous en cours de route?",
      answer: "Oui, vous pouvez modifier votre demande de rendez-vous en cours de route. Les utilisateurs ont la possibilité de modifier ou d'annuler leur demande de rendez-vous sans frais, tant qu'aucun rendez-vous n'a été déjà réservé. Pour ce faire, les utilisateurs doivent simplement cliquer sur le lien pour modifier ou annuler un rendez-vous qui leur est envoyé par courriel après avoir soumis leur demande initiale. Cette fonctionnalité vous permet de gérer facilement votre rendez-vous en fonction de vos besoins et de votre emploi du temps."
    },
    {
      question: "Comment faire pour annuler un rendez-vous?",
      answer: "Pour annuler un rendez-vous, recherchez le courriel de confirmation que vous avez reçu après avoir effectué votre demande de rendez-vous et cliquez le lien pour modifier ou annuler votre rendez-vous. Vous pouvez également annuler un rendez-vous à travers le portail client. Si vous rencontrez des difficultés, n'hésitez pas à contacter notre service à la clientèle. Nous serons heureux de vous aider!"
    },
    {
      question: "Est-ce que Sans rendez-vous express propose des rendez-vous d'urgence?",
      answer: "Bien que nous ne proposons pas de services pour des situations d'urgence médicale, notre plateforme permet aux utilisateurs de trouver rapidement des rendez-vous avec des médecins pour des consultations régulières et des besoins de suivi médical. En cas d'urgence médicale, nous vous encourageons à contacter immédiatement les services d'urgence appropriés ou à se rendre à l'hôpital le plus proche."
    },
    {
      question: "Est-ce que Rendez-vous médecins Québec propose des consultations en ligne?",
      answer: "Actuellement, nous ne proposons pas de consultations en ligne sur notre plateforme. Cependant, nous travaillons constamment à améliorer nos services et à explorer de nouvelles options pour répondre aux besoins de nos utilisateurs. Restez à l'écoute pour toute mise à jour concernant les consultations en ligne sur Sans rendez-vous express."
    },
    {
      question: "Le service ne répond pas à mes attentes.",
      answer: "Nous sommes désolés d'apprendre que le service ne répond pas à vos attentes. Votre satisfaction est notre priorité absolue, et nous souhaitons résoudre tout problème que vous pourriez rencontrer. N'hésitez pas à nous contacter directement pour nous faire part de vos préoccupations et de vos commentaires. Notre équipe est là pour vous aider et pour trouver des solutions qui vous conviennent."
    },
    {
      question: "Comment est-ce que vous protégez mes renseignements personnels?",
      answer: "Chez Rendez-vous médecins Québec, nous accordons une grande importance à la protection de vos renseignements personnels. Nous nous engageons à respecter la confidentialité des informations que vous nous confiez. Conformément aux lois applicables concernant le régime d’assurance maladie du Québec (Loi sur l’assurance maladie, RLRQ c A-29) et la protection des renseignements personnels dans le secteur privé (Loi sur la protection des renseignements personnels dans le secteur privé, RLRQ, c P-39.1), nous avons mis en place des mesures pour garantir la sécurité et la confidentialité de vos données. Vos renseignements personnels seront supprimés de notre système sept (7) jours après la prise de rendez-vous, conformément à notre Politique de confidentialité."
    }
  ];

  return (
    <section
      className="py-16 lg:py-24 overflow-hidden"
      ref={ref}
    >
      <div
        className={`${
          isMounted && isIntersecting ? 'animate-fade-in-together' : 'opacity-0'
        }`}
      >
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-[52px] lg:text-[56px] xl:text-[60px] font-bold text-[#212244]">
            Foire aux questions
          </h1>
        </div>

        {/* Accordion 50% wider than the home page (max-w-3xl → max-w-[72rem]) */}
        <div className="max-w-5xl mx-auto px-2">
          <Accordion type="single" collapsible>
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="mb-6">
                <AccordionTrigger className="text-left text-lg font-medium">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 text-lg leading-relaxed">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
