"use client";

import { Check } from "lucide-react";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { useState, useEffect } from "react";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";

export function HowItWorksSection() {
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <section
      id="how-it-works-section"
      className="pt-0 pb-16 lg:pb-24 overflow-hidden"
      ref={ref}
    >
      <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16">
        {/* Image Side */}
        <div className={`w-full lg:w-1/2 flex justify-center lg:justify-start ${isMounted && isIntersecting ? 'animate-fade-in-up-1' : 'opacity-0'}`}>
          <img 
            src="/zaply-images/howitworks_image.png" 
            alt={translationKeys.landing.howItWorks.imageAlt} 
            className="rounded-xl w-full max-w-[500px] h-auto object-contain" 
          />
        </div>

        {/* Content */}
        <div className="w-full lg:w-1/2 space-y-8">
          <h2 className={`text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244] mb-6 md:mb-10 lg:mb-20 ${isMounted && isIntersecting ? 'animate-fade-in-up-2' : 'opacity-0'}`}>
            <T keyName={translationKeys.landing.howItWorks.title} />
          </h2>

          <div className="space-y-8">
            <div className={`flex items-start gap-4 ${isMounted && isIntersecting ? 'animate-fade-in-up-3' : 'opacity-0'}`}>
              <div className="flex-shrink-0 w-6 h-6 bg-[#16a349] rounded-full flex items-center justify-center mt-1">
                <Check className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-[#212244] mb-2"><T keyName={translationKeys.landing.howItWorks.customAppointments.title} /></h3>
                <p className="text-gray-600 text-lg">
                  <T keyName={translationKeys.landing.howItWorks.customAppointments.description} />
                </p>
              </div>
            </div>

            <div className={`flex items-start gap-4 ${isMounted && isIntersecting ? 'animate-fade-in-up-4' : 'opacity-0'}`}>
              <div className="flex-shrink-0 w-6 h-6 bg-[#16a349] rounded-full flex items-center justify-center mt-1">
                <Check className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-[#212244] mb-2"><T keyName={translationKeys.landing.howItWorks.easyManagement.title} /></h3>
                <p className="text-gray-600 text-lg">
                  <T keyName={translationKeys.landing.howItWorks.easyManagement.description} />
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
