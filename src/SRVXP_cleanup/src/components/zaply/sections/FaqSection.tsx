"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/zaply/ui/accordion";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { useState, useEffect } from "react";
import Link from "next/link";
import { T } from "@/components/t";
import { translationKeys } from "@/lib/translations";
import { useLanguage } from "@/lib/LanguageContext";

interface FaqItem {
  question: string | React.ReactNode;
  answer: string | React.ReactNode;
}

export function FaqSection() {
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const { language } = useLanguage();
  const faqs: FaqItem[] = [
    {
      question: <T keyName={translationKeys.landing.faq.questions[0].question} />,
      answer: <T keyName={translationKeys.landing.faq.questions[0].answer} />
    },
    {
      question: <T keyName={translationKeys.landing.faq.questions[1].question} />,
      answer: <T keyName={translationKeys.landing.faq.questions[1].answer} />
    },
    {
      question: <T keyName={translationKeys.landing.faq.questions[2].question} />,
      answer: <T keyName={translationKeys.landing.faq.questions[2].answer} />
    },
    {
      question: <T keyName={translationKeys.landing.faq.questions[3].question} />,
      answer: <T keyName={translationKeys.landing.faq.questions[3].answer} />
    },
    {
      question: <T keyName={translationKeys.landing.faq.questions[4].question} />,
      answer: <T keyName={translationKeys.landing.faq.questions[4].answer} />
    }
  ];

  return (
    <section
      id="faq"
      className="py-16 lg:py-24 overflow-hidden"
      ref={ref}
    >
      <div>
        <div className={`text-center mb-16 ${isMounted && isIntersecting ? 'animate-fade-in-up-1' : 'opacity-0'}`}>
          <h2 className="text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244]">
            <T keyName={translationKeys.landing.faq.title} />
          </h2>
        </div>

        <div className="max-w-3xl mx-auto px-2">
          <Accordion type="single" collapsible>
            {faqs.map((faq, index) => (
              <AccordionItem
                key={index}
                value={`item-${index}`}
                className={`mb-7 ${isMounted && isIntersecting ? `animate-fade-in-up-${index + 2} -mt-6` : 'opacity-0'}`}
              >
                <AccordionTrigger className="text-left text-lg font-medium">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 text-lg leading-relaxed">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>

          <div className={`mt-10 text-center ${isMounted && isIntersecting ? 'animate-fade-in-up-3' : 'opacity-0'}`}>
            <Link
            href={language === 'en' ? '/foire-aux-questions-EN' : '/foire-aux-questions'}
            className="text-lg font-medium text-brandBlue hover:underline inline-flex items-center"
            >
              <T keyName={translationKeys.landing.faq.viewFullFaq} />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2 h-5 w-5"
              >
                <path d="M5 12h14"/>
                <path d="m12 5 7 7-7 7"/>
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
