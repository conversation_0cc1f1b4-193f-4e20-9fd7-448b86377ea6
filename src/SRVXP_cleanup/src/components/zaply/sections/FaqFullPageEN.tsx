"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/zaply/ui/accordion";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { useState, useEffect } from "react";

interface FaqItem {
  question: string;
  answer: string;
}

export function FaqFullPageEN() {
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // English FAQs
  const faqs: FaqItem[] = [
    {
      question: "What exactly does Sans rendez-vous express offer?",
      answer: "Sans rendez-vous express is an online platform that facilitates the search and booking of appointments with doctors in Quebec. Our goal is to simplify the process for patients by allowing them to quickly find available appointments and providing them with easy access to a variety of healthcare professionals in their area."
    },
    {
      question: "What is the waiting time to get an appointment?",
      answer: "At Sans rendez-vous express, we strive to find an appointment with a doctor as quickly as possible for all our users. In general, most appointments are found within 48 hours. For larger municipalities, the majority of appointments are found within 24 hours. Our team works diligently to meet the needs of patients and to ensure quick access to necessary healthcare services."
    },
    {
      question: "What information is needed to schedule an appointment with a doctor via Sans rendez-vous express?",
      answer: "To schedule an appointment with a doctor through our platform, users will need to provide basic information such as their name, phone number, email address, and health insurance number. Once this information is provided, users can then specify the time, date, and location of the appointment according to their preferences. These details help us find an appointment that best matches the needs and availability of each user."
    },
    {
      question: "How are appointments booked?",
      answer: "After submitting your request, our system automatically searches for available appointments in participating clinics according to your preferences. Once an appropriate appointment is found, you will receive a confirmation by email and/or SMS with all the appointment details."
    },
    {
      question: "Who can use this service?",
      answer: "Our service is accessible to anyone holding a Quebec Health Insurance Card."
    },
    {
      question: "How do you choose the clinics?",
      answer: "Clinics are selected based on the postal code that the user has chosen for their search, as well as the search radius they are willing to travel. Our platform uses this information to identify the clinics closest to the location specified by the user. This ensures that the clinic options presented match each user's location and travel needs, providing a personalized and convenient search experience."
    },
    {
      question: "Can I choose a specific clinic?",
      answer: "Unfortunately, the option to choose a specific clinic is not available on our platform. Clinics are selected based on the postal code you have chosen for your search, as well as the search radius you are willing to travel. This ensures that the clinics offered match the location criteria you have specified."
    },
    {
      question: "Can I change my appointment request along the way?",
      answer: "Yes, you can modify your appointment request along the way. Users have the option to change or cancel their appointment request at no cost, as long as no appointment has already been booked. To do this, users simply need to click on the link to modify or cancel an appointment that is sent to them by email after submitting their initial request. This feature allows you to easily manage your appointment according to your needs and schedule."
    },
    {
      question: "How do I cancel an appointment?",
      answer: "To cancel an appointment, look for the confirmation email you received after making your appointment request and click the link to modify or cancel your appointment. Follow the instructions to cancel your appointment. If you encounter any difficulties, do not hesitate to contact our customer service. We will be happy to assist you!"
    },
    {
      question: "Does Sans rendez-vous express offer emergency appointments?",
      answer: "While we do not offer services for medical emergency situations, our platform allows users to quickly find appointments with doctors for regular consultations and medical follow-up needs. In case of a medical emergency, we encourage you to immediately contact the appropriate emergency services or go to the nearest hospital."
    },
    {
      question: "Does Sans rendez-vous express offer online consultations?",
      answer: "Currently, we do not offer online consultations on our platform. However, we are constantly working to improve our services and explore new options to meet the needs of our users. Stay tuned for any updates regarding online consultations at Sans rendez-vous express."
    },
    {
      question: "The service does not meet my expectations.",
      answer: "We are sorry to hear that the service does not meet your expectations. Your satisfaction is our top priority, and we want to resolve any issues you may encounter. Please do not hesitate to contact us directly to share your concerns and feedback. Our team is here to help and to find solutions that suit you."
    },
    {
      question: "How do you protect my personal information?",
      answer: "At Sans rendez-vous express, we place great importance on the protection of your personal information. We are committed to maintaining the confidentiality of the information you entrust to us. In accordance with applicable laws regarding the Quebec health insurance plan (Health Insurance Act, RLRQ c A-29) and the protection of personal information in the private sector (Act respecting the protection of personal information in the private sector, RLRQ, c P-39.1), we have implemented measures to ensure the security and confidentiality of your data. Your personal information will be deleted from our system seven (7) days after the appointment is made, in accordance with our Privacy Policy."
    }
  ];

  return (
    <section
      className="py-16 lg:py-24 overflow-hidden"
      ref={ref}
    >
      <div
        className={`${
          isMounted && isIntersecting ? 'animate-fade-in-together' : 'opacity-0'
        }`}
      >
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-[52px] lg:text-[56px] xl:text-[60px] font-bold text-[#212244]">
            Frequently Asked Questions
          </h1>
        </div>

        <div className="max-w-5xl mx-auto px-2">
          <Accordion type="single" collapsible>
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`} className="mb-6">
                <AccordionTrigger className="text-left text-lg font-medium">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 text-lg leading-relaxed">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
