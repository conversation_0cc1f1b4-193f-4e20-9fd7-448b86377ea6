"use client";

import { But<PERSON> } from "@/components/zaply/ui/button";
import Link from "next/link";
import { useScrollReveal } from "@/lib/zaply/hooks/useScrollReveal";
import { useState, useEffect } from "react";

export function CtaSection() {
  const { ref, isIntersecting } = useScrollReveal({
    threshold: 0.2,
    rootMargin: "-50px"
  });
  const [isMounted, setIsMounted] = useState(false);

  // Set isMounted to true after component mounts to prevent hydration mismatch
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <section
      className="bg-white text-[#212244] w-full pt-0 pb-16 overflow-hidden"
      ref={ref}
    >
      <div
        className="max-w-[1200px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col items-center"
      >
        <div className="bg-[#f8f9fb] rounded-xl py-24 px-6 sm:px-10 md:px-10 w-full max-w-6xl text-center">
          <h2
            className={`text-[42px] sm:text-[52px] m:text-[52px] lg:text-[56px] xl:text-[60px] leading-none font-bold text-[#212244] max-w-6xl mb-8 ${
              isMounted && isIntersecting ? 'animate-fade-in-up-1' : 'opacity-0'
            }`}
          >
            Ne tardez p​as à consulter <br /> <span className="text-brandBlue">un​ médecin.</span>
          </h2>

          <p
            className={`text-xl font-normal text-gray-600 max-w-3xl mx-auto mb-10 ${
              isMounted && isIntersecting ? 'animate-fade-in-up-2' : 'opacity-0'
            }`}
          >
            Laissez-nous vous trouvez un rendez-vous médical aujourd'hui.
          </p>

          <div className={`${isMounted && isIntersecting ? 'animate-fade-in-up-3' : 'opacity-0'}`}>
            <Button asChild size="lg" className="rounded-md font-medium text-base px-8 py-4 bg-brandBlue text-white hover:bg-brandBlue/90 transition-all duration-300 h-auto hover:shadow-lg group hover:scale-105">
              <Link href="/auth/sign-up">
                Trouvez un rendez-vous
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="ml-2 h-5 w-5 arrow-icon"
                >
                  <path d="M5 12h14"/>
                  <path d="m12 5 7 7-7 7"/>
                </svg>
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
