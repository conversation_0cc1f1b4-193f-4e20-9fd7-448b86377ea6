/**
 * Subscription Store
 *
 * This store manages subscription data, including plan type, billing period, and status.
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { supabase } from '@/lib/supabase/client'
import { createSelectors } from './utils'
import { createPersistConfig, createHybridStorage } from './persistence'
import { useUserStore } from './useUserStore'

// Define subscription types
export type SubscriptionPlanType = 'free' | 'individual' | 'family'
export type SubscriptionBillingPeriod = 'monthly' | 'annual'
export type SubscriptionStatus = 'active' | 'cancelled' | 'past_due' | 'trialing' | 'incomplete' | 'incomplete_expired'

export interface Subscription {
  id: string
  stripe_id: string
  user_id: string
  price_id: string
  stripe_price_id: string
  plan_type: SubscriptionPlanType
  billing_period?: SubscriptionBillingPeriod
  interval: 'month' | 'year'
  status: SubscriptionStatus
  current_period_start: number
  current_period_end: number
  cancel_at_period_end: boolean
  amount: number
  currency: string
  started_at: number
  customer_id: string
  metadata?: Record<string, any>
  canceled_at?: number | null
  ended_at?: number | null
  created_at?: string
  updated_at?: string
}

export interface SubscriptionDetails {
  hasSubscription: boolean
  planType: SubscriptionPlanType | null
  billingPeriod: SubscriptionBillingPeriod | null
  status: SubscriptionStatus | null
  currentPeriodStart: Date | null
  currentPeriodEnd: Date | null
  cancelAtPeriodEnd: boolean
  amount: number | null
  currency: string | null
  formattedAmount: string | null
  daysUntilRenewal: number | null
  isTrialing: boolean
  isPastDue: boolean
  isCancelled: boolean
}

// Define the store state
interface SubscriptionState {
  subscription: Subscription | null
  isLoading: boolean
  error: Error | null
  lastFetched: number | null
}

// Define the store actions
interface SubscriptionActions {
  // Fetch actions
  fetchSubscription: (userId: string) => Promise<Subscription | null>

  // Subscription actions
  updateSubscription: (subscriptionData: Partial<Subscription>) => Promise<boolean>
  cancelSubscription: (stripeId: string) => Promise<boolean>

  // State management actions
  setSubscription: (subscription: Subscription | null) => void
  setIsLoading: (isLoading: boolean) => void
  setError: (error: Error | null) => void
  clearError: () => void
  reset: () => void
}

// Combine state and actions
type SubscriptionStore = SubscriptionState & SubscriptionActions

// Define initial state
const initialState: SubscriptionState = {
  subscription: null,
  isLoading: false,
  error: null,
  lastFetched: null
}

// Cache duration in milliseconds
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Create the store
const createSubscriptionStore = () => {
  return create<SubscriptionStore>()(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,

        // Fetch actions
        fetchSubscription: async (userId: string) => {
          try {
            set({ isLoading: true, error: null })

            // Check if we have cached data that's still valid
            const { subscription, lastFetched } = get()
            const now = Date.now()

            if (subscription && lastFetched && now - lastFetched < CACHE_DURATION) {
              set({ isLoading: false })
              return subscription
            }

            // Fetch subscription from Supabase
            const { data, error } = await supabase
              .from('subscriptions')
              .select('*')
              .eq('user_id', userId)
              .eq('status', 'active')
              .in('plan_type', ['individual', 'family'])
              .maybeSingle()

            if (error) {
              throw error
            }

            // Log billing period information for debugging
            if (data) {
              console.log('Subscription billing info:', {
                interval: data.interval,
                billing_period: data.billing_period,
                derived_billing_period: data.billing_period ||
                  (data.interval === 'month' ? 'monthly' : data.interval === 'year' ? 'annual' : null)
              })
            }

            // Update state with subscription data
            set({
              subscription: data as Subscription | null,
              isLoading: false,
              lastFetched: Date.now()
            })

            return data as Subscription | null
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error fetching subscription')
            set({
              isLoading: false,
              error: err
            })
            console.error('Error fetching subscription:', error)
            return null
          }
        },

        // Subscription actions
        updateSubscription: async (subscriptionData: Partial<Subscription>) => {
          try {
            set({ isLoading: true, error: null })

            const currentSubscription = get().subscription
            if (!currentSubscription) {
              throw new Error('No subscription to update')
            }

            // Update subscription in Supabase
            const { error } = await supabase
              .from('subscriptions')
              .update(subscriptionData)
              .eq('id', currentSubscription.id)

            if (error) {
              throw error
            }

            // Update local state
            set({
              subscription: { ...currentSubscription, ...subscriptionData },
              isLoading: false
            })

            return true
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error updating subscription')
            set({
              isLoading: false,
              error: err
            })
            console.error('Error updating subscription:', error)
            return false
          }
        },

        cancelSubscription: async (stripeId: string) => {
          try {
            set({ isLoading: true, error: null })

            // Call the cancel subscription API
            const response = await fetch('/api/subscriptions/cancel', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ subscriptionId: stripeId }),
            })

            const result = await response.json()

            if (!response.ok) {
              throw new Error(result.error || 'Failed to cancel subscription')
            }

            // Update local state
            const currentSubscription = get().subscription
            if (currentSubscription) {
              set({
                subscription: {
                  ...currentSubscription,
                  cancel_at_period_end: true,
                  canceled_at: Math.floor(Date.now() / 1000)
                },
                isLoading: false
              })
            } else {
              set({ isLoading: false })
            }

            return true
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error cancelling subscription')
            set({
              isLoading: false,
              error: err
            })
            console.error('Error cancelling subscription:', error)
            return false
          }
        },

        // State management actions
        setSubscription: (subscription) => set({ subscription }),
        setIsLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        clearError: () => set({ error: null }),
        reset: () => set(initialState)
      }),
      createPersistConfig<SubscriptionStore>('subscription-store', {
        storage: createJSONStorage(() => createHybridStorage()),
        // Only persist specific parts of the state
        partialize: (state) => ({
          subscription: state.subscription,
          lastFetched: state.lastFetched
          // Don't persist loading states or errors
        }),
        // Set version for cache invalidation
        version: 1,
        // Handle rehydration completion
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Check if we need to refresh the data
            const { lastFetched } = state
            const now = Date.now()

            // If the data is older than the cache duration, refresh it
            if (!lastFetched || now - lastFetched > CACHE_DURATION) {
              // We need to use getState() here because the store might not be fully initialized yet
              setTimeout(() => {
                const userId = useUserStore.getState().user?.id
                if (userId) {
                  useSubscriptionStore.getState().fetchSubscription(userId)
                }
              }, 0)
            }
          }
        }
      })
    )
  )
}

// Create and export the store with selectors
export const useSubscriptionStore = createSelectors(createSubscriptionStore())

// Export common selectors as hooks for convenience
export const useSubscriptionDetails = (): SubscriptionDetails => {
  const subscription = useSubscriptionStore((state) => state.subscription)
  const isLoading = useSubscriptionStore((state) => state.isLoading)
  const error = useSubscriptionStore((state) => state.error)

  // Compute derived properties
  const hasSubscription = !!subscription && subscription.status === 'active'
  const planType = subscription?.plan_type || null

  // Determine billing period - prioritize the billing_period property, fall back to interval
  const billingPeriod = subscription?.billing_period ||
    (subscription?.interval === 'month' ? 'monthly' : subscription?.interval === 'year' ? 'annual' : null)
  const status = subscription?.status || null
  const currentPeriodStart = subscription?.current_period_start ? new Date(subscription.current_period_start * 1000) : null
  const currentPeriodEnd = subscription?.current_period_end ? new Date(subscription.current_period_end * 1000) : null
  const cancelAtPeriodEnd = subscription?.cancel_at_period_end || false
  const amount = subscription?.amount ? subscription.amount / 100 : null // Convert cents to dollars
  const currency = subscription?.currency || null

  // Format amount with currency
  const formattedAmount = amount !== null && currency
    ? new Intl.NumberFormat('en-US', { style: 'currency', currency: currency.toUpperCase() }).format(amount)
    : null

  // Calculate days until renewal
  const daysUntilRenewal = currentPeriodEnd
    ? Math.ceil((currentPeriodEnd.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
    : null

  // Subscription status flags
  const isTrialing = status === 'trialing'
  const isPastDue = status === 'past_due'
  const isCancelled = status === 'cancelled' || cancelAtPeriodEnd

  return {
    hasSubscription,
    planType,
    billingPeriod,
    status,
    currentPeriodStart,
    currentPeriodEnd,
    cancelAtPeriodEnd,
    amount,
    currency,
    formattedAmount,
    daysUntilRenewal,
    isTrialing,
    isPastDue,
    isCancelled
  }
}
