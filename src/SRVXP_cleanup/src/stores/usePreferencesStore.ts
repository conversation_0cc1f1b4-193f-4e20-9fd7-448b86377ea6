/**
 * Preferences Store
 * 
 * This store manages user preferences such as theme and language.
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { supabase } from '@/lib/supabase/client'
import { createSelectors } from './utils'
import { createPersistConfig, createHybridStorage } from './persistence'
import { useUserStore } from './useUserStore'

// Define the preferences type
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system'
  language: 'fr' | 'en'
}

// Define the store state
interface PreferencesState {
  preferences: UserPreferences
  isLoading: boolean
  isSaving: boolean
  error: Error | null
  saveError: Error | null
  saveSuccess: boolean
  lastFetched: number | null
}

// Define the store actions
interface PreferencesActions {
  // Preferences actions
  fetchPreferences: (userId: string) => Promise<UserPreferences>
  updatePreferences: (userId: string, preferences: Partial<UserPreferences>) => Promise<boolean>
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setLanguage: (language: 'fr' | 'en') => void
  
  // State management actions
  setPreferences: (preferences: UserPreferences) => void
  setIsLoading: (isLoading: boolean) => void
  setIsSaving: (isSaving: boolean) => void
  setError: (error: Error | null) => void
  setSaveError: (error: Error | null) => void
  setSaveSuccess: (success: boolean) => void
  clearErrors: () => void
  reset: () => void
}

// Combine state and actions
type PreferencesStore = PreferencesState & PreferencesActions

// Define default preferences
const defaultPreferences: UserPreferences = {
  theme: 'light',
  language: 'fr'
}

// Define initial state
const initialState: PreferencesState = {
  preferences: defaultPreferences,
  isLoading: false,
  isSaving: false,
  error: null,
  saveError: null,
  saveSuccess: false,
  lastFetched: null
}

// Cache duration in milliseconds
const CACHE_DURATION = 30 * 60 * 1000 // 30 minutes

// Create the store
const createPreferencesStore = () => {
  return create<PreferencesStore>()(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,
        
        // Preferences actions
        fetchPreferences: async (userId: string) => {
          try {
            set({ isLoading: true, error: null })
            
            // Check if we have cached preferences that are still valid
            const { preferences, lastFetched } = get()
            const now = Date.now()
            
            if (lastFetched && now - lastFetched < CACHE_DURATION) {
              set({ isLoading: false })
              return preferences
            }
            
            // Query the users table for preferences
            const { data, error } = await supabase
              .from('users')
              .select('language, theme')
              .eq('id', userId)
              .maybeSingle()
            
            if (error) {
              throw error
            }
            
            // Use default preferences if no data is found
            const userPreferences: UserPreferences = {
              language: (data?.language as 'fr' | 'en') || defaultPreferences.language,
              theme: (data?.theme as 'light' | 'dark' | 'system') || defaultPreferences.theme
            }
            
            set({ 
              preferences: userPreferences, 
              isLoading: false,
              lastFetched: Date.now()
            })
            
            return userPreferences
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error fetching preferences')
            set({ 
              isLoading: false, 
              error: err 
            })
            return defaultPreferences
          }
        },
        
        updatePreferences: async (userId: string, newPreferences: Partial<UserPreferences>) => {
          try {
            set({ isSaving: true, saveError: null, saveSuccess: false })
            
            // Update preferences in Supabase
            const { error } = await supabase
              .from('users')
              .update({
                ...(newPreferences.language && { language: newPreferences.language }),
                ...(newPreferences.theme && { theme: newPreferences.theme }),
                updated_at: new Date().toISOString()
              })
              .eq('id', userId)
            
            if (error) {
              throw error
            }
            
            // Update local state
            const currentPreferences = get().preferences
            set({
              preferences: {
                ...currentPreferences,
                ...newPreferences
              },
              isSaving: false,
              saveSuccess: true
            })
            
            // Store language in localStorage for quick access
            if (newPreferences.language) {
              localStorage.setItem('language', newPreferences.language)
              // Update HTML lang attribute
              if (typeof document !== 'undefined') {
                document.documentElement.lang = newPreferences.language
              }
            }
            
            // Auto-reset success message after 3 seconds
            setTimeout(() => {
              set({ saveSuccess: false })
            }, 3000)
            
            return true
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error updating preferences')
            set({ 
              isSaving: false, 
              saveError: err,
              saveSuccess: false
            })
            return false
          }
        },
        
        setTheme: (theme) => {
          const userId = useUserStore.getState().user?.id
          if (userId) {
            get().updatePreferences(userId, { theme })
          } else {
            // Just update local state if no user is logged in
            set((state) => ({
              preferences: {
                ...state.preferences,
                theme
              }
            }))
          }
        },
        
        setLanguage: (language) => {
          const userId = useUserStore.getState().user?.id
          if (userId) {
            get().updatePreferences(userId, { language })
          } else {
            // Update local state and localStorage if no user is logged in
            set((state) => ({
              preferences: {
                ...state.preferences,
                language
              }
            }))
            localStorage.setItem('language', language)
            // Update HTML lang attribute
            if (typeof document !== 'undefined') {
              document.documentElement.lang = language
            }
          }
        },
        
        // State management actions
        setPreferences: (preferences) => set({ preferences }),
        setIsLoading: (isLoading) => set({ isLoading }),
        setIsSaving: (isSaving) => set({ isSaving }),
        setError: (error) => set({ error }),
        setSaveError: (error) => set({ saveError: error }),
        setSaveSuccess: (success) => set({ saveSuccess: success }),
        clearErrors: () => set({ error: null, saveError: null }),
        reset: () => set(initialState)
      }),
      createPersistConfig<PreferencesStore>('preferences-store', {
        storage: createJSONStorage(() => createHybridStorage()),
        // Only persist specific parts of the state
        partialize: (state) => ({
          preferences: state.preferences,
          lastFetched: state.lastFetched
          // Don't persist loading states or errors
        }),
        // Set version for cache invalidation
        version: 1
      })
    )
  )
}

// Create and export the store with selectors
export const usePreferencesStore = createSelectors(createPreferencesStore())

// Export common selectors as hooks for convenience
export const usePreferences = () => {
  const preferences = usePreferencesStore((state) => state.preferences)
  const isLoading = usePreferencesStore((state) => state.isLoading)
  const isSaving = usePreferencesStore((state) => state.isSaving)
  const error = usePreferencesStore((state) => state.error)
  const saveError = usePreferencesStore((state) => state.saveError)
  const saveSuccess = usePreferencesStore((state) => state.saveSuccess)
  const fetchPreferences = usePreferencesStore((state) => state.fetchPreferences)
  const updatePreferences = usePreferencesStore((state) => state.updatePreferences)
  const setTheme = usePreferencesStore((state) => state.setTheme)
  const setLanguage = usePreferencesStore((state) => state.setLanguage)
  
  // Get user ID from auth store
  const user = useUserStore((state) => state.user)
  const userId = user?.id
  
  return {
    // State
    theme: preferences.theme,
    language: preferences.language,
    isLoading,
    isSaving,
    error,
    saveError,
    saveSuccess,
    
    // Actions
    fetchPreferences,
    updatePreferences,
    setTheme,
    setLanguage,
    
    // Helper
    userId
  }
}
