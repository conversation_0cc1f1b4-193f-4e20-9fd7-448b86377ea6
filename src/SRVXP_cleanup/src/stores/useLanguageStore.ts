/**
 * Language Store
 * 
 * This store manages language preferences and translations.
 * It extends the preferences store with language-specific functionality.
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { createSelectors } from './utils'
import { createPersistConfig, createHybridStorage } from './persistence'
import { usePreferencesStore } from './usePreferencesStore'
import { useUserStore } from './useUserStore'
import { Language, TranslationDictionary } from '@/lib/translations'
import { loadTranslations } from '@/lib/translation-utils'

// Define the store state
interface LanguageState {
  language: Language
  translations: TranslationDictionary
  isLoading: boolean
  error: Error | null
  lastFetched: number | null
}

// Define the store actions
interface LanguageActions {
  // Language actions
  setLanguage: (language: Language) => Promise<void>
  translate: (key: string) => string
  loadTranslations: () => Promise<void>
  
  // State management actions
  setTranslations: (translations: TranslationDictionary) => void
  setIsLoading: (isLoading: boolean) => void
  setError: (error: Error | null) => void
  clearError: () => void
  reset: () => void
}

// Combine state and actions
type LanguageStore = LanguageState & LanguageActions

// Define initial state
const initialState: LanguageState = {
  language: 'fr',
  translations: {},
  isLoading: true,
  error: null,
  lastFetched: null
}

// Create the store
const createLanguageStore = () => {
  return create<LanguageStore>()(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,
        
        // Language actions
        setLanguage: async (language: Language) => {
          try {
            set({ isLoading: true, error: null })
            
            // Update language in preferences store
            const userId = useUserStore.getState().user?.id
            if (userId) {
              await usePreferencesStore.getState().updatePreferences(userId, { language })
            } else {
              // Just update preferences store if no user is logged in
              usePreferencesStore.getState().setLanguage(language)
            }
            
            // Update language state
            set({ language })
            
            // Store the language preference locally
            localStorage.setItem('language', language)
            if (typeof document !== 'undefined') {
              document.documentElement.lang = language
            }
            
            // Load translations for the new language
            await get().loadTranslations()
          } catch (error) {
            const err = error instanceof Error ? error : new Error(`Failed to set language to ${language}`)
            set({ 
              isLoading: false, 
              error: err 
            })
            console.error(`Error setting language to ${language}:`, error)
          }
        },
        
        translate: (key: string) => {
          // Return the translation if it exists, otherwise return the key
          return get().translations[key] || key
        },
        
        loadTranslations: async () => {
          try {
            const language = get().language
            set({ isLoading: true, error: null })
            
            // Load translations for the current language
            const translationData = await loadTranslations(language)
            
            set({ 
              translations: translationData, 
              isLoading: false,
              lastFetched: Date.now()
            })
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Failed to load translations')
            set({ 
              isLoading: false, 
              error: err 
            })
            console.error('Error loading translations:', error)
          }
        },
        
        // State management actions
        setTranslations: (translations) => set({ translations }),
        setIsLoading: (isLoading) => set({ isLoading }),
        setError: (error) => set({ error }),
        clearError: () => set({ error: null }),
        reset: () => set(initialState)
      }),
      createPersistConfig<LanguageStore>('language-store', {
        storage: createJSONStorage(() => createHybridStorage()),
        // Only persist specific parts of the state
        partialize: (state) => ({
          language: state.language,
          lastFetched: state.lastFetched
          // Don't persist translations, loading states, or errors
        }),
        // Set version for cache invalidation
        version: 1,
        // Handle rehydration completion
        onRehydrateStorage: () => (state) => {
          if (state) {
            // Sync with preferences store
            const preferencesLanguage = usePreferencesStore.getState().preferences.language
            
            // If the language in preferences is different from the one in this store,
            // update this store to match
            if (preferencesLanguage !== state.language) {
              useLanguageStore.setState({ language: preferencesLanguage })
            }
            
            // Load translations for the current language
            setTimeout(() => {
              useLanguageStore.getState().loadTranslations()
            }, 0)
          }
        }
      })
    )
  )
}

// Create and export the store with selectors
export const useLanguageStore = createSelectors(createLanguageStore())

// Export common selectors as hooks for convenience
export const useTranslations = () => {
  const language = useLanguageStore((state) => state.language)
  const translations = useLanguageStore((state) => state.translations)
  const isLoading = useLanguageStore((state) => state.isLoading)
  const error = useLanguageStore((state) => state.error)
  const translate = useLanguageStore((state) => state.translate)
  const setLanguage = useLanguageStore((state) => state.setLanguage)
  
  // Extended translate function that also handles parameter substitution
  const t = (key: string, params?: Record<string, string | number>) => {
    // If translations are still loading, return the key
    if (isLoading) {
      return key
    }
    
    let text = translate(key)
    
    // Replace parameters if any
    if (params) {
      Object.entries(params).forEach(([paramKey, value]) => {
        text = text.replace(`{{${paramKey}}}`, String(value))
      })
    }
    
    return text
  }
  
  return {
    language,
    translations,
    isLoading,
    error,
    translate,
    setLanguage,
    t
  }
}
