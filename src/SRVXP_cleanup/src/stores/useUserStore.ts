/**
 * User Store - Authentication
 *
 * This store manages user authentication state and provides actions for
 * signing in, signing out, and refreshing the session.
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { Session, User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase/client'
import { createSelectors } from './utils'
import { createPersistConfig, createHybridStorage, invalidateCache } from './persistence'

// Auth state types
export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated'

// Define the store state
interface UserState {
  user: User | null
  session: Session | null
  status: AuthStatus
  error: Error | null
  lastRefreshed: number | null
}

// Define the store actions
interface UserActions {
  // Authentication actions
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: Error }>
  signInWithGoogle: () => Promise<{ success: boolean; error?: Error }>
  signOut: (redirectUrl?: string) => Promise<void>
  refresh: () => Promise<void>

  // State management actions
  setUser: (user: User | null) => void
  setSession: (session: Session | null) => void
  setStatus: (status: AuthStatus) => void
  setError: (error: Error | null) => void
  clearError: () => void
  reset: () => void
}

// Combine state and actions
type UserStore = UserState & UserActions

// Define initial state
const initialState: UserState = {
  user: null,
  session: null,
  status: 'loading',
  error: null,
  lastRefreshed: null
}

// Cache duration in milliseconds
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

// Create the store
const createUserStore = () => {
  return create<UserStore>()(
    persist(
      (set, get) => ({
        // Initial state
        ...initialState,

        // Authentication actions
        signIn: async (email: string, password: string) => {
          try {
            set({ status: 'loading', error: null })

            const { data, error } = await supabase.auth.signInWithPassword({
              email,
              password,
            })

            if (error) {
              set({
                status: 'unauthenticated',
                error: error as Error
              })
              return { success: false, error: error as Error }
            }

            if (data.session) {
              set({
                user: data.session.user,
                session: data.session,
                status: 'authenticated',
                lastRefreshed: Date.now()
              })

              // Clear any old auth-related flags from sessionStorage
              const keysToRemove = []
              for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i)
                if (key && (key.includes('auth') || key.includes('logout') || key.includes('supabase'))) {
                  keysToRemove.push(key)
                }
              }
              keysToRemove.forEach(key => sessionStorage.removeItem(key as string))
            }

            return { success: true }
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error during sign in')
            set({
              status: 'unauthenticated',
              error: err
            })
            return { success: false, error: err }
          }
        },

        signInWithGoogle: async () => {
          try {
            set({ status: 'loading', error: null })

            // Get the current origin to ensure correct redirect URL
            const origin = window.location.origin;
            // Create the callback URL with explicit dashboard redirect
            const callbackUrl = `${origin}/auth/callback?redirectTo=/dashboard`;

            console.log(`Setting up Google auth with callback URL: ${callbackUrl}`);

            const { data, error } = await supabase.auth.signInWithOAuth({
              provider: 'google',
              options: {
                redirectTo: callbackUrl
              }
            })

            if (error) {
              set({
                status: 'unauthenticated',
                error: error as Error
              })
              return { success: false, error: error as Error }
            }

            return { success: true }
          } catch (error) {
            const err = error instanceof Error ? error : new Error('Unknown error during Google sign in')
            set({
              status: 'unauthenticated',
              error: err
            })
            return { success: false, error: err }
          }
        },

        signOut: async (redirectUrl: string = '/') => {
          try {
            set({ status: 'loading', error: null })

            // Sign out with Supabase
            await supabase.auth.signOut()

            // Reset store state
            set({
              user: null,
              session: null,
              status: 'unauthenticated',
              error: null,
              lastRefreshed: null
            })

            // Clear any other potential local storage tokens
            localStorage.removeItem('supabase.auth.token')

            // Reset theme to system default when logging out
            localStorage.removeItem('theme')

            // Clear user-specific preferences cache
            const userId = get().user?.id
            if (userId) {
              localStorage.removeItem(`user_preferences_${userId}`)

              // Invalidate all user-related caches
              invalidateCache('user-store')
              invalidateCache('preferences-store')
              invalidateCache('subscription-store')
              invalidateCache('appointment-history-store')
            }

            // Redirect if in browser context
            if (typeof window !== 'undefined') {
              window.location.href = redirectUrl
            }
          } catch (error) {
            console.error('Error during sign out:', error)
            // Still reset the state even if there's an error
            set({
              user: null,
              session: null,
              status: 'unauthenticated',
              error: error instanceof Error ? error : new Error('Unknown error during sign out'),
              lastRefreshed: null
            })
          }
        },

        refresh: async () => {
          try {
            const currentStatus = get().status

            // Only set loading if we're not already authenticated
            if (currentStatus !== 'authenticated') {
              set({ status: 'loading', error: null })
            }

            // Get current session from Supabase
            const { data, error } = await supabase.auth.getSession()

            if (error) {
              throw error
            }

            if (data.session) {
              set({
                user: data.session.user,
                session: data.session,
                status: 'authenticated',
                lastRefreshed: Date.now()
              })
            } else {
              set({
                user: null,
                session: null,
                status: 'unauthenticated',
                lastRefreshed: Date.now()
              })
            }
          } catch (error) {
            console.error('Error refreshing auth state:', error)
            set({
              user: null,
              session: null,
              status: 'unauthenticated',
              error: error instanceof Error ? error : new Error('Unknown error during refresh'),
              lastRefreshed: Date.now()
            })
          }
        },

        // State management actions
        setUser: (user) => set({ user }),
        setSession: (session) => set({ session }),
        setStatus: (status) => set({ status }),
        setError: (error) => set({ error }),
        clearError: () => set({ error: null }),
        reset: () => set(initialState)
      }),
      createPersistConfig<UserStore>('user-store', {
        storage: createJSONStorage(() => createHybridStorage()),
        // Only persist specific parts of the state
        partialize: (state) => ({
          user: state.user,
          session: state.session,
          status: state.status,
          lastRefreshed: state.lastRefreshed
          // Don't persist error
        }),
        // Set version for cache invalidation
        version: 1,
        // Handle rehydration completion
        onRehydrateStorage: () => (state) => {
          // Setup auth state listener for changes
          if (typeof window !== 'undefined') {
            const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
              if (event === 'SIGNED_IN' && session) {
                useUserStore.setState({
                  user: session.user,
                  session: session,
                  status: 'authenticated',
                  lastRefreshed: Date.now()
                })
              } else if (event === 'SIGNED_OUT') {
                useUserStore.setState({
                  user: null,
                  session: null,
                  status: 'unauthenticated',
                  lastRefreshed: Date.now()
                })
              }
            })

            // Check if we need to refresh the session
            if (state) {
              const { lastRefreshed } = state
              const now = Date.now()

              // If the session is older than the cache duration, refresh it
              if (!lastRefreshed || now - lastRefreshed > CACHE_DURATION) {
                // We need to use getState() here because the store might not be fully initialized yet
                setTimeout(() => {
                  useUserStore.getState().refresh()
                }, 0)
              }
            }
          }
        }
      })
    )
  )
}

// Create and export the store with selectors
export const useUserStore = createSelectors(createUserStore())

// Export common selectors as hooks for convenience
export const useAuthStatus = () => useUserStore((state) => state.status)
export const useCurrentUser = () => useUserStore((state) => state.user)
export const useIsAuthenticated = () => useUserStore((state) => state.status === 'authenticated')
export const useAuthError = () => useUserStore((state) => state.error)
