/**
 * Zustand Store Entry Point
 *
 * This file serves as the main entry point for all Zustand stores in the application.
 * It exports all store hooks and utility functions for easy access.
 */

// Re-export all store hooks
// User authentication store
export {
  useUserStore,
  useAuthStatus,
  useCurrentUser,
  useIsAuthenticated,
  useAuthError
} from './useUserStore';

// User profile store
export {
  useUserProfileStore,
  useUserProfile,
  type UserProfile,
  type UserUpdateData
} from './useUserProfileStore';

// User preferences store
export {
  usePreferencesStore,
  usePreferences,
  type UserPreferences
} from './usePreferencesStore';

// Language store
export {
  useLanguageStore,
  useTranslations
} from './useLanguageStore';

// Appointment history store
export {
  useAppointmentHistoryStore,
  useAppointmentHistory,
  type AppointmentRequest,
  type CompletedAppointment,
  type CancelledAppointment
} from './useAppointmentHistoryStore';

// Subscription store
export {
  useSubscriptionStore,
  useSubscriptionDetails,
  type Subscription,
  type SubscriptionDetails,
  type SubscriptionPlanType,
  type SubscriptionBillingPeriod,
  type SubscriptionStatus
} from './useSubscriptionStore';

// Combined app store
export {
  useAppStore,
  useInitializeApp
} from './useAppStore';

// Export the template store (for reference only)
export { useTemplateStore, useTemplateValue } from './template';

// Export store utility functions
export * from './utils';
export * from './persistence';
