/**
 * Template Store
 *
 * This is a template for creating new Zustand stores.
 * Copy this file and modify it to create a new store.
 *
 * NOTE: This file is not meant to be used directly, it's just a template.
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { createSelectors } from './utils'
import { createPersistConfig, createHybridStorage, setExpiryTime } from './persistence'

// 1. Define the store state type
interface TemplateState {
  // State properties
  value: number
  status: 'idle' | 'loading' | 'error'
  error: Error | null
}

// 2. Define the store actions type
interface TemplateActions {
  // Action methods
  setValue: (value: number) => void
  increment: () => void
  decrement: () => void
  reset: () => void
  fetchValue: () => Promise<void>
}

// 3. Combine state and actions for the complete store type
type TemplateStore = TemplateState & TemplateActions

// 4. Define initial state
const initialState: TemplateState = {
  value: 0,
  status: 'idle',
  error: null
}

// 5. Create the store
const createTemplateStore = (initialValue = initialState) => {
  return create<TemplateStore>()(
    // Add persist middleware if you want to persist the store
    persist(
      // Store implementation
      (set, get) => ({
        // Initial state
        ...initialValue,

        // Actions
        setValue: (value) => set({ value }),

        increment: () => set((state) => ({ value: state.value + 1 })),

        decrement: () => set((state) => ({ value: state.value - 1 })),

        reset: () => set(initialState),

        fetchValue: async () => {
          try {
            set({ status: 'loading', error: null })

            // Example async operation
            const response = await fetch('/api/value')
            const data = await response.json()

            set({ value: data.value, status: 'idle' })
          } catch (error) {
            set({
              status: 'error',
              error: error instanceof Error ? error : new Error('Unknown error')
            })
          }
        }
      }),
      createPersistConfig<TemplateStore>('template-store', {
        storage: createJSONStorage(() => createHybridStorage()),
        // Optionally, only persist specific parts of the state
        partialize: (state) => ({
          value: state.value
          // Don't persist status and error
        }),
        // Set version for cache invalidation
        version: 1,
        // Handle rehydration completion
        onRehydrateStorage: () => (state) => {
          // Set expiry time for 1 hour
          if (state) {
            setExpiryTime('template-store', 60 * 60 * 1000)
          }
        }
      })
    )
  )
}

// 6. Export the store with selectors
export const useTemplateStore = createSelectors(createTemplateStore())

// 7. Export any utility functions specific to this store
export function useTemplateValue() {
  const value = useTemplateStore((state) => state.value)
  const increment = useTemplateStore((state) => state.increment)
  const decrement = useTemplateStore((state) => state.decrement)

  return { value, increment, decrement }
}
