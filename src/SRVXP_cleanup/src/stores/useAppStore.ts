/**
 * Combined App Store
 *
 * This store combines all other stores into a single store for easier access.
 * It provides a central place to access all application state.
 */

import { create } from 'zustand'
import { createSelectors } from './utils'
import { useUserStore } from './useUserStore'
import { useUserProfileStore } from './useUserProfileStore'
import { usePreferencesStore } from './usePreferencesStore'
import { useLanguageStore } from './useLanguageStore'
import { useAppointmentHistoryStore } from './useAppointmentHistoryStore'
import { useSubscriptionStore } from './useSubscriptionStore'

// Define the store state
interface AppState {
  // This store doesn't have its own state, it just combines other stores
}

// Define the store actions
interface AppActions {
  // Authentication actions
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: Error }>
  signInWithGoogle: () => Promise<{ success: boolean; error?: Error }>
  signOut: (redirectUrl?: string) => Promise<void>

  // Profile actions
  fetchProfile: (userId: string) => Promise<any>
  updateProfile: (userId: string, data: any) => Promise<boolean>

  // Preferences actions
  fetchPreferences: (userId: string) => Promise<any>
  updatePreferences: (userId: string, preferences: any) => Promise<boolean>
  setTheme: (theme: 'light' | 'dark' | 'system') => void
  setLanguage: (language: 'fr' | 'en') => void

  // Language actions
  translate: (key: string) => string
  loadTranslations: () => Promise<void>

  // Appointment actions
  fetchAppointmentRequests: (params?: any) => Promise<any[]>
  fetchCompletedAppointments: (params?: any) => Promise<any[]>
  fetchCancelledAppointments: (params?: any) => Promise<any[]>
  fetchAllAppointmentHistory: () => Promise<void>
  createAppointmentRequest: (data: any) => Promise<{ success: boolean; data?: any; error?: string }>
  cancelAppointmentRequest: (requestId: string, reason?: string) => Promise<{ success: boolean; error?: string }>

  // Appointment pagination actions
  fetchNextPage: (type: 'requests' | 'completed' | 'cancelled') => Promise<void>
  fetchPreviousPage: (type: 'requests' | 'completed' | 'cancelled') => Promise<void>
  goToPage: (type: 'requests' | 'completed' | 'cancelled', page: number) => Promise<void>
  setPageSize: (type: 'requests' | 'completed' | 'cancelled', size: number) => void

  // Appointment advanced selectors
  filterByDateRange: (startDate?: string, endDate?: string) => Promise<any[][]>
  filterByStatus: (status: 'pending' | 'in_progress' | 'completed' | 'cancelled') => any[]
  filterByType: (requestType: string) => any[]
  getAppointmentCountsByStatus: () => any
  getAppointmentCountsByMonth: (year?: number) => any[]
  getAppointmentSummary: () => any
  groupByStatus: () => any
  groupByMonth: (year?: number) => any
  groupByType: () => any
  sortByDate: (appointments: any[], ascending?: boolean) => any[]
  getAppointmentsInDateRange: (startDate: Date, endDate: Date) => any[]
  getAppointmentsForDay: (date: Date) => any[]
  getUpcomingAppointments: () => any[]

  // Subscription actions
  fetchSubscription: (userId: string) => Promise<any>
  updateSubscription: (subscriptionData: any) => Promise<boolean>
  cancelSubscription: (stripeId: string) => Promise<boolean>

  // Initialization action
  initializeApp: () => Promise<void>
}

// Combine state and actions
type AppStore = AppState & AppActions

// Create the store
const createAppStore = () => {
  return create<AppStore>()((set, get) => ({
    // Authentication actions
    signIn: async (email, password) => {
      return useUserStore.getState().signIn(email, password)
    },

    signInWithGoogle: async () => {
      return useUserStore.getState().signInWithGoogle()
    },

    signOut: async (redirectUrl) => {
      return useUserStore.getState().signOut(redirectUrl)
    },

    // Profile actions
    fetchProfile: async (userId) => {
      return useUserProfileStore.getState().fetchProfile(userId)
    },

    updateProfile: async (userId, data) => {
      return useUserProfileStore.getState().updateProfile(userId, data)
    },

    // Preferences actions
    fetchPreferences: async (userId) => {
      return usePreferencesStore.getState().fetchPreferences(userId)
    },

    updatePreferences: async (userId, preferences) => {
      return usePreferencesStore.getState().updatePreferences(userId, preferences)
    },

    setTheme: (theme) => {
      usePreferencesStore.getState().setTheme(theme)
    },

    setLanguage: (language) => {
      usePreferencesStore.getState().setLanguage(language)
      useLanguageStore.getState().setLanguage(language)
    },

    // Language actions
    translate: (key) => {
      return useLanguageStore.getState().translate(key)
    },

    loadTranslations: async () => {
      return useLanguageStore.getState().loadTranslations()
    },

    // Appointment actions
    fetchAppointmentRequests: async (params) => {
      return useAppointmentHistoryStore.getState().fetchAppointmentRequests(params)
    },

    fetchCompletedAppointments: async (params) => {
      return useAppointmentHistoryStore.getState().fetchCompletedAppointments(params)
    },

    fetchCancelledAppointments: async (params) => {
      return useAppointmentHistoryStore.getState().fetchCancelledAppointments(params)
    },

    fetchAllAppointmentHistory: async () => {
      return useAppointmentHistoryStore.getState().fetchAllAppointmentHistory()
    },

    createAppointmentRequest: async (data) => {
      return useAppointmentHistoryStore.getState().createAppointmentRequest(data)
    },

    cancelAppointmentRequest: async (requestId, reason) => {
      return useAppointmentHistoryStore.getState().cancelAppointmentRequest(requestId, reason)
    },

    // Appointment pagination actions
    fetchNextPage: async (type) => {
      return useAppointmentHistoryStore.getState().fetchNextPage(type)
    },

    fetchPreviousPage: async (type) => {
      return useAppointmentHistoryStore.getState().fetchPreviousPage(type)
    },

    goToPage: async (type, page) => {
      return useAppointmentHistoryStore.getState().goToPage(type, page)
    },

    setPageSize: (type, size) => {
      useAppointmentHistoryStore.getState().setPageSize(type, size)
    },

    // Appointment advanced selectors
    filterByDateRange: (startDate, endDate) => {
      // This function needs to call the store's fetch methods with date filters
      // We'll keep the implementation that calls the store's method
      return useAppointmentHistoryStore.getState().fetchAllAppointmentHistory({
        startDate,
        endDate
      })
    },

    filterByStatus: (status) => {
      // Filter by status manually
      const state = useAppointmentHistoryStore.getState()

      if (status === 'completed') {
        return state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean)
      } else if (status === 'cancelled') {
        return state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      } else {
        return state.appointmentRequests.filter(req => req.status === status)
      }
    },

    filterByType: (requestType) => {
      // Filter by type manually
      const state = useAppointmentHistoryStore.getState()

      // Get all appointments
      const allAppointments = [
        ...state.appointmentRequests,
        ...state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
        ...state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      ]

      return allAppointments.filter(appointment => appointment.request_type === requestType)
    },

    getAppointmentCountsByStatus: () => {
      // Calculate counts by status manually
      const state = useAppointmentHistoryStore.getState()

      return {
        pending: state.appointmentRequests.filter(req => req.status === 'pending').length,
        in_progress: state.appointmentRequests.filter(req => req.status === 'in_progress').length,
        completed: state.completedAppointments.length,
        cancelled: state.cancelledAppointments.length,
        total: state.appointmentRequests.length + state.completedAppointments.length + state.cancelledAppointments.length
      }
    },

    getAppointmentCountsByMonth: (year = new Date().getFullYear()) => {
      // Calculate counts by month manually
      const state = useAppointmentHistoryStore.getState()

      // Initialize counts for each month
      const monthlyCounts = Array(12).fill(0).map(() => ({
        pending: 0,
        in_progress: 0,
        completed: 0,
        cancelled: 0,
        total: 0
      }))

      // Count pending and in-progress appointments
      state.appointmentRequests.forEach(appointment => {
        const date = new Date(appointment.created_at)
        if (date.getFullYear() === year) {
          const month = date.getMonth()

          // Increment the appropriate counter
          if (appointment.status === 'pending') {
            monthlyCounts[month].pending++
          } else if (appointment.status === 'in_progress') {
            monthlyCounts[month].in_progress++
          }

          // Always increment the total
          monthlyCounts[month].total++
        }
      })

      // Count completed appointments
      state.completedAppointments.forEach(completed => {
        if (completed.completed_at) {
          const date = new Date(completed.completed_at)
          if (date.getFullYear() === year) {
            const month = date.getMonth()
            monthlyCounts[month].completed++
            monthlyCounts[month].total++
          }
        }
      })

      // Count cancelled appointments
      state.cancelledAppointments.forEach(cancelled => {
        if (cancelled.cancelled_at) {
          const date = new Date(cancelled.cancelled_at)
          if (date.getFullYear() === year) {
            const month = date.getMonth()
            monthlyCounts[month].cancelled++
            monthlyCounts[month].total++
          }
        }
      })

      return monthlyCounts
    },

    getAppointmentSummary: () => {
      // Calculate appointment summary manually
      const state = useAppointmentHistoryStore.getState()

      // Get counts by status
      const pendingCount = state.appointmentRequests.filter(req => req.status === 'pending').length
      const inProgressCount = state.appointmentRequests.filter(req => req.status === 'in_progress').length
      const completedCount = state.completedAppointments.length
      const cancelledCount = state.cancelledAppointments.length
      const totalCount = pendingCount + inProgressCount + completedCount + cancelledCount

      // Calculate rates
      const completionRate = totalCount > 0 ? (completedCount / totalCount) * 100 : 0
      const cancellationRate = totalCount > 0 ? (cancelledCount / totalCount) * 100 : 0

      // Get all appointments
      const allAppointments = [
        ...state.appointmentRequests,
        ...state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
        ...state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      ]

      // Get most recent appointment
      const sortedAppointments = [...allAppointments].sort(
        (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )
      const mostRecentAppointment = sortedAppointments.length > 0 ? sortedAppointments[0] : null

      return {
        counts: {
          pending: pendingCount,
          in_progress: inProgressCount,
          completed: completedCount,
          cancelled: cancelledCount,
          total: totalCount
        },
        completionRate,
        cancellationRate,
        mostRecentAppointment,
        totalAppointments: totalCount
      }
    },

    groupByStatus: () => {
      // Calculate group by status manually
      const state = useAppointmentHistoryStore.getState()
      return {
        pending: state.appointmentRequests.filter(req => req.status === 'pending'),
        in_progress: state.appointmentRequests.filter(req => req.status === 'in_progress'),
        completed: state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
        cancelled: state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      }
    },

    groupByMonth: (year = new Date().getFullYear()) => {
      // Calculate group by month manually
      const state = useAppointmentHistoryStore.getState()

      // Get all appointments
      const allAppointments = [
        ...state.appointmentRequests,
        ...state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
        ...state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      ]

      // Initialize arrays for each month
      const monthlyGroups: Record<number, any[]> = {}

      // Group appointments by month
      allAppointments.forEach(appointment => {
        const date = new Date(appointment.created_at)
        if (date.getFullYear() === year) {
          const month = date.getMonth()

          // Initialize the month array if it doesn't exist
          if (!monthlyGroups[month]) {
            monthlyGroups[month] = []
          }

          // Add the appointment to the month
          monthlyGroups[month].push(appointment)
        }
      })

      return monthlyGroups
    },

    groupByType: () => {
      // Calculate group by type manually
      const state = useAppointmentHistoryStore.getState()

      // Get all appointments
      const allAppointments = [
        ...state.appointmentRequests,
        ...state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
        ...state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      ]

      const groups: Record<string, any[]> = {}

      allAppointments.forEach(appointment => {
        const type = appointment.request_type || 'unknown'

        // Initialize the type array if it doesn't exist
        if (!groups[type]) {
          groups[type] = []
        }

        // Add the appointment to the type
        groups[type].push(appointment)
      })

      return groups
    },

    sortByDate: (appointments, ascending = false) => {
      // Sort appointments by date
      return [...appointments].sort((a, b) => {
        const dateA = new Date(a.created_at).getTime()
        const dateB = new Date(b.created_at).getTime()
        return ascending ? dateA - dateB : dateB - dateA
      })
    },

    getAppointmentsInDateRange: (startDate, endDate) => {
      // Get appointments in date range manually
      const state = useAppointmentHistoryStore.getState()

      // Get all appointments
      const allAppointments = [
        ...state.appointmentRequests,
        ...state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
        ...state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      ]

      return allAppointments.filter(appointment => {
        const appointmentDate = new Date(appointment.created_at)
        return appointmentDate >= startDate && appointmentDate <= endDate
      })
    },

    getAppointmentsForDay: (date) => {
      // Get appointments for a specific day manually
      const state = useAppointmentHistoryStore.getState()

      // Get all appointments
      const allAppointments = [
        ...state.appointmentRequests,
        ...state.completedAppointments.map(ca => ca.appointment_request).filter(Boolean),
        ...state.cancelledAppointments.map(ca => ca.appointment_request).filter(Boolean)
      ]

      const year = date.getFullYear()
      const month = date.getMonth()
      const day = date.getDate()

      return allAppointments.filter(appointment => {
        const appointmentDate = new Date(appointment.created_at)
        return (
          appointmentDate.getFullYear() === year &&
          appointmentDate.getMonth() === month &&
          appointmentDate.getDate() === day
        )
      })
    },

    getUpcomingAppointments: () => {
      // Get upcoming appointments manually
      const state = useAppointmentHistoryStore.getState()
      const now = new Date()

      return state.appointmentRequests
        .filter(req => {
          // Only include pending or in_progress appointments
          if (req.status !== 'pending' && req.status !== 'in_progress') {
            return false
          }

          // Check if the appointment date is in the future
          if (!req.request_details?.date || !req.request_details?.time) {
            return false
          }

          const appointmentDate = new Date(
            `${req.request_details.date}T${req.request_details.time}`
          )
          return appointmentDate > now
        })
        .sort((a, b) => {
          // Sort by appointment date (ascending)
          const dateA = new Date(
            `${a.request_details.date}T${a.request_details.time}`
          ).getTime()
          const dateB = new Date(
            `${b.request_details.date}T${b.request_details.time}`
          ).getTime()
          return dateA - dateB
        })
    },

    // Subscription actions
    fetchSubscription: async (userId) => {
      return useSubscriptionStore.getState().fetchSubscription(userId)
    },

    updateSubscription: async (subscriptionData) => {
      return useSubscriptionStore.getState().updateSubscription(subscriptionData)
    },

    cancelSubscription: async (stripeId) => {
      return useSubscriptionStore.getState().cancelSubscription(stripeId)
    },

    // Initialization action
    initializeApp: async () => {
      try {
        // First, refresh the auth state
        await useUserStore.getState().refresh()

        // Get the current user
        const user = useUserStore.getState().user

        if (user) {
          const userId = user.id

          // Load user data in parallel
          await Promise.all([
            // Load profile
            useUserProfileStore.getState().fetchProfile(userId),

            // Load preferences
            usePreferencesStore.getState().fetchPreferences(userId),

            // Load subscription
            useSubscriptionStore.getState().fetchSubscription(userId),

            // Load appointment history
            useAppointmentHistoryStore.getState().fetchAllAppointmentHistory()
          ])
        }

        // Always load translations
        await useLanguageStore.getState().loadTranslations()

        return
      } catch (error) {
        console.error('Error initializing app:', error)
        throw error
      }
    }
  }))
}

// Create and export the store with selectors
export const useAppStore = createSelectors(createAppStore())

// Export a hook for initializing the app
export const useInitializeApp = () => {
  const initializeApp = useAppStore((state) => state.initializeApp)
  const isAuthenticated = useUserStore((state) => state.status === 'authenticated')
  const isAuthLoading = useUserStore((state) => state.status === 'loading')
  const isProfileLoading = useUserProfileStore((state) => state.isLoading)
  const isPreferencesLoading = usePreferencesStore((state) => state.isLoading)
  const isLanguageLoading = useLanguageStore((state) => state.isLoading)
  const isAppointmentHistoryLoading = useAppointmentHistoryStore((state) => state.isLoading)
  const isSubscriptionLoading = useSubscriptionStore((state) => state.isLoading)

  // Compute overall loading state
  const isLoading =
    isAuthLoading ||
    isProfileLoading ||
    isPreferencesLoading ||
    isLanguageLoading ||
    isAppointmentHistoryLoading ||
    isSubscriptionLoading

  return {
    initializeApp,
    isAuthenticated,
    isLoading
  }
}

// Export a hook for accessing the combined store
export const useCombinedStore = () => {
  // User and authentication
  const user = useUserStore((state) => state.user)
  const status = useUserStore((state) => state.status)
  const signIn = useAppStore((state) => state.signIn)
  const signInWithGoogle = useAppStore((state) => state.signInWithGoogle)
  const signOut = useAppStore((state) => state.signOut)

  // User profile
  const profile = useUserProfileStore((state) => state.profile)
  const updateProfile = useAppStore((state) => state.updateProfile)
  const fetchProfile = useAppStore((state) => state.fetchProfile)

  // Preferences
  const preferences = usePreferencesStore((state) => state.preferences)
  const theme = usePreferencesStore((state) => state.theme)
  const updatePreferences = useAppStore((state) => state.updatePreferences)
  const fetchPreferences = useAppStore((state) => state.fetchPreferences)
  const setTheme = useAppStore((state) => state.setTheme)

  // Language
  const language = useLanguageStore((state) => state.language)
  const translations = useLanguageStore((state) => state.translations)
  const setLanguage = useAppStore((state) => state.setLanguage)
  const translate = useAppStore((state) => state.translate)

  // Subscription
  const subscription = useSubscriptionStore((state) => state.subscription)
  const fetchSubscription = useAppStore((state) => state.fetchSubscription)
  const updateSubscription = useAppStore((state) => state.updateSubscription)
  const cancelSubscription = useAppStore((state) => state.cancelSubscription)

  // Appointments
  const appointmentRequests = useAppointmentHistoryStore((state) => state.appointmentRequests)
  const completedAppointments = useAppointmentHistoryStore((state) => state.completedAppointments)
  const cancelledAppointments = useAppointmentHistoryStore((state) => state.cancelledAppointments)
  const pendingAppointments = appointmentRequests.filter((req) => req.status === 'pending')
  const inProgressAppointments = appointmentRequests.filter((req) => req.status === 'in_progress')

  // Appointment pagination
  const requestsPagination = useAppointmentHistoryStore((state) => state.requestsPagination)
  const completedPagination = useAppointmentHistoryStore((state) => state.completedPagination)
  const cancelledPagination = useAppointmentHistoryStore((state) => state.cancelledPagination)

  // Appointment actions
  const fetchAppointmentRequests = useAppStore((state) => state.fetchAppointmentRequests)
  const fetchCompletedAppointments = useAppStore((state) => state.fetchCompletedAppointments)
  const fetchCancelledAppointments = useAppStore((state) => state.fetchCancelledAppointments)
  const fetchAllAppointmentHistory = useAppStore((state) => state.fetchAllAppointmentHistory)
  const createAppointmentRequest = useAppStore((state) => state.createAppointmentRequest)
  const cancelAppointmentRequest = useAppStore((state) => state.cancelAppointmentRequest)

  // Appointment pagination actions
  const fetchNextPage = useAppStore((state) => state.fetchNextPage)
  const fetchPreviousPage = useAppStore((state) => state.fetchPreviousPage)
  const goToPage = useAppStore((state) => state.goToPage)
  const setPageSize = useAppStore((state) => state.setPageSize)

  // Appointment advanced selectors
  const filterByDateRange = useAppStore((state) => state.filterByDateRange)
  const filterByStatus = useAppStore((state) => state.filterByStatus)
  const filterByType = useAppStore((state) => state.filterByType)
  const getAppointmentCountsByStatus = useAppStore((state) => state.getAppointmentCountsByStatus)
  const getAppointmentCountsByMonth = useAppStore((state) => state.getAppointmentCountsByMonth)
  const getAppointmentSummary = useAppStore((state) => state.getAppointmentSummary)
  const groupByStatus = useAppStore((state) => state.groupByStatus)
  const groupByMonth = useAppStore((state) => state.groupByMonth)
  const groupByType = useAppStore((state) => state.groupByType)
  const sortByDate = useAppStore((state) => state.sortByDate)
  const getAppointmentsInDateRange = useAppStore((state) => state.getAppointmentsInDateRange)
  const getAppointmentsForDay = useAppStore((state) => state.getAppointmentsForDay)
  const getUpcomingAppointments = useAppStore((state) => state.getUpcomingAppointments)

  // Loading states
  const isAuthLoading = useUserStore((state) => state.status === 'loading')
  const isProfileLoading = useUserProfileStore((state) => state.isLoading)
  const isPreferencesLoading = usePreferencesStore((state) => state.isLoading)
  const isLanguageLoading = useLanguageStore((state) => state.isLoading)
  const isAppointmentHistoryLoading = useAppointmentHistoryStore((state) => state.isLoading)
  const isSubscriptionLoading = useSubscriptionStore((state) => state.isLoading)

  // Compute overall loading state
  const isLoading =
    isAuthLoading ||
    isProfileLoading ||
    isPreferencesLoading ||
    isLanguageLoading ||
    isAppointmentHistoryLoading ||
    isSubscriptionLoading

  return {
    // User and authentication
    user,
    status,
    isAuthenticated: status === 'authenticated',
    signIn,
    signInWithGoogle,
    signOut,

    // User profile
    profile,
    updateProfile,
    fetchProfile,

    // Preferences
    preferences,
    theme,
    updatePreferences,
    fetchPreferences,
    setTheme,

    // Language
    language,
    translations,
    setLanguage,
    translate,

    // Subscription
    subscription,
    fetchSubscription,
    updateSubscription,
    cancelSubscription,

    // Appointments
    appointmentRequests,
    completedAppointments,
    cancelledAppointments,
    pendingAppointments,
    inProgressAppointments,

    // Appointment pagination
    requestsPagination,
    completedPagination,
    cancelledPagination,

    // Appointment actions
    fetchAppointmentRequests,
    fetchCompletedAppointments,
    fetchCancelledAppointments,
    fetchAllAppointmentHistory,
    createAppointmentRequest,
    cancelAppointmentRequest,

    // Appointment pagination actions
    fetchNextPage,
    fetchPreviousPage,
    goToPage,
    setPageSize,

    // Appointment advanced selectors
    filterByDateRange,
    filterByStatus,
    filterByType,
    getAppointmentCountsByStatus,
    getAppointmentCountsByMonth,
    getAppointmentSummary,
    groupByStatus,
    groupByMonth,
    groupByType,
    sortByDate,
    getAppointmentsInDateRange,
    getAppointmentsForDay,
    getUpcomingAppointments,

    // Loading states
    isLoading,
    isAuthLoading,
    isProfileLoading,
    isPreferencesLoading,
    isLanguageLoading,
    isAppointmentHistoryLoading,
    isSubscriptionLoading,

    // Initialization
    initializeApp: useAppStore((state) => state.initializeApp)
  }
}
