/**
 * Tests for pagination functionality in the appointment history store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/pagination.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useAppointmentHistoryStore, PaginationParams } from '../useAppointmentHistoryStore'

// Mock fetch
global.fetch = vi.fn()

// Mock Supabase
vi.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      from: vi.fn().mockReturnThis(),
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      range: vi.fn().mockReturnThis(),
      limit: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      filter: vi.fn().mockReturnThis(),
      gte: vi.fn().mockReturnThis(),
      lte: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis()
    }
  }
})

// Import the mocked supabase
import { supabase } from '@/lib/supabase/client'

// Mock useUserStore
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' }
      })
    }
  }
})

describe('Appointment History Store - Pagination', () => {
  beforeEach(() => {
    // Reset the store to initial state
    useAppointmentHistoryStore.setState({
      appointmentRequests: [],
      completedAppointments: [],
      cancelledAppointments: [],
      requestsPagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasMore: false,
        pageSize: 10
      },
      completedPagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasMore: false,
        pageSize: 10
      },
      cancelledPagination: {
        currentPage: 1,
        totalPages: 0,
        totalCount: 0,
        hasMore: false,
        pageSize: 10
      },
      isLoading: false,
      error: null,
      lastFetched: null
    })
    
    // Reset all mocks
    vi.resetAllMocks()
  })
  
  test('fetchAppointmentRequests should use pagination parameters', async () => {
    // Mock Supabase response
    const mockResponse = {
      data: [{ id: 'req-1', status: 'pending' }],
      error: null,
      count: 15 // Total count for pagination
    }
    
    // Setup the mock to return our response
    supabase.from().select().order().range().eq().filter = vi.fn().mockResolvedValue(mockResponse)
    
    // Call the function with pagination params
    const params: PaginationParams = {
      page: 2,
      pageSize: 5
    }
    
    await useAppointmentHistoryStore.getState().fetchAppointmentRequests(params)
    
    // Verify the range was calculated correctly (5-9 for page 2 with pageSize 5)
    expect(supabase.range).toHaveBeenCalledWith(5, 9)
    
    // Verify the state was updated correctly
    const state = useAppointmentHistoryStore.getState()
    expect(state.requestsPagination.currentPage).toBe(2)
    expect(state.requestsPagination.pageSize).toBe(5)
    expect(state.requestsPagination.totalCount).toBe(15)
    expect(state.requestsPagination.totalPages).toBe(3) // 15 items with 5 per page = 3 pages
    expect(state.requestsPagination.hasMore).toBe(true) // Page 2 of 3 has more
  })
  
  test('fetchNextPage should increment the page number', async () => {
    // Setup initial state with pagination
    useAppointmentHistoryStore.setState({
      requestsPagination: {
        currentPage: 1,
        totalPages: 3,
        totalCount: 15,
        hasMore: true,
        pageSize: 5
      }
    })
    
    // Mock the goToPage function
    const goToPageMock = vi.fn()
    useAppointmentHistoryStore.setState({
      goToPage: goToPageMock
    })
    
    // Call fetchNextPage
    await useAppointmentHistoryStore.getState().fetchNextPage('requests')
    
    // Verify goToPage was called with the next page
    expect(goToPageMock).toHaveBeenCalledWith('requests', 2)
  })
  
  test('fetchPreviousPage should decrement the page number', async () => {
    // Setup initial state with pagination
    useAppointmentHistoryStore.setState({
      requestsPagination: {
        currentPage: 2,
        totalPages: 3,
        totalCount: 15,
        hasMore: true,
        pageSize: 5
      }
    })
    
    // Mock the goToPage function
    const goToPageMock = vi.fn()
    useAppointmentHistoryStore.setState({
      goToPage: goToPageMock
    })
    
    // Call fetchPreviousPage
    await useAppointmentHistoryStore.getState().fetchPreviousPage('requests')
    
    // Verify goToPage was called with the previous page
    expect(goToPageMock).toHaveBeenCalledWith('requests', 1)
  })
  
  test('setPageSize should update the page size and reset to page 1', async () => {
    // Setup initial state with pagination
    useAppointmentHistoryStore.setState({
      requestsPagination: {
        currentPage: 2,
        totalPages: 3,
        totalCount: 15,
        hasMore: true,
        pageSize: 5
      }
    })
    
    // Mock the fetchAppointmentRequests function
    const fetchMock = vi.fn()
    useAppointmentHistoryStore.setState({
      fetchAppointmentRequests: fetchMock
    })
    
    // Call setPageSize
    useAppointmentHistoryStore.getState().setPageSize('requests', 10)
    
    // Verify the page size was updated and page reset to 1
    const state = useAppointmentHistoryStore.getState()
    expect(state.requestsPagination.pageSize).toBe(10)
    expect(state.requestsPagination.currentPage).toBe(1)
    
    // Verify fetchAppointmentRequests was called with the new page size
    expect(fetchMock).toHaveBeenCalledWith({ pageSize: 10, page: 1 })
  })
})
