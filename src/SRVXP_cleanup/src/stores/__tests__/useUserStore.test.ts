/**
 * Tests for the user store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/useUserStore.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useUserStore } from '../useUserStore'
import { invalidateCache } from '../persistence'

// Mock Supabase
vi.mock('@/lib/supabase/client', () => {
  return {
    supabase: {
      auth: {
        getSession: vi.fn(),
        signInWithPassword: vi.fn(),
        signInWithOAuth: vi.fn(),
        signOut: vi.fn(),
        onAuthStateChange: vi.fn().mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } })
      }
    }
  }
})

// Import the mocked supabase
import { supabase } from '@/lib/supabase/client'

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value },
    removeItem: (key: string) => { delete store[key] },
    clear: () => { store = {} },
    getAllKeys: () => Object.keys(store)
  }
})()

// Mock sessionStorage
const mockSessionStorage = (() => {
  let store: Record<string, string> = {}
  let length = 0
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { 
      store[key] = value
      length = Object.keys(store).length
    },
    removeItem: (key: string) => { 
      delete store[key]
      length = Object.keys(store).length
    },
    clear: () => { 
      store = {}
      length = 0
    },
    key: (index: number) => Object.keys(store)[index] || null,
    length
  }
})()

// Mock window.location
const mockLocation = {
  href: '',
  origin: 'http://localhost:3000'
}

describe('User Store', () => {
  beforeEach(() => {
    // Clear localStorage and sessionStorage before each test
    mockLocalStorage.clear()
    mockSessionStorage.clear()
    
    // Reset the store to its initial state
    useUserStore.getState().reset()
    
    // Reset mocks
    vi.resetAllMocks()
    
    // Mock global objects
    Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })
    Object.defineProperty(global, 'sessionStorage', { value: mockSessionStorage })
    Object.defineProperty(global, 'window', { 
      value: { 
        location: mockLocation,
        localStorage: mockLocalStorage,
        sessionStorage: mockSessionStorage
      },
      writable: true
    })
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  test('should initialize with default values', () => {
    const state = useUserStore.getState()
    expect(state.user).toBeNull()
    expect(state.session).toBeNull()
    expect(state.status).toBe('loading')
    expect(state.error).toBeNull()
  })
  
  test('should handle successful sign in', async () => {
    // Mock successful sign in
    const mockUser = { id: 'user-123', email: '<EMAIL>' }
    const mockSession = { user: mockUser, access_token: 'token-123' }
    
    vi.mocked(supabase.auth.signInWithPassword).mockResolvedValue({
      data: { session: mockSession },
      error: null
    } as any)
    
    const { signIn } = useUserStore.getState()
    const result = await signIn('<EMAIL>', 'password')
    
    expect(result.success).toBe(true)
    expect(useUserStore.getState().user).toEqual(mockUser)
    expect(useUserStore.getState().session).toEqual(mockSession)
    expect(useUserStore.getState().status).toBe('authenticated')
    expect(useUserStore.getState().error).toBeNull()
  })
  
  test('should handle sign in error', async () => {
    // Mock sign in error
    const mockError = new Error('Invalid credentials')
    
    vi.mocked(supabase.auth.signInWithPassword).mockResolvedValue({
      data: { session: null },
      error: mockError
    } as any)
    
    const { signIn } = useUserStore.getState()
    const result = await signIn('<EMAIL>', 'wrong-password')
    
    expect(result.success).toBe(false)
    expect(result.error).toEqual(mockError)
    expect(useUserStore.getState().user).toBeNull()
    expect(useUserStore.getState().session).toBeNull()
    expect(useUserStore.getState().status).toBe('unauthenticated')
    expect(useUserStore.getState().error).toEqual(mockError)
  })
  
  test('should handle Google sign in', async () => {
    // Mock successful OAuth sign in
    vi.mocked(supabase.auth.signInWithOAuth).mockResolvedValue({
      data: { provider: 'google', url: 'https://accounts.google.com/o/oauth2/auth' },
      error: null
    } as any)
    
    const { signInWithGoogle } = useUserStore.getState()
    const result = await signInWithGoogle()
    
    expect(result.success).toBe(true)
    expect(vi.mocked(supabase.auth.signInWithOAuth)).toHaveBeenCalledWith({
      provider: 'google',
      options: {
        redirectTo: 'http://localhost:3000/auth/callback'
      }
    })
  })
  
  test('should handle sign out', async () => {
    // Set up initial authenticated state
    useUserStore.setState({
      user: { id: 'user-123', email: '<EMAIL>' } as any,
      session: { user: { id: 'user-123' } } as any,
      status: 'authenticated',
      error: null,
      lastRefreshed: Date.now()
    })
    
    // Mock successful sign out
    vi.mocked(supabase.auth.signOut).mockResolvedValue({ error: null } as any)
    
    const { signOut } = useUserStore.getState()
    await signOut()
    
    expect(useUserStore.getState().user).toBeNull()
    expect(useUserStore.getState().session).toBeNull()
    expect(useUserStore.getState().status).toBe('unauthenticated')
    expect(useUserStore.getState().error).toBeNull()
    expect(vi.mocked(supabase.auth.signOut)).toHaveBeenCalled()
  })
  
  test('should handle refresh with valid session', async () => {
    // Mock successful session retrieval
    const mockUser = { id: 'user-123', email: '<EMAIL>' }
    const mockSession = { user: mockUser, access_token: 'token-123' }
    
    vi.mocked(supabase.auth.getSession).mockResolvedValue({
      data: { session: mockSession },
      error: null
    } as any)
    
    const { refresh } = useUserStore.getState()
    await refresh()
    
    expect(useUserStore.getState().user).toEqual(mockUser)
    expect(useUserStore.getState().session).toEqual(mockSession)
    expect(useUserStore.getState().status).toBe('authenticated')
    expect(useUserStore.getState().error).toBeNull()
  })
  
  test('should handle refresh with no session', async () => {
    // Mock no session
    vi.mocked(supabase.auth.getSession).mockResolvedValue({
      data: { session: null },
      error: null
    } as any)
    
    const { refresh } = useUserStore.getState()
    await refresh()
    
    expect(useUserStore.getState().user).toBeNull()
    expect(useUserStore.getState().session).toBeNull()
    expect(useUserStore.getState().status).toBe('unauthenticated')
    expect(useUserStore.getState().error).toBeNull()
  })
  
  test('should handle refresh error', async () => {
    // Mock error during session retrieval
    const mockError = new Error('Network error')
    
    vi.mocked(supabase.auth.getSession).mockRejectedValue(mockError)
    
    const { refresh } = useUserStore.getState()
    await refresh()
    
    expect(useUserStore.getState().user).toBeNull()
    expect(useUserStore.getState().session).toBeNull()
    expect(useUserStore.getState().status).toBe('unauthenticated')
    expect(useUserStore.getState().error).toEqual(mockError)
  })
  
  test('should handle cache invalidation', () => {
    // Set up initial authenticated state
    useUserStore.setState({
      user: { id: 'user-123', email: '<EMAIL>' } as any,
      session: { user: { id: 'user-123' } } as any,
      status: 'authenticated',
      error: null,
      lastRefreshed: Date.now()
    })
    
    // Invalidate the cache
    invalidateCache('user-store')
    
    // Reset the store (simulating page refresh)
    const newStore = useUserStore
    
    // The store should be reset to initial state
    expect(newStore.getState().user).toBeNull()
    expect(newStore.getState().session).toBeNull()
    expect(newStore.getState().status).toBe('loading')
    expect(newStore.getState().error).toBeNull()
  })
})
