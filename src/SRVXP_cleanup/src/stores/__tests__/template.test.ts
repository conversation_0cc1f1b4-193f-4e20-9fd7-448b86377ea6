/**
 * Tests for the template store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/template.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useTemplateStore } from '../template'
import { invalidateCache } from '../persistence'

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value },
    removeItem: (key: string) => { delete store[key] },
    clear: () => { store = {} },
    getAllKeys: () => Object.keys(store)
  }
})()

// Mock window.localStorage
Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true
})

describe('Template Store', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    mockLocalStorage.clear()
    
    // Reset the store to its initial state
    useTemplateStore.getState().reset()
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  test('should initialize with default values', () => {
    const state = useTemplateStore.getState()
    expect(state.value).toBe(0)
    expect(state.status).toBe('idle')
    expect(state.error).toBeNull()
  })
  
  test('should update value with setValue', () => {
    const { setValue } = useTemplateStore.getState()
    setValue(42)
    expect(useTemplateStore.getState().value).toBe(42)
  })
  
  test('should increment value', () => {
    const { increment } = useTemplateStore.getState()
    increment()
    expect(useTemplateStore.getState().value).toBe(1)
  })
  
  test('should decrement value', () => {
    const { setValue, decrement } = useTemplateStore.getState()
    setValue(10)
    decrement()
    expect(useTemplateStore.getState().value).toBe(9)
  })
  
  test('should reset to initial state', () => {
    const { setValue, reset } = useTemplateStore.getState()
    setValue(42)
    reset()
    expect(useTemplateStore.getState().value).toBe(0)
  })
  
  test('should persist value to localStorage', () => {
    const { setValue } = useTemplateStore.getState()
    setValue(42)
    
    // Check that localStorage has an entry for the store
    expect(mockLocalStorage.getItem('template-store')).not.toBeNull()
    
    // Create a new store instance (simulating page refresh)
    const newStore = useTemplateStore
    
    // The new store should have the persisted value
    expect(newStore.getState().value).toBe(42)
  })
  
  test('should handle cache invalidation', () => {
    const { setValue } = useTemplateStore.getState()
    setValue(42)
    
    // Invalidate the cache
    invalidateCache('template-store')
    
    // Reset the store (simulating page refresh)
    const newStore = useTemplateStore
    
    // The store should be reset to initial state
    expect(newStore.getState().value).toBe(0)
  })
  
  test('should handle fetch errors', async () => {
    // Mock fetch to throw an error
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'))
    
    const { fetchValue } = useTemplateStore.getState()
    await fetchValue()
    
    const state = useTemplateStore.getState()
    expect(state.status).toBe('error')
    expect(state.error).toBeInstanceOf(Error)
    expect(state.error?.message).toBe('Network error')
  })
  
  test('should handle successful fetch', async () => {
    // Mock fetch to return a successful response
    global.fetch = vi.fn().mockResolvedValue({
      json: () => Promise.resolve({ value: 42 })
    })
    
    const { fetchValue } = useTemplateStore.getState()
    await fetchValue()
    
    const state = useTemplateStore.getState()
    expect(state.status).toBe('idle')
    expect(state.value).toBe(42)
    expect(state.error).toBeNull()
  })
})
