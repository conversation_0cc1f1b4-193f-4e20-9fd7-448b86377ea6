/**
 * Tests for the language store
 * 
 * To run these tests:
 * bun test src/stores/__tests__/useLanguageStore.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { useLanguageStore } from '../useLanguageStore'
import { usePreferencesStore } from '../usePreferencesStore'
import { useUserStore } from '../useUserStore'

// Mock translation-utils
vi.mock('@/lib/translation-utils', () => {
  return {
    loadTranslations: vi.fn().mockImplementation((lang) => {
      if (lang === 'fr') {
        return Promise.resolve({
          'common.hello': 'Bonjour',
          'common.goodbye': 'Au revoir'
        })
      } else {
        return Promise.resolve({
          'common.hello': 'Hello',
          'common.goodbye': 'Goodbye'
        })
      }
    }),
    clearTranslationCache: vi.fn()
  }
})

// Import the mocked loadTranslations
import { loadTranslations } from '@/lib/translation-utils'

// Mock usePreferencesStore
vi.mock('../usePreferencesStore', () => {
  return {
    usePreferencesStore: {
      getState: vi.fn().mockReturnValue({
        preferences: { language: 'fr', theme: 'light' },
        updatePreferences: vi.fn().mockResolvedValue(true),
        setLanguage: vi.fn()
      })
    }
  }
})

// Mock useUserStore
vi.mock('../useUserStore', () => {
  return {
    useUserStore: {
      getState: vi.fn().mockReturnValue({
        user: { id: 'user-123', email: '<EMAIL>' }
      })
    }
  }
})

// Mock localStorage
const mockLocalStorage = (() => {
  let store: Record<string, string> = {}
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => { store[key] = value },
    removeItem: (key: string) => { delete store[key] },
    clear: () => { store = {} },
    getAllKeys: () => Object.keys(store)
  }
})()

// Mock document
const mockDocument = {
  documentElement: {
    lang: ''
  }
}

describe('Language Store', () => {
  beforeEach(() => {
    // Clear localStorage before each test
    mockLocalStorage.clear()
    
    // Reset the store to its initial state
    useLanguageStore.getState().reset()
    
    // Reset mocks
    vi.resetAllMocks()
    
    // Mock global objects
    Object.defineProperty(global, 'localStorage', { value: mockLocalStorage })
    Object.defineProperty(global, 'document', { value: mockDocument })
    
    // Mock console methods
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  test('should initialize with default values', () => {
    const state = useLanguageStore.getState()
    expect(state.language).toBe('fr')
    expect(state.translations).toEqual({})
    expect(state.isLoading).toBe(true)
    expect(state.error).toBeNull()
  })
  
  test('should load translations successfully', async () => {
    const { loadTranslations } = useLanguageStore.getState()
    await loadTranslations()
    
    expect(useLanguageStore.getState().isLoading).toBe(false)
    expect(useLanguageStore.getState().error).toBeNull()
    expect(useLanguageStore.getState().translations).toEqual({
      'common.hello': 'Bonjour',
      'common.goodbye': 'Au revoir'
    })
  })
  
  test('should handle load translations error', async () => {
    // Mock loadTranslations to throw an error
    vi.mocked(loadTranslations).mockRejectedValueOnce(new Error('Failed to load translations'))
    
    const { loadTranslations } = useLanguageStore.getState()
    await loadTranslations()
    
    expect(useLanguageStore.getState().isLoading).toBe(false)
    expect(useLanguageStore.getState().error).toBeInstanceOf(Error)
    expect(useLanguageStore.getState().error?.message).toBe('Failed to load translations')
    expect(useLanguageStore.getState().translations).toEqual({})
  })
  
  test('should set language and load translations', async () => {
    const { setLanguage } = useLanguageStore.getState()
    await setLanguage('en')
    
    // Should update preferences store
    expect(usePreferencesStore.getState().updatePreferences).toHaveBeenCalledWith('user-123', { language: 'en' })
    
    // Should update localStorage and document.lang
    expect(localStorage.getItem('language')).toBe('en')
    expect(document.documentElement.lang).toBe('en')
    
    // Should update language state
    expect(useLanguageStore.getState().language).toBe('en')
    
    // Should load translations for the new language
    expect(loadTranslations).toHaveBeenCalledWith('en')
    
    // Should update translations
    expect(useLanguageStore.getState().translations).toEqual({
      'common.hello': 'Hello',
      'common.goodbye': 'Goodbye'
    })
  })
  
  test('should handle set language error', async () => {
    // Mock updatePreferences to throw an error
    vi.mocked(usePreferencesStore.getState().updatePreferences).mockRejectedValueOnce(new Error('Failed to update preferences'))
    
    const { setLanguage } = useLanguageStore.getState()
    await setLanguage('en')
    
    expect(useLanguageStore.getState().isLoading).toBe(false)
    expect(useLanguageStore.getState().error).toBeInstanceOf(Error)
    expect(console.error).toHaveBeenCalled()
  })
  
  test('should translate keys correctly', () => {
    // Set up translations
    useLanguageStore.setState({
      translations: {
        'common.hello': 'Bonjour',
        'common.goodbye': 'Au revoir'
      },
      isLoading: false
    })
    
    const { translate } = useLanguageStore.getState()
    
    expect(translate('common.hello')).toBe('Bonjour')
    expect(translate('common.goodbye')).toBe('Au revoir')
    expect(translate('common.unknown')).toBe('common.unknown') // Returns key if translation not found
  })
  
  test('should handle parameter substitution in translations', () => {
    // Set up translations
    useLanguageStore.setState({
      translations: {
        'greeting': 'Bonjour, {{name}}!',
        'count': 'Vous avez {{count}} messages'
      },
      isLoading: false
    })
    
    const { useTranslations } = require('../useLanguageStore')
    const { t } = useTranslations()
    
    expect(t('greeting', { name: 'John' })).toBe('Bonjour, John!')
    expect(t('count', { count: 5 })).toBe('Vous avez 5 messages')
  })
  
  test('should return key when translations are loading', () => {
    // Set loading state
    useLanguageStore.setState({
      isLoading: true
    })
    
    const { useTranslations } = require('../useLanguageStore')
    const { t } = useTranslations()
    
    expect(t('common.hello')).toBe('common.hello')
  })
})
