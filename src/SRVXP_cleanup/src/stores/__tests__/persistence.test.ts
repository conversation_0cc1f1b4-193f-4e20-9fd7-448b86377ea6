/**
 * Tests for Zustand store persistence utilities
 * 
 * To run these tests:
 * bun test src/stores/__tests__/persistence.test.ts
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'bun:test'
import { 
  createVersionedStorage, 
  setExpiryTime, 
  invalidateCache,
  invalidateCachesByPrefix,
  createPersistConfig,
  createHybridStorage
} from '../persistence'

// Mock localStorage
class LocalStorageMock {
  store: Record<string, string> = {}
  length: number = 0
  
  clear() {
    this.store = {}
    this.length = 0
  }
  
  getItem(key: string) {
    return this.store[key] || null
  }
  
  setItem(key: string, value: string) {
    this.store[key] = String(value)
    this.length = Object.keys(this.store).length
  }
  
  removeItem(key: string) {
    delete this.store[key]
    this.length = Object.keys(this.store).length
  }
  
  key(index: number) {
    return Object.keys(this.store)[index] || null
  }
}

describe('Persistence Utilities', () => {
  let mockStorage: LocalStorageMock
  
  beforeEach(() => {
    mockStorage = new LocalStorageMock()
    vi.spyOn(console, 'error').mockImplementation(() => {})
    vi.spyOn(console, 'log').mockImplementation(() => {})
  })
  
  afterEach(() => {
    vi.restoreAllMocks()
  })
  
  describe('createVersionedStorage', () => {
    test('should store data with version and timestamp', () => {
      const storage = createVersionedStorage(mockStorage, 1)
      storage.setItem('test-store', JSON.stringify({ count: 42 }))
      
      const rawStored = mockStorage.getItem('test-store')
      expect(rawStored).toBeTruthy()
      
      const parsed = JSON.parse(rawStored!)
      expect(parsed.__version).toBe(1)
      expect(parsed.__timestamp).toBeDefined()
      expect(parsed.data).toEqual({ count: 42 })
    })
    
    test('should return null for version mismatch', () => {
      const storage = createVersionedStorage(mockStorage, 2)
      mockStorage.setItem('test-store', JSON.stringify({
        __version: 1,
        __timestamp: Date.now(),
        data: { count: 42 }
      }))
      
      const result = storage.getItem('test-store')
      expect(result).toBeNull()
    })
    
    test('should return null for expired data', () => {
      const storage = createVersionedStorage(mockStorage, 1)
      mockStorage.setItem('test-store', JSON.stringify({
        __version: 1,
        __timestamp: Date.now(),
        __expires: Date.now() - 1000, // Expired
        data: { count: 42 }
      }))
      
      const result = storage.getItem('test-store')
      expect(result).toBeNull()
    })
    
    test('should return data for valid version and non-expired data', () => {
      const storage = createVersionedStorage(mockStorage, 1)
      mockStorage.setItem('test-store', JSON.stringify({
        __version: 1,
        __timestamp: Date.now(),
        __expires: Date.now() + 1000, // Not expired
        data: { count: 42 }
      }))
      
      const result = storage.getItem('test-store')
      expect(result).toBeTruthy()
      expect(JSON.parse(result!)).toEqual({ count: 42 })
    })
  })
  
  describe('setExpiryTime', () => {
    test('should set expiry time for existing store', () => {
      mockStorage.setItem('test-store', JSON.stringify({
        __version: 1,
        __timestamp: Date.now(),
        data: { count: 42 }
      }))
      
      const now = Date.now()
      vi.spyOn(Date, 'now').mockImplementation(() => now)
      
      setExpiryTime('test-store', 60000, mockStorage)
      
      const rawStored = mockStorage.getItem('test-store')
      const parsed = JSON.parse(rawStored!)
      expect(parsed.__expires).toBe(now + 60000)
    })
    
    test('should do nothing for non-existent store', () => {
      setExpiryTime('non-existent', 60000, mockStorage)
      expect(mockStorage.getItem('non-existent')).toBeNull()
    })
  })
  
  describe('invalidateCache', () => {
    test('should remove the store from storage', () => {
      mockStorage.setItem('test-store', 'some-data')
      invalidateCache('test-store', mockStorage)
      expect(mockStorage.getItem('test-store')).toBeNull()
    })
  })
  
  describe('invalidateCachesByPrefix', () => {
    test('should remove all stores with matching prefix', () => {
      mockStorage.setItem('user-store', 'user-data')
      mockStorage.setItem('user-preferences', 'preferences-data')
      mockStorage.setItem('other-store', 'other-data')
      
      invalidateCachesByPrefix('user-', mockStorage)
      
      expect(mockStorage.getItem('user-store')).toBeNull()
      expect(mockStorage.getItem('user-preferences')).toBeNull()
      expect(mockStorage.getItem('other-store')).toBe('other-data')
    })
  })
  
  describe('createPersistConfig', () => {
    test('should create config with default values', () => {
      const config = createPersistConfig('test-store')
      expect(config.name).toBe('test-store')
      expect(config.version).toBe(1)
    })
    
    test('should merge custom options', () => {
      const partialize = (state: any) => ({ count: state.count })
      const config = createPersistConfig('test-store', {
        version: 2,
        partialize
      })
      
      expect(config.name).toBe('test-store')
      expect(config.version).toBe(2)
      expect(config.partialize).toBe(partialize)
    })
  })
  
  describe('createHybridStorage', () => {
    const originalWindow = global.window
    
    afterEach(() => {
      global.window = originalWindow
    })
    
    test('should use memory storage in non-browser environment', () => {
      // @ts-ignore - Mocking window
      global.window = undefined
      
      const storage = createHybridStorage()
      storage.setItem('test-key', 'test-value')
      expect(storage.getItem('test-key')).toBe('test-value')
      
      storage.removeItem('test-key')
      expect(storage.getItem('test-key')).toBeNull()
    })
  })
})
