/**
 * Zustand Store Utilities
 * 
 * This file contains utility functions for working with Zustand stores.
 */

import { StoreApi, UseBoundStore } from 'zustand'

/**
 * Creates auto-generated selectors for a Zustand store
 * This allows for more convenient access to store state properties
 * 
 * Example usage:
 * const useBearStore = createSelectors(create<BearState>()(...))
 * const bears = useBearStore.use.bears()
 * 
 * @param store The Zustand store to create selectors for
 * @returns The store with added selectors
 */
type WithSelectors<S> = S extends { getState: () => infer T }
  ? S & { use: { [K in keyof T]: () => T[K] } }
  : never

export function createSelectors<S extends UseBoundStore<StoreApi<object>>>(
  store: S
) {
  const storeWithSelectors = store as WithSelectors<typeof store>
  storeWithSelectors.use = {}
  
  // Create a selector for each property in the store state
  for (const key of Object.keys(store.getState())) {
    Object.defineProperty(storeWithSelectors.use, key, {
      get: () => () => store((state) => state[key as keyof typeof state])
    })
  }
  
  return storeWithSelectors
}

/**
 * Helper function to create a logger middleware for Zustand stores
 * 
 * @param storeName The name of the store for logging
 * @returns A middleware function for logging state changes
 */
export const createStoreLogger = (storeName: string) => 
  (config: any) => 
    (set: any, get: any, api: any) => 
      config(
        (...args: any[]) => {
          const prevState = get()
          set(...args)
          const nextState = get()
          
          // Log state changes in development mode
          if (process.env.NODE_ENV === 'development') {
            console.group(`[${storeName}] State updated`)
            console.log('Prev:', prevState)
            console.log('Next:', nextState)
            console.log('Action:', args[0])
            console.groupEnd()
          }
        },
        get,
        api
      )
