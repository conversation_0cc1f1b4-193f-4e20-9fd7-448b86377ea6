#!/usr/bin/env node

/**
 * Test script to verify domain consistency between localhost and 0.0.0.0
 * This helps debug OAuth authentication issues related to domain mismatches
 */

const { execSync } = require('child_process');

console.log('🔍 Testing Domain Consistency for OAuth Authentication\n');

const domains = [
  'http://localhost:3002',
  'http://0.0.0.0:3002'
];

async function testDomain(domain) {
  console.log(`\n📍 Testing ${domain}:`);
  
  try {
    // Test basic connectivity
    console.log('  ✓ Testing connectivity...');
    const response = execSync(`curl -s -I "${domain}" | head -1`, { encoding: 'utf8' });
    console.log(`    Response: ${response.trim()}`);
    
    // Test test-oauth page
    console.log('  ✓ Testing /test-oauth page...');
    const testOAuthResponse = execSync(`curl -s -I "${domain}/test-oauth" | head -1`, { encoding: 'utf8' });
    console.log(`    Response: ${testOAuthResponse.trim()}`);
    
    // Test OAuth success page
    console.log('  ✓ Testing /auth/oauth-success page...');
    const oauthSuccessResponse = execSync(`curl -s -I "${domain}/auth/oauth-success" | head -1`, { encoding: 'utf8' });
    console.log(`    Response: ${oauthSuccessResponse.trim()}`);
    
    // Test cookie handling
    console.log('  ✓ Testing cookie handling...');
    const cookieTest = execSync(`curl -s -c /tmp/cookies_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.txt -b /tmp/cookies_${domain.replace(/[^a-zA-Z0-9]/g, '_')}.txt "${domain}" > /dev/null && echo "Cookies saved"`, { encoding: 'utf8' });
    console.log(`    Cookie test: ${cookieTest.trim()}`);
    
  } catch (error) {
    console.log(`    ❌ Error: ${error.message}`);
  }
}

async function testOAuthFlow() {
  console.log('\n🔐 Testing OAuth Configuration:');
  
  // Check environment variables
  console.log('  📋 Environment Variables:');
  console.log(`    NEXT_PUBLIC_APP_URL: ${process.env.NEXT_PUBLIC_APP_URL || 'Not set'}`);
  console.log(`    NODE_ENV: ${process.env.NODE_ENV || 'Not set'}`);
  
  // Check Supabase configuration
  console.log('\n  🔧 Supabase Configuration:');
  console.log(`    NEXT_PUBLIC_SUPABASE_URL: ${process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set'}`);
  console.log(`    NEXT_PUBLIC_SUPABASE_ANON_KEY: ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}`);
  
  // Test OAuth callback URL construction
  console.log('\n  🔄 OAuth Callback URL Test:');
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3002';
  const callbackUrl = `${baseUrl}/auth/callback?redirectTo=/dashboard`;
  console.log(`    Constructed callback URL: ${callbackUrl}`);
  
  // Test OAuth success URL construction
  const successUrl = `${baseUrl}/auth/oauth-success?redirectTo=/dashboard`;
  console.log(`    Constructed success URL: ${successUrl}`);
}

async function runTests() {
  try {
    // Test both domains
    for (const domain of domains) {
      await testDomain(domain);
    }
    
    // Test OAuth configuration
    await testOAuthFlow();
    
    console.log('\n📊 Summary:');
    console.log('  • Both localhost:3002 and 0.0.0.0:3002 should return HTTP 200');
    console.log('  • OAuth should be configured for localhost:3002 only');
    console.log('  • All redirects should use localhost:3002 to match OAuth config');
    console.log('  • Cookies should not specify domain to work with localhost');
    
    console.log('\n💡 Recommendations:');
    console.log('  1. Use localhost:3002 for all OAuth testing');
    console.log('  2. Ensure NEXT_PUBLIC_APP_URL is set to http://localhost:3002');
    console.log('  3. Configure Google OAuth for localhost:3002 only');
    console.log('  4. Configure Supabase redirects for localhost:3002 only');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the tests
runTests().then(() => {
  console.log('\n🏁 Domain consistency test completed');
}).catch((error) => {
  console.error('❌ Test script failed:', error);
});
