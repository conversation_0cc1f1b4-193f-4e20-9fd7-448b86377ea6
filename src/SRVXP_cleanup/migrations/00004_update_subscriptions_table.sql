-- Alter the existing subscriptions table to better support the subscription functionality required
-- Note: We're modifying the existing table rather than creating a new one

-- First, add missing columns if they don't exist
DO $$ 
BEGIN
  -- Add the plan_type column for individual/family plans if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_schema = 'public' AND table_name = 'subscriptions' AND column_name = 'plan_type'
  ) THEN
    ALTER TABLE subscriptions ADD COLUMN plan_type TEXT CHECK (plan_type IN ('free', 'individual', 'family')) DEFAULT 'free';
  END IF;

END $$;

-- Add constraints if needed
DO $$ 
BEGIN
  -- Add constraints if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'subscriptions_status_check'
  ) THEN
    ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_status_check 
      CHECK (status IN ('active', 'cancelled', 'past_due', 'trialing', 'incomplete', 'incomplete_expired'));
  END IF;
END $$;

-- <PERSON>reate function to track recent payments
CREATE OR REPLACE FUNCTION get_recent_payments(user_id_param TEXT, limit_count INTEGER DEFAULT 3)
RETURNS TABLE (payment_date TIMESTAMP WITH TIME ZONE, amount BIGINT, currency TEXT) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    to_timestamp(s.current_period_start) as payment_date,
    s.amount,
    s.currency
  FROM 
    subscriptions s
  WHERE 
    s.user_id = user_id_param
    AND s.status = 'active'
  ORDER BY 
    payment_date DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policies if not already present
DO $$ 
BEGIN
  -- Try to create RLS policies (will fail silently if they already exist)
  BEGIN
    ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
  EXCEPTION WHEN OTHERS THEN
    -- Table already has RLS enabled
  END;

  -- Create policies
  BEGIN
    CREATE POLICY "Users can view their own subscriptions" 
      ON subscriptions FOR SELECT
      USING (user_id::uuid = auth.uid());
  EXCEPTION WHEN OTHERS THEN
    -- Policy might already exist
  END;
END $$;
