# Database Migrations

This folder contains SQL migrations for the Supabase database.

## How to Apply Migrations

To apply migrations to your Supabase instance, you can use one of the following methods:

### Method 1: Supabase Dashboard SQL Editor

1. Log in to the Supabase dashboard at https://app.supabase.io
2. Navigate to your project
3. Go to the SQL Editor
4. Copy and paste the contents of the migration file
5. Execute the SQL

### Method 2: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Initialize Supabase (if not already done)
supabase init

# Apply migrations
supabase db push
```

## Migration Files

- `00001_create_family_members_table.sql` - Creates the family_members table with appropriate security policies
- `00002_create_user_preferences_table.sql` - Creates the user_preferences table for storing user language and theme preferences
