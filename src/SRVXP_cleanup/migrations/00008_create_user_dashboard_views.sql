-- Create view for each user's dashboard to display their recent appointment requests
CREATE OR REPLACE VIEW user_recent_appointments AS
SELECT 
  ar.id,
  ar.user_id,
  ar.status,
  ar.request_type,
  ar.request_details,
  ar.created_at,
  ar.updated_at,
  -- Include patient info
  CASE 
    WHEN ar.patient_id IS NULL THEN 'self'
    ELSE 'family member'
  END AS patient_type,
  COALESCE(fm.first_name, p.first_name) AS patient_first_name,
  COALESCE(fm.last_name, p.last_name) AS patient_last_name,
  COALESCE(fm.health_card, '') AS patient_health_card,
  COALESCE(fm.birth_date, NULL) AS patient_birth_date
FROM 
  appointment_requests ar
LEFT JOIN 
  family_members fm ON ar.patient_id = fm.id
LEFT JOIN 
  profiles p ON ar.user_id = p.id AND ar.patient_id IS NULL;

-- Add RLS policy to view
ALTER VIEW user_recent_appointments SECURITY INVOKER;

-- Create function to get a user's recent appointments with filtering
CREATE OR REPLACE FUNCTION get_user_recent_appointments(
  user_id_param UUID,
  status_filter TEXT DEFAULT NULL,
  limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
  id UUID,
  status TEXT,
  request_type TEXT,
  request_details JSONB,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  patient_type TEXT,
  patient_first_name TEXT,
  patient_last_name TEXT,
  patient_health_card TEXT,
  patient_birth_date TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ura.id,
    ura.status,
    ura.request_type,
    ura.request_details,
    ura.created_at,
    ura.updated_at,
    ura.patient_type,
    ura.patient_first_name,
    ura.patient_last_name,
    ura.patient_health_card,
    ura.patient_birth_date
  FROM 
    user_recent_appointments ura
  WHERE 
    ura.user_id = user_id_param
    AND (status_filter IS NULL OR ura.status = status_filter)
  ORDER BY 
    ura.created_at DESC
  LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
