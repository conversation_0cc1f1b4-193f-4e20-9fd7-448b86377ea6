-- Create appointment_requests table for storing user appointment requests
CREATE TABLE IF NOT EXISTS appointment_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  patient_id UUID REFERENCES family_members(id),
  status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')) DEFAULT 'pending',
  request_type TEXT NOT NULL,
  request_details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE appointment_requests ENABLE ROW LEVEL SECURITY;

-- Allow users to only see and modify their own appointment requests
CREATE POLICY "Users can view their own appointment requests" 
  ON appointment_requests
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own appointment requests" 
  ON appointment_requests
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own appointment requests" 
  ON appointment_requests
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Create indexes for performance
CREATE INDEX appointment_requests_user_id_idx ON appointment_requests(user_id);
CREATE INDEX appointment_requests_status_idx ON appointment_requests(status);
CREATE INDEX appointment_requests_created_at_idx ON appointment_requests(created_at);

-- Apply update timestamp trigger
CREATE TRIGGER update_appointment_requests_modtime
BEFORE UPDATE ON appointment_requests
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Create function to check subscription eligibility for appointment requests
CREATE OR REPLACE FUNCTION check_appointment_eligibility()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if user has an active paid subscription
  IF NOT EXISTS (
    SELECT 1 FROM subscriptions 
    WHERE user_id::uuid = NEW.user_id 
    AND status = 'active' 
    AND plan_type IN ('individual', 'family')
  ) THEN
    RAISE EXCEPTION 'Free users cannot create appointment requests';
  END IF;
  
  -- If patient_id is set (not self), verify user has family plan
  IF NEW.patient_id IS NOT NULL THEN
    IF NOT EXISTS (
      SELECT 1 FROM subscriptions 
      WHERE user_id::uuid = NEW.user_id 
      AND status = 'active' 
      AND plan_type = 'family'
    ) THEN
      RAISE EXCEPTION 'Only family plan members can book for others';
    END IF;
    
    -- Verify patient belongs to this user
    IF NOT EXISTS (
      SELECT 1 FROM family_members 
      WHERE id = NEW.patient_id 
      AND user_id = NEW.user_id
    ) THEN
      RAISE EXCEPTION 'Patient does not belong to this user';
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_appointment_before_insert
BEFORE INSERT ON appointment_requests
FOR EACH ROW
EXECUTE FUNCTION check_appointment_eligibility();

-- Create function to notify on new appointment request (for Redis integration)
CREATE OR REPLACE FUNCTION notify_new_appointment()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify('new_appointment', NEW.id::text);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_new_appointment
AFTER INSERT ON appointment_requests
FOR EACH ROW
EXECUTE FUNCTION notify_new_appointment();
