-- Create completed_appointments table for storing completed appointment information
CREATE TABLE IF NOT EXISTS completed_appointments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  appointment_request_id UUID NOT NULL REFERENCES appointment_requests(id),
  completion_details JSONB,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE completed_appointments ENABLE ROW LEVEL SECURITY;

-- Allow users to only view their own completed appointments (using parent relationship)
CREATE POLICY "Users can view their own completed appointments" 
  ON completed_appointments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM appointment_requests ar 
      WHERE ar.id = completed_appointments.appointment_request_id 
      AND ar.user_id = auth.uid()
    )
  );

-- Create indexes for performance
CREATE INDEX completed_appointments_request_id_idx ON completed_appointments(appointment_request_id);
CREATE INDEX completed_appointments_completed_at_idx ON completed_appointments(completed_at);

-- Create trigger function to update the parent appointment request status
CREATE OR REPLACE FUNCTION update_appointment_status_on_completion()
R<PERSON>URNS TRIGGER AS $$
BEGIN
  -- Update the parent appointment request status to completed
  UPDATE appointment_requests
  SET status = 'completed', updated_at = NOW()
  WHERE id = NEW.appointment_request_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update appointment status when completed
CREATE TRIGGER set_appointment_completed
AFTER INSERT ON completed_appointments
FOR EACH ROW
EXECUTE FUNCTION update_appointment_status_on_completion();
