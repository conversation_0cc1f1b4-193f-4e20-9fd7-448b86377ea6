-- Add explicit patient fields to appointment_requests table for better performance and convenience
-- These fields supplement the existing request_details JSONB field

ALTER TABLE appointment_requests 
ADD COLUMN IF NOT EXISTS patient_first_name TEXT,
ADD COLUMN IF NOT EXISTS patient_last_name TEXT,
ADD COLUMN IF NOT EXISTS patient_health_card TEXT,
ADD COLUMN IF NOT EXISTS patient_health_card_sequence TEXT,
ADD COLUMN IF NOT EXISTS patient_date_of_birth TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS patient_postal_code TEXT,
ADD COLUMN IF NOT EXISTS appointment_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS appointment_time_preference TEXT CHECK (appointment_time_preference IN ('asap', 'morning', 'afternoon', 'evening'));

-- Add constraints for postal code format (Canadian A1A 1A1)
ALTER TABLE appointment_requests 
ADD CONSTRAINT patient_postal_code_format 
CHECK (patient_postal_code IS NULL OR patient_postal_code ~ '^[A-Z][0-9][A-Z] [0-9][A-Z][0-9]$');

-- Add constraints for health card format (Quebec format: AAAA12341234 - 12 characters without dashes)
ALTER TABLE appointment_requests 
ADD CONSTRAINT patient_health_card_format 
CHECK (patient_health_card IS NULL OR patient_health_card ~ '^[A-Z]{4}[0-9]{8}$');

-- Add constraints for health card sequence (2 digits)
ALTER TABLE appointment_requests 
ADD CONSTRAINT patient_health_card_sequence_format 
CHECK (patient_health_card_sequence IS NULL OR patient_health_card_sequence ~ '^[0-9]{2}$');

-- Create indexes for performance on commonly queried fields
CREATE INDEX IF NOT EXISTS appointment_requests_patient_postal_code_idx ON appointment_requests(patient_postal_code);
CREATE INDEX IF NOT EXISTS appointment_requests_appointment_date_idx ON appointment_requests(appointment_date);
CREATE INDEX IF NOT EXISTS appointment_requests_appointment_time_preference_idx ON appointment_requests(appointment_time_preference);
CREATE INDEX IF NOT EXISTS appointment_requests_patient_first_name_idx ON appointment_requests(patient_first_name);
CREATE INDEX IF NOT EXISTS appointment_requests_patient_last_name_idx ON appointment_requests(patient_last_name);

-- Add a comment to explain the purpose of these fields
COMMENT ON COLUMN appointment_requests.patient_first_name IS 'Explicit patient first name field for performance and worker convenience';
COMMENT ON COLUMN appointment_requests.patient_last_name IS 'Explicit patient last name field for performance and worker convenience';
COMMENT ON COLUMN appointment_requests.patient_health_card IS 'Health card number in AAAA12341234 format (12 characters without dashes)';
COMMENT ON COLUMN appointment_requests.patient_health_card_sequence IS 'Health card sequence number (2 digits)';
COMMENT ON COLUMN appointment_requests.patient_date_of_birth IS 'Patient date of birth';
COMMENT ON COLUMN appointment_requests.patient_postal_code IS 'Patient postal code in A1A 1A1 format';
COMMENT ON COLUMN appointment_requests.appointment_date IS 'Preferred appointment date';
COMMENT ON COLUMN appointment_requests.appointment_time_preference IS 'Preferred appointment time (asap, morning, afternoon, evening)'; 