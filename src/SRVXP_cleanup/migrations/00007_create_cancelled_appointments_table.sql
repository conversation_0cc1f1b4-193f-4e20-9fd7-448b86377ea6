-- Create cancelled_appointments table for storing cancelled appointment information
CREATE TABLE IF NOT EXISTS cancelled_appointments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  appointment_request_id UUID NOT NULL REFERENCES appointment_requests(id),
  cancellation_reason TEXT,
  cancelled_by TEXT CHECK (cancelled_by IN ('user', 'system', 'admin')),
  cancelled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE cancelled_appointments ENABLE ROW LEVEL SECURITY;

-- Allow users to only view their own cancelled appointments (using parent relationship)
CREATE POLICY "Users can view their own cancelled appointments" 
  ON cancelled_appointments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM appointment_requests ar 
      WHERE ar.id = cancelled_appointments.appointment_request_id 
      AND ar.user_id = auth.uid()
    )
  );

-- Create indexes for performance
CREATE INDEX cancelled_appointments_request_id_idx ON cancelled_appointments(appointment_request_id);
CREATE INDEX cancelled_appointments_cancelled_at_idx ON cancelled_appointments(cancelled_at);

-- <PERSON>reate trigger function to update the parent appointment request status
CREATE OR REPLACE FUNCTION update_appointment_status_on_cancellation()
RETURNS TRIGGER AS $$
BEGIN
  -- Update the parent appointment request status to cancelled
  UPDATE appointment_requests
  SET status = 'cancelled', updated_at = NOW()
  WHERE id = NEW.appointment_request_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update appointment status when cancelled
CREATE TRIGGER set_appointment_cancelled
AFTER INSERT ON cancelled_appointments
FOR EACH ROW
EXECUTE FUNCTION update_appointment_status_on_cancellation();
