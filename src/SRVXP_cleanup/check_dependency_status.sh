#!/bin/bash

# SRVXP Dependency Status Checker
# This script checks the current status of dependency separation

echo "🔍 SRVXP Dependency Status Check"
echo "================================"

# Define paths
PROJECT_DIR="/Users/<USER>/src/SRVXP"
DEPS_DIR="/Users/<USER>/src/SRVXP_dependencies"

cd "$PROJECT_DIR"

echo "📁 Project directory: $PROJECT_DIR"
echo "📁 Dependencies directory: $DEPS_DIR"
echo ""

# Check if dependencies folder exists
if [ -d "$DEPS_DIR" ]; then
    echo "✅ SRVXP_dependencies folder exists"
else
    echo "❌ SRVXP_dependencies folder not found"
    exit 1
fi

echo ""
echo "🔗 Checking symlinks in project folder:"

# Function to check symlink status
check_item() {
    local item=$1
    
    if [ -L "$item" ]; then
        local target=$(readlink "$item")
        echo "  🔗 $item -> $target"
        if [ -e "$item" ]; then
            echo "    ✅ Target exists"
        else
            echo "    ❌ Target missing!"
        fi
    elif [ -d "$item" ] || [ -f "$item" ]; then
        echo "  📁 $item (regular file/directory - not symlinked)"
    else
        echo "  ❌ $item not found"
    fi
}

check_item "node_modules"
check_item ".next"
check_item "build"
check_item "bun.lock"
check_item "package-lock.json"
check_item "tsconfig.tsbuildinfo"

echo ""
echo "📊 Folder sizes:"

echo "SRVXP project (excluding symlinked dependencies):"
# Calculate size excluding symlinks
actual_size=$(du -sh --exclude="node_modules" --exclude=".next" --exclude="build" "$PROJECT_DIR" 2>/dev/null | cut -f1 || echo "Calculating...")
echo "  📏 $actual_size"

echo ""
echo "SRVXP_dependencies folder:"
if [ -d "$DEPS_DIR" ]; then
    deps_size=$(du -sh "$DEPS_DIR" 2>/dev/null | cut -f1 || echo "Calculating...")
    echo "  📏 $deps_size"
else
    echo "  ❌ Folder not found"
fi

echo ""
echo "🧩 Dependencies in SRVXP_dependencies:"
if [ -d "$DEPS_DIR" ]; then
    ls -la "$DEPS_DIR" | grep -v "^total" | grep -v "^\.$" | grep -v "^\.\.$"
fi

echo ""
echo "🎯 Current Setup Status:"

# Determine setup status
symlinks_exist=false
if [ -L "node_modules" ] || [ -L ".next" ] || [ -L "build" ]; then
    symlinks_exist=true
fi

if $symlinks_exist; then
    echo "  ✅ Dependency separation is ACTIVE"
    echo "  📝 Dependencies are stored in SRVXP_dependencies with symlinks"
    echo "  🎯 Project folder is clean for sharing with Claude"
    echo "  💻 VSCode/Cursor should work normally"
else
    echo "  📁 Standard setup - dependencies are in project folder"
    echo "  💡 Run './setup_dependency_separation.sh' to separate dependencies"
fi

echo ""
echo "🚀 Available commands:"
echo "  ./setup_dependency_separation.sh   - Separate dependencies"
echo "  ./restore_dependencies.sh          - Restore original structure"
echo "  ./check_dependency_status.sh       - Check current status (this script)" 