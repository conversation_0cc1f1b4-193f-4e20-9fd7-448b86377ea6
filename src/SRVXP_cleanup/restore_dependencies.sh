#!/bin/bash

# SRVXP Dependency Restoration Script
# This script restores dependencies back to the main project folder

set -e  # Exit on any error

echo "🔄 SRVXP Dependency Restoration"
echo "==============================="

# Define paths
PROJECT_DIR="/Users/<USER>/src/SRVXP"
DEPS_DIR="/Users/<USER>/src/SRVXP_dependencies"

# Ensure we're in the right directory
cd "$PROJECT_DIR"

echo "📁 Current project directory: $(pwd)"
echo "📁 Dependencies directory: $DEPS_DIR"

# Function to restore symlinked items
restore_item() {
    local item=$1
    local source_subdir=${2:-""}
    
    if [ -L "$item" ]; then
        echo "  🔗 Restoring $item..."
        
        # Remove the symlink
        rm "$item"
        
        # Move the actual item back
        if [ -n "$source_subdir" ]; then
            mv "$DEPS_DIR/$source_subdir/$item" "$item"
        else
            mv "$DEPS_DIR/$item" "$item"
        fi
        
        echo "  ✅ Restored $item"
    elif [ -d "$item" ] || [ -f "$item" ]; then
        echo "  ⏭️  $item already exists in project (not a symlink), skipping..."
    else
        echo "  ❌ $item not found as symlink, checking dependencies folder..."
        
        # Try to restore from dependencies folder anyway
        if [ -n "$source_subdir" ] && [ -e "$DEPS_DIR/$source_subdir/$item" ]; then
            mv "$DEPS_DIR/$source_subdir/$item" "$item"
            echo "  ✅ Restored $item from $source_subdir"
        elif [ -e "$DEPS_DIR/$item" ]; then
            mv "$DEPS_DIR/$item" "$item"
            echo "  ✅ Restored $item"
        else
            echo "  ⚠️  $item not found in dependencies folder"
        fi
    fi
}

echo ""
echo "🔄 Restoring dependencies to project folder..."

# Restore main dependency directories
restore_item "node_modules"
restore_item ".next" "build_artifacts"
restore_item "build" "build_artifacts"

# Restore cache and lock files
restore_item "bun.lock" "cache"
restore_item "package-lock.json" "cache"
restore_item "tsconfig.tsbuildinfo" "cache"

# Restore any other build artifacts
echo ""
echo "🧹 Checking for additional build artifacts..."
restore_item ".eslintcache" "cache"
restore_item ".turbo" "cache"
restore_item "dist" "build_artifacts"

echo ""
echo "📊 Checking folder sizes after restoration..."
echo "SRVXP project folder:"
du -sh "$PROJECT_DIR" 2>/dev/null

echo ""
echo "SRVXP_dependencies folder (should be mostly empty now):"
du -sh "$DEPS_DIR" 2>/dev/null || echo "Dependencies folder: empty or minimal"

echo ""
echo "🧹 Cleaning up empty directories in dependencies folder..."
find "$DEPS_DIR" -type d -empty -delete 2>/dev/null || echo "No empty directories to clean"

echo ""
echo "✅ Dependency restoration complete!"
echo ""
echo "🎯 What's been accomplished:"
echo "  • All dependencies restored to SRVXP project folder"
echo "  • Symlinks removed"
echo "  • Project is back to original structure"
echo "  • Dependencies folder cleaned up"
echo ""
echo "⚠️  Note: The project is now in its original state with all dependencies included" 