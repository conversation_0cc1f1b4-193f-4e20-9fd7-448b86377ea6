import { NextRequest, NextResponse } from 'next/server'
import { updateSession } from '@/lib/supabase/middleware'

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}

export async function middleware(request: NextRequest) {
  // Authentication checks for protected routes
  const authRoutes = [
    '/dashboard',
    '/mes-rendez-vous',
    '/compte',
    '/trouver-rendez-vous',
    '/aide'
  ]

  // Get authentication state and update the session
  const { response, session } = await updateSession(request)

  // Check if the current path is a protected route
  if (authRoutes.some(route => request.nextUrl.pathname.startsWith(route))) {
    // If no session or user, redirect to sign-in
    if (!session || !session.user) {
      // Create the redirect URL with the original location as a parameter
      const redirectUrl = new URL('/auth/sign-in', request.url)
      redirectUrl.searchParams.set('redirectedFrom', request.nextUrl.pathname)

      // Apply cache control headers to prevent caching of protected pages
      const redirectResponse = NextResponse.redirect(redirectUrl)
      redirectResponse.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
      redirectResponse.headers.set('Pragma', 'no-cache')
      redirectResponse.headers.set('Expires', '0')

      return redirectResponse
    }
  }

  // Apply cache control headers to all responses for protected routes
  if (authRoutes.some(route => request.nextUrl.pathname.startsWith(route))) {
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
  }

  return response
}
