# SRVXP Puppeteer Worker

A scalable Docker-based worker service that processes appointment booking jobs using Puppeteer automation.

## Overview

This worker service:
- Consumes jobs from Redis queues published by the Worker Manager
- Executes the Quebec appointment scraper with job-specific configuration
- Updates appointment status in Supabase database
- Provides health monitoring and metrics endpoints
- Supports horizontal scaling and graceful shutdown

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Worker        │    │   Redis         │    │   Supabase      │
│   Manager       │───▶│   Queue         │───▶│   Database      │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Puppeteer     │
                       │   Worker        │
                       │                 │
                       └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Scraper       │
                       │   (Swappable)   │
                       │                 │
                       └─────────────────┘
```

## Features

### ✅ Implemented
- **Job Processing**: Consumes jobs from Redis with retry queue priority
- **Scraper Integration**: Dynamically configures and executes the Quebec scraper
- **Database Updates**: Updates appointment status throughout the process
- **Health Monitoring**: HTTP endpoints for health checks and metrics
- **Graceful Shutdown**: Waits for jobs to complete before stopping
- **Error Handling**: Comprehensive retry logic and dead letter queue
- **Logging**: Structured JSON logging with correlation IDs
- **Docker Support**: Full containerization with Puppeteer dependencies

### 🔄 Flexible Scraper Integration
- **Swappable Scraper Directory**: Mount different scraper versions via Docker volumes
- **Dynamic Configuration**: Maps job data to scraper variables automatically
- **Temporary Config Files**: Creates isolated config files per job
- **Output Parsing**: Detects success messages and extracts appointment details

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for development)
- Access to Redis and Supabase

### Environment Setup

The worker automatically links to the main backend .env file for consistent configuration.

1. **Automatic environment linking:**
```bash
npm install  # Automatically links to main backend .env and sets up scraper
```

2. **Manual environment linking (if needed):**
```bash
npm run link-env  # Links worker .env to main backend .env
```

3. **Environment variables are automatically synced from:**
```bash
# From backend/.env:
SUPABASE_URL=https://tfvswgreslsbctjrvdbd.supabase.co
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
REDIS_PASSWORD=
LOG_LEVEL=info
PUPPETEER_HEADLESS=true

# Worker-specific (automatically set):
WORKER_ID=puppeteer-worker-1
WORKER_CONCURRENCY=1
HEALTH_CHECK_PORT=3001
```

### Development

1. **Setup worker (automatic environment linking):**
```bash
npm install  # Automatically links to main backend .env and sets up scraper
```

2. **Start development environment:**
```bash
docker-compose up -d redis redis-commander
npm run dev
```

3. **Access services:**
- Worker Health: http://localhost:3001/health
- Worker Metrics: http://localhost:3001/metrics
- Redis UI: http://localhost:8081

### Production Deployment

1. **Build and start with Docker Compose:**
```bash
docker-compose up -d
```

2. **Or build Docker image:**
```bash
docker build -t srvxp-puppeteer-worker .
docker run -d \
  --name puppeteer-worker \
  -p 3001:3001 \
  -e REDIS_HOST=your-redis-host \
  -e SUPABASE_URL=your-supabase-url \
  -e SUPABASE_SERVICE_KEY=your-service-key \
  srvxp-puppeteer-worker
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `REDIS_HOST` | localhost | Redis server hostname |
| `REDIS_PORT` | 6379 | Redis server port |
| `REDIS_PASSWORD` | "" | Redis password (if required) |
| `WORKER_ID` | puppeteer-worker-1 | Unique worker identifier |
| `WORKER_CONCURRENCY` | 1 | Number of concurrent jobs |
| `WORKER_TIMEOUT` | 300000 | Job timeout in milliseconds |
| `PUPPETEER_HEADLESS` | true | Run browser in headless mode |
| `SCRAPER_PATH` | ./src/scrapers/Appt_Scraper | Path to scraper directory |
| `SCRAPER_SCRIPT` | SRVXP_Bot.js | Scraper script filename |
| `LOG_LEVEL` | info | Logging level (debug, info, warn, error) |

### Job Data Mapping

The worker automatically maps job data to scraper configuration:

```typescript
// Job Data (from Redis)
{
  patientInfo: {
    firstName: "John",
    lastName: "Doe",
    healthCard: "DOEJ12345678",
    // ...
  },
  searchCriteria: {
    appointmentDate: "2025-06-09",
    timePreference: "morning",
    searchRadius: 20
  }
}

// Mapped to Scraper Config
{
  firstName: "John",
  lastName: "Doe",
  healthCardNumber: "DOEJ12345678",
  appointmentDate: "09-06-2025",
  momentOfDay: "morning",
  searchPerimeter: "2", // 20km
  // ...
}
```

## Swapping Scraper Versions

The worker supports easy scraper updates without rebuilding:

### Method 1: Docker Volume Mount
```bash
# Update your local scraper
cp -r /path/to/new/scraper ./src/scrapers/Appt_Scraper/

# Restart worker (picks up changes automatically)
docker-compose restart puppeteer-worker
```

### Method 2: Environment Variable
```bash
# Point to different scraper location
export SCRAPER_PATH=/path/to/different/scraper
npm run dev
```

### Method 3: Runtime Update
```bash
# Copy new scraper to running container
docker cp /path/to/new/scraper srvxp-puppeteer-worker:/app/src/scrapers/Appt_Scraper/

# Worker will use new scraper for next job
```

## Monitoring

### Health Check Endpoint
```bash
curl http://localhost:3001/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-09T10:30:00.000Z",
  "workerId": "puppeteer-worker-1",
  "healthy": true,
  "services": {
    "redis": true,
    "database": true
  },
  "metrics": {
    "jobsProcessed": 42,
    "jobsSucceeded": 38,
    "jobsFailed": 4,
    "averageProcessingTime": 45000,
    "workerStartedAt": "2025-01-09T09:00:00.000Z"
  },
  "processingJobs": 1
}
```

### Metrics Endpoint
```bash
curl http://localhost:3001/metrics
```

### Logs
```bash
# View worker logs
docker-compose logs -f puppeteer-worker

# Filter by correlation ID
docker-compose logs puppeteer-worker | grep "correlationId.*abc-123"
```

## Job Processing Flow

1. **Job Consumption**: Worker polls Redis queues (retry queue has priority)
2. **Status Update**: Marks appointment as "in_progress" in database
3. **Config Generation**: Creates temporary config file with job data
4. **Scraper Execution**: Runs Puppeteer script with generated config
5. **Result Processing**: 
   - Success + Appointment Booked → Mark as "completed"
   - Success + No Appointment → Retry or fail after max attempts
   - Failure → Retry or move to dead letter queue
6. **Cleanup**: Removes temporary config file

## Error Handling

### Retry Logic
- Jobs are retried up to `WORKER_RETRY_ATTEMPTS` times
- Retry jobs have priority over new jobs
- Exponential backoff between retries

### Dead Letter Queue
- Jobs that exceed max retry attempts
- Jobs with permanent failures
- Accessible via Redis for manual inspection

### Graceful Shutdown
- Stops accepting new jobs
- Waits for current jobs to complete (30s timeout)
- Cleanly disconnects from services

## Development

### Project Structure
```
src/
├── index.ts              # Main entry point
├── worker.ts             # Core worker logic
├── types/                # TypeScript interfaces
├── utils/                # Configuration and logging
├── services/             # Redis, database, scraper services
└── scrapers/             # Scraper directory (swappable)
    └── Appt_Scraper/     # Quebec scraper
```

### Building
```bash
npm run build    # Compile TypeScript
npm run start    # Run compiled version
npm run dev      # Run with ts-node
npm run lint     # Check code style
```

### Testing
```bash
# Test with sample job
npm run test

# Manual job injection (requires Redis)
redis-cli LPUSH appointment-requests '{"id":"test-123","type":"appointment-request","data":{...}}'
```

## Scaling

### Horizontal Scaling
```bash
# Scale to 3 workers
docker-compose up -d --scale puppeteer-worker=3

# Each worker gets unique ID automatically
WORKER_ID=puppeteer-worker-1
WORKER_ID=puppeteer-worker-2
WORKER_ID=puppeteer-worker-3
```

### Resource Requirements
- **Memory**: 512MB - 1GB per worker
- **CPU**: 0.5 - 1 core per worker
- **Storage**: 100MB + temp files

## Troubleshooting

### Common Issues

1. **Scraper Not Found**
   ```
   Error: Scraper script not found at: ./src/scrapers/Appt_Scraper/SRVXP_Bot.js
   ```
   - Ensure scraper directory is mounted correctly
   - Check `SCRAPER_PATH` and `SCRAPER_SCRIPT` environment variables

2. **Puppeteer Launch Failed**
   ```
   Error: Failed to launch the browser process
   ```
   - Ensure Docker container has necessary dependencies
   - Check if running in headless mode: `PUPPETEER_HEADLESS=true`

3. **Redis Connection Failed**
   ```
   Error: Redis connection failed after 10 retries
   ```
   - Verify Redis host and port configuration
   - Check network connectivity between services

4. **Database Connection Failed**
   ```
   Error: Failed to connect to database
   ```
   - Verify Supabase URL and service key
   - Check database permissions

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=debug
npm run dev

# Or with Docker
docker-compose up -d -e LOG_LEVEL=debug
```

## Integration with Worker Manager

The worker is designed to work with the existing Worker Manager service:

1. **Worker Manager** publishes jobs to Redis
2. **Puppeteer Worker** consumes and processes jobs
3. **Database** tracks appointment status
4. **Notification Service** (Phase 3) sends results to users

## Next Steps

This completes **Phase 2: Worker Implementation**. Next phases:

- **Phase 3**: Email notification system
- **Phase 4**: AWS multi-node deployment
- **Phase 5**: Monitoring and operations
- **Phase 6**: Security and compliance

## Support

For issues or questions:
1. Check logs for error details
2. Verify configuration and connectivity
3. Test with sample jobs
4. Review health check endpoints 