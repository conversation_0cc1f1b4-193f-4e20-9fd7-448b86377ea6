{"name": "srvxp-puppeteer-worker", "version": "1.0.0", "description": "Puppeteer worker for processing appointment booking jobs", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "setup-scraper": "node scripts/setup-scraper-integration.js", "link-env": "node scripts/link-env.js", "postinstall": "npm run link-env && npm run setup-scraper"}, "keywords": ["puppeteer", "worker", "appointment", "booking", "redis", "queue"], "author": "SRVXP Team", "license": "MIT", "dependencies": {"redis": "^4.6.0", "puppeteer": "^24.10.0", "uuid": "^9.0.0", "@types/uuid": "^9.0.0", "winston": "^3.11.0", "dotenv": "^16.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.0.0", "ts-node": "^10.9.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "@types/jest": "^29.0.0"}, "engines": {"node": ">=18.0.0"}}