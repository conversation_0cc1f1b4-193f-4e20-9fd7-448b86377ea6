version: '3.8'

services:
  puppeteer-worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: srvxp-puppeteer-worker
    environment:
      # Redis Configuration (matches main backend .env)
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ""
      REDIS_DB: 0
      
      # Worker Configuration
      WORKER_ID: puppeteer-worker-dev
      WORKER_CONCURRENCY: 1
      WORKER_TIMEOUT: 300000
      WORKER_RETRY_ATTEMPTS: 3
      
      # Puppeteer Configuration (matches main backend .env)
      PUPPETEER_HEADLESS: "true"
      PUPPETEER_TIMEOUT: 30000
      PUPPETEER_VIEWPORT_WIDTH: 1920
      PUPPETEER_VIEWPORT_HEIGHT: 1080
      
      # Scraper Configuration
      SCRAPER_PATH: ./src/scrapers/Appt_Scraper
      SCRAPER_SCRIPT: SRVXP_Bot.js
      
      # Logging Configuration (matches main backend .env)
      LOG_LEVEL: info
      LOG_FORMAT: json
      
      # Health Check Configuration
      HEALTH_CHECK_PORT: 3001
      HEALTH_CHECK_INTERVAL: 30000
      
      # Database Configuration (from main backend .env)
      SUPABASE_URL: https://tfvswgreslsbctjrvdbd.supabase.co
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.VzXYRhD2WwOUpaRavQFj-1Bgh8VN-7OLlP-fTCP3s7M
    ports:
      - "3001:3001"
    volumes:
      # Mount the scraper directory to allow easy swapping
      - ./src/scrapers/Appt_Scraper:/app/src/scrapers/Appt_Scraper
      # Mount temp directory for config files
      - ./temp:/app/src/scrapers/Appt_Scraper/temp
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - srvxp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    container_name: srvxp-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - srvxp-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: srvxp-redis-ui
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - srvxp-network

volumes:
  redis_data:
    driver: local

networks:
  srvxp-network:
    driver: bridge 