# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Worker Configuration
WORKER_ID=puppeteer-worker-1
WORKER_CONCURRENCY=1
WORKER_TIMEOUT=300000
WORKER_RETRY_ATTEMPTS=3

# Puppeteer Configuration
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=30000
PUPPETEER_VIEWPORT_WIDTH=1920
PUPPETEER_VIEWPORT_HEIGHT=1080

# Scraper Configuration
SCRAPER_PATH=./src/scrapers/Appt_Scraper
SCRAPER_SCRIPT=SRVXP_Bot.js

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Health Check Configuration
HEALTH_CHECK_PORT=3001
HEALTH_CHECK_INTERVAL=30000

# Database Configuration (for status updates)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key 