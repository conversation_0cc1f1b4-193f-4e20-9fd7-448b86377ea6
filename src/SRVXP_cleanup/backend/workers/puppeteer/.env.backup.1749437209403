# Puppeteer Worker Environment Configuration
# This file links to the main backend .env file for shared configuration

# Redis Configuration (from main backend .env)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Worker Configuration
WORKER_ID=puppeteer-worker-1
WORKER_CONCURRENCY=1
WORKER_TIMEOUT=300000
WORKER_RETRY_ATTEMPTS=3

# Puppeteer Configuration (from main backend .env)
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=30000
PUPPETEER_VIEWPORT_WIDTH=1920
PUPPETEER_VIEWPORT_HEIGHT=1080

# Scraper Configuration
SCRAPER_PATH=./src/scrapers/Appt_Scraper
SCRAPER_SCRIPT=SRVXP_Bot.js

# Logging Configuration (from main backend .env)
LOG_LEVEL=info
LOG_FORMAT=json

# Health Check Configuration
HEALTH_CHECK_PORT=3001
HEALTH_CHECK_INTERVAL=30000

# Database Configuration (from main backend .env)
SUPABASE_URL=https://tfvswgreslsbctjrvdbd.supabase.co
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.VzXYRhD2WwOUpaRavQFj-1Bgh8VN-7OLlP-fTCP3s7M 