#!/usr/bin/env node

/**
 * Script to link the puppeteer worker environment to the main backend .env file
 * This ensures consistent configuration across all services
 */

const fs = require('fs');
const path = require('path');

const MAIN_ENV_PATH = path.join(__dirname, '..', '..', '..', '.env');
const WORKER_ENV_PATH = path.join(__dirname, '..', '.env');

function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return {};
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const env = {};
  
  content.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        env[key.trim()] = valueParts.join('=').trim();
      }
    }
  });
  
  return env;
}

function createWorkerEnv(mainEnv) {
  const workerEnvContent = `# Puppeteer Worker Environment Configuration
# This file is automatically generated from the main backend .env file
# Last updated: ${new Date().toISOString()}

# Redis Configuration (from main backend .env)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=${mainEnv.REDIS_PASSWORD || ''}
REDIS_DB=${mainEnv.REDIS_DB || '0'}

# Worker Configuration
WORKER_ID=puppeteer-worker-1
WORKER_CONCURRENCY=1
WORKER_TIMEOUT=300000
WORKER_RETRY_ATTEMPTS=3

# Puppeteer Configuration (from main backend .env)
PUPPETEER_HEADLESS=${mainEnv.PUPPETEER_HEADLESS || 'true'}
PUPPETEER_TIMEOUT=${mainEnv.PUPPETEER_TIMEOUT || '30000'}
PUPPETEER_VIEWPORT_WIDTH=1920
PUPPETEER_VIEWPORT_HEIGHT=1080

# Scraper Configuration
SCRAPER_PATH=./src/scrapers/Appt_Scraper
SCRAPER_SCRIPT=SRVXP_Bot.js

# Logging Configuration (from main backend .env)
LOG_LEVEL=${mainEnv.LOG_LEVEL || 'info'}
LOG_FORMAT=${mainEnv.LOG_FORMAT || 'json'}

# Health Check Configuration
HEALTH_CHECK_PORT=3001
HEALTH_CHECK_INTERVAL=30000

# Database Configuration (from main backend .env)
SUPABASE_URL=${mainEnv.SUPABASE_URL || ''}
SUPABASE_SERVICE_KEY=${mainEnv.SUPABASE_SERVICE_ROLE_KEY || ''}
`;

  return workerEnvContent;
}

function main() {
  console.log('🔗 Linking worker environment to main backend .env file...\n');
  
  try {
    // Check if main .env exists
    if (!fs.existsSync(MAIN_ENV_PATH)) {
      throw new Error(`Main .env file not found at: ${MAIN_ENV_PATH}`);
    }
    
    console.log('✅ Found main .env file at:', MAIN_ENV_PATH);
    
    // Parse main .env file
    const mainEnv = parseEnvFile(MAIN_ENV_PATH);
    console.log('✅ Parsed main environment variables');
    
    // Validate required variables
    const requiredVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];
    const missingVars = requiredVars.filter(varName => !mainEnv[varName]);
    
    if (missingVars.length > 0) {
      console.warn('⚠️  Missing required variables in main .env:', missingVars.join(', '));
    }
    
    // Create worker .env content
    const workerEnvContent = createWorkerEnv(mainEnv);
    
    // Backup existing worker .env if it exists
    if (fs.existsSync(WORKER_ENV_PATH)) {
      const backupPath = WORKER_ENV_PATH + '.backup.' + Date.now();
      fs.copyFileSync(WORKER_ENV_PATH, backupPath);
      console.log('✅ Backed up existing worker .env to:', backupPath);
    }
    
    // Write new worker .env
    fs.writeFileSync(WORKER_ENV_PATH, workerEnvContent, 'utf8');
    console.log('✅ Created worker .env file at:', WORKER_ENV_PATH);
    
    // Verify the created file
    const workerEnv = parseEnvFile(WORKER_ENV_PATH);
    console.log('\n📋 Worker environment summary:');
    console.log('   SUPABASE_URL:', workerEnv.SUPABASE_URL ? '✅ Set' : '❌ Missing');
    console.log('   SUPABASE_SERVICE_KEY:', workerEnv.SUPABASE_SERVICE_KEY ? '✅ Set' : '❌ Missing');
    console.log('   REDIS_HOST:', workerEnv.REDIS_HOST);
    console.log('   LOG_LEVEL:', workerEnv.LOG_LEVEL);
    console.log('   PUPPETEER_HEADLESS:', workerEnv.PUPPETEER_HEADLESS);
    
    console.log('\n✅ Environment linking completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Review the generated .env file if needed');
    console.log('2. Start the worker: npm run dev or docker-compose up');
    console.log('3. The worker will use the same database and Redis as the main backend');
    
  } catch (error) {
    console.error('\n❌ Environment linking failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  parseEnvFile,
  createWorkerEnv
}; 