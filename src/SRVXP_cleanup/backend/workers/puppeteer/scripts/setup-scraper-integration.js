#!/usr/bin/env node

/**
 * Setup script to integrate the existing SRVXP_Bot.js with the worker system
 * This script modifies the scraper to accept configuration from environment variables
 * or external config files instead of hardcoded USER_CONFIG
 */

const fs = require('fs');
const path = require('path');

const SCRAPER_PATH = path.join(__dirname, '..', 'src', 'scrapers', 'Appt_Scraper', 'SRVXP_Bot.js');
const BACKUP_PATH = SCRAPER_PATH + '.original';

function backupOriginalScript() {
  if (!fs.existsSync(BACKUP_PATH)) {
    console.log('Creating backup of original scraper...');
    fs.copyFileSync(SCRAPER_PATH, BACKUP_PATH);
    console.log('✅ Backup created at:', BACKUP_PATH);
  } else {
    console.log('ℹ️  Backup already exists at:', BACKUP_PATH);
  }
}

function modifyScraperForWorkerIntegration() {
  console.log('Reading original scraper...');
  let content = fs.readFileSync(SCRAPER_PATH, 'utf8');

  // Check if already modified
  if (content.includes('// WORKER INTEGRATION MODIFICATION')) {
    console.log('ℹ️  Scraper already modified for worker integration');
    return;
  }

  console.log('Modifying scraper for worker integration...');

  // Find the USER_CONFIG section
  const configStartRegex = /const USER_CONFIG = \{/;
  const configMatch = content.match(configStartRegex);
  
  if (!configMatch) {
    throw new Error('Could not find USER_CONFIG in scraper file');
  }

  // Find the end of the USER_CONFIG object
  let braceCount = 0;
  let configStart = configMatch.index;
  let configEnd = -1;
  let inString = false;
  let stringChar = '';
  
  for (let i = configStart; i < content.length; i++) {
    const char = content[i];
    
    if (!inString) {
      if (char === '"' || char === "'") {
        inString = true;
        stringChar = char;
      } else if (char === '{') {
        braceCount++;
      } else if (char === '}') {
        braceCount--;
        if (braceCount === 0) {
          configEnd = i + 1;
          break;
        }
      }
    } else {
      if (char === stringChar && content[i-1] !== '\\') {
        inString = false;
      }
    }
  }

  if (configEnd === -1) {
    throw new Error('Could not find end of USER_CONFIG object');
  }

  // Create the new configuration loading code
  const newConfigCode = `// WORKER INTEGRATION MODIFICATION
// This section has been modified to support dynamic configuration from the worker system

function loadUserConfig() {
  // Check if running in worker mode (config file provided)
  const configPath = process.env.SCRAPER_CONFIG_PATH;
  
  if (configPath && require('fs').existsSync(configPath)) {
    console.log('Loading configuration from worker:', configPath);
    try {
      const workerConfig = require(configPath);
      console.log('Worker configuration loaded successfully');
      return workerConfig;
    } catch (error) {
      console.error('Failed to load worker configuration:', error.message);
      console.log('Falling back to default configuration');
    }
  }
  
  // Fallback to original hardcoded configuration
  console.log('Using default hardcoded configuration');
  return ${content.substring(configStart + 'const USER_CONFIG = '.length, configEnd)};
}

const USER_CONFIG = loadUserConfig();`;

  // Replace the original USER_CONFIG with the new dynamic loading code
  const newContent = content.substring(0, configStart) + newConfigCode + content.substring(configEnd);

  // Write the modified content
  fs.writeFileSync(SCRAPER_PATH, newContent, 'utf8');
  console.log('✅ Scraper modified successfully');
}

function createTestConfig() {
  const testConfigPath = path.join(__dirname, '..', 'src', 'scrapers', 'Appt_Scraper', 'test-config.js');
  
  const testConfig = `// Test configuration for worker integration
const USER_CONFIG = {
  // Personal Information (for identification form)
  firstName: 'Test',
  lastName: 'User',
  healthCardNumber: 'TEST12345678',
  cardSequenceNumber: '01',
  birthDay: '01',
  birthMonth: '01',
  birthYear: '1990',
  email: '<EMAIL>',
  cellphone: '************',
  language: 'english',
  communicationMethod: 'email',
  
  // Location and Search Preferences
  postalCode: 'H1A1A1',
  searchPerimeter: '1',
  
  // Appointment Preferences
  appointmentDate: '01-12-2025',
  
  // Consultation Type
  consultationReason: 'ac2a5fa4-8514-11ef-a759-005056b11d6c',
  
  // Additional Configuration
  momentOfDay: 'asap',
  transportationDistance: '10',
  afterThisTime: '',
  beforeThisTime: '',
  notBeforeThisDelay: '2'
};

module.exports = USER_CONFIG;
`;

  fs.writeFileSync(testConfigPath, testConfig, 'utf8');
  console.log('✅ Test configuration created at:', testConfigPath);
  
  return testConfigPath;
}

function testIntegration() {
  console.log('\n🧪 Testing integration...');
  
  const testConfigPath = createTestConfig();
  
  // Set environment variable for test
  process.env.SCRAPER_CONFIG_PATH = testConfigPath;
  
  try {
    // Try to require the modified scraper (this will execute the config loading)
    delete require.cache[require.resolve(SCRAPER_PATH)];
    
    console.log('✅ Integration test passed - scraper can load external configuration');
    
    // Clean up test config
    fs.unlinkSync(testConfigPath);
    console.log('✅ Test configuration cleaned up');
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    throw error;
  }
}

function main() {
  console.log('🚀 Setting up scraper integration for worker system...\n');
  
  try {
    // Check if scraper exists
    if (!fs.existsSync(SCRAPER_PATH)) {
      throw new Error(`Scraper not found at: ${SCRAPER_PATH}`);
    }
    
    // Backup original
    backupOriginalScript();
    
    // Modify for worker integration
    modifyScraperForWorkerIntegration();
    
    // Test the integration
    testIntegration();
    
    console.log('\n✅ Scraper integration setup completed successfully!');
    console.log('\nNext steps:');
    console.log('1. The scraper now supports dynamic configuration from the worker');
    console.log('2. Original scraper backed up to:', BACKUP_PATH);
    console.log('3. You can now start the worker and it will use this scraper');
    console.log('4. To restore original: cp', BACKUP_PATH, SCRAPER_PATH);
    
  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  backupOriginalScript,
  modifyScraperForWorkerIntegration,
  testIntegration
}; 