import winston from 'winston';

export interface LogContext {
  component?: string;
  correlationId?: string;
  jobId?: string;
  appointmentId?: string;
  workerId?: string;
  [key: string]: any;
}

export class StructuredLogger {
  private logger: winston.Logger;
  private defaultContext: LogContext;

  constructor(defaultContext: LogContext = {}) {
    this.defaultContext = defaultContext;
    
    const logLevel = process.env.LOG_LEVEL || 'info';
    const logFormat = process.env.LOG_FORMAT || 'json';

    const formats = [
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
    ];

    if (logFormat === 'json') {
      formats.push(winston.format.json());
    } else {
      formats.push(
        winston.format.colorize(),
        winston.format.simple()
      );
    }

    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(...formats),
      defaultMeta: this.defaultContext,
      transports: [
        new winston.transports.Console({
          handleExceptions: true,
          handleRejections: true,
        }),
      ],
    });
  }

  withCorrelationId(correlationId: string): StructuredLogger {
    return new StructuredLogger({
      ...this.defaultContext,
      correlationId,
    });
  }

  withJobId(jobId: string): StructuredLogger {
    return new StructuredLogger({
      ...this.defaultContext,
      jobId,
    });
  }

  withContext(context: LogContext): StructuredLogger {
    return new StructuredLogger({
      ...this.defaultContext,
      ...context,
    });
  }

  debug(message: string, context?: LogContext): void {
    this.logger.debug(message, context);
  }

  info(message: string, context?: LogContext): void {
    this.logger.info(message, context);
  }

  warn(message: string, context?: LogContext): void {
    this.logger.warn(message, context);
  }

  error(message: string, error?: Error, context?: LogContext): void {
    const errorContext = {
      ...context,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name,
      } : undefined,
    };
    this.logger.error(message, errorContext);
  }

  fatal(message: string, error?: Error, context?: LogContext): void {
    const errorContext = {
      ...context,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name,
      } : undefined,
    };
    this.logger.error(`FATAL: ${message}`, errorContext);
  }
} 