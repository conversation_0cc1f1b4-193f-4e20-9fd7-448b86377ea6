import { config } from 'dotenv';
import { WorkerConfig } from '../types';

// Load environment variables
config();

function getEnvVar(name: string, defaultValue?: string): string {
  const value = process.env[name];
  if (!value && defaultValue === undefined) {
    throw new Error(`Environment variable ${name} is required but not set`);
  }
  return value || defaultValue || '';
}

function getEnvNumber(name: string, defaultValue: number): number {
  const value = process.env[name];
  if (!value) return defaultValue;
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${name} must be a valid number`);
  }
  return parsed;
}

function getEnvBoolean(name: string, defaultValue: boolean): boolean {
  const value = process.env[name];
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

const workerConfig: WorkerConfig = {
  workerId: getEnvVar('WORKER_ID', 'puppeteer-worker-1'),
  concurrency: getEnvNumber('WORKER_CONCURRENCY', 1),
  timeout: getEnvNumber('WORKER_TIMEOUT', 300000), // 5 minutes
  retryAttempts: getEnvNumber('WORKER_RETRY_ATTEMPTS', 3),
  
  redis: {
    host: getEnvVar('REDIS_HOST', 'localhost'),
    port: getEnvNumber('REDIS_PORT', 6379),
    password: getEnvVar('REDIS_PASSWORD', ''),
    db: getEnvNumber('REDIS_DB', 0),
  },
  
  puppeteer: {
    headless: getEnvBoolean('PUPPETEER_HEADLESS', true),
    timeout: getEnvNumber('PUPPETEER_TIMEOUT', 30000),
    viewport: {
      width: getEnvNumber('PUPPETEER_VIEWPORT_WIDTH', 1920),
      height: getEnvNumber('PUPPETEER_VIEWPORT_HEIGHT', 1080),
    },
  },
  
  scraper: {
    path: getEnvVar('SCRAPER_PATH', './src/scrapers/Appt_Scraper'),
    script: getEnvVar('SCRAPER_SCRIPT', 'SRVXP_Bot.js'),
  },
  
  supabase: {
    url: getEnvVar('SUPABASE_URL'),
    serviceKey: getEnvVar('SUPABASE_SERVICE_KEY'),
  },
};

export default workerConfig; 