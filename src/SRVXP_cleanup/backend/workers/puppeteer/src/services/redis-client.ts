import { createClient, RedisClientType } from 'redis';
import { StructuredLogger } from '../utils/logger';
import { QueuedJ<PERSON>, JobData } from '../types';
import workerConfig from '../utils/config';

const logger = new StructuredLogger({ component: 'redis-client' });

export class RedisClient {
  private client: RedisClientType;
  private isConnected: boolean = false;

  constructor() {
    const redisUrl = workerConfig.redis.password
      ? `redis://:${workerConfig.redis.password}@${workerConfig.redis.host}:${workerConfig.redis.port}/${workerConfig.redis.db}`
      : `redis://${workerConfig.redis.host}:${workerConfig.redis.port}/${workerConfig.redis.db}`;

    this.client = createClient({
      url: redisUrl,
      socket: {
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            logger.error('Redis connection failed after 10 retries');
            return false;
          }
          return Math.min(retries * 100, 3000);
        },
      },
    });

    this.client.on('error', (error) => {
      logger.error('Redis client error', error);
      this.isConnected = false;
    });

    this.client.on('connect', () => {
      logger.info('Redis client connected');
      this.isConnected = true;
    });

    this.client.on('disconnect', () => {
      logger.warn('Redis client disconnected');
      this.isConnected = false;
    });
  }

  async connect(): Promise<void> {
    try {
      await this.client.connect();
      this.isConnected = true;
      logger.info('Successfully connected to Redis', {
        host: workerConfig.redis.host,
        port: workerConfig.redis.port,
        db: workerConfig.redis.db,
      });
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect to Redis', error as Error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await this.client.disconnect();
      this.isConnected = false;
      logger.info('Disconnected from Redis');
    } catch (error) {
      logger.error('Error disconnecting from Redis', error as Error);
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.client.ping();
      const healthy = result === 'PONG';
      this.isConnected = healthy;
      return healthy;
    } catch (error) {
      logger.error('Redis health check failed', error as Error);
      this.isConnected = false;
      return false;
    }
  }

  async popFromQueue(queueName: string, timeout: number = 10): Promise<QueuedJob | null> {
    try {
      // Use BLPOP for blocking pop with timeout
      const result = await this.client.blPop(
        { key: queueName, timeout },
      );

      if (!result) {
        return null; // Timeout occurred
      }

      const jobData = JSON.parse(result.element) as QueuedJob;
      
      logger.debug('Job popped from queue', {
        queueName,
        jobId: jobData.id,
        appointmentId: jobData.data.appointmentRequestId,
      });

      return jobData;
    } catch (error) {
      logger.error('Failed to pop job from queue', error as Error, {
        queueName,
      });
      throw error;
    }
  }

  async pushToRetryQueue(job: QueuedJob, retryReason: string): Promise<void> {
    try {
      const retryJob = {
        ...job,
        attempts: job.attempts + 1,
        isRetry: true,
        retryReason,
        retriedAt: new Date().toISOString(),
      };

      // Push to retry queue with higher priority (front of queue)
      await this.client.lPush('retry-queue', JSON.stringify(retryJob));

      logger.info('Job pushed to retry queue', {
        jobId: job.id,
        attempt: retryJob.attempts,
        maxAttempts: job.maxAttempts,
        retryReason,
      });
    } catch (error) {
      logger.error('Failed to push job to retry queue', error as Error, {
        jobId: job.id,
        retryReason,
      });
      throw error;
    }
  }

  async pushToDeadLetterQueue(job: QueuedJob, failureReason: string): Promise<void> {
    try {
      const deadJob = {
        ...job,
        failedAt: new Date().toISOString(),
        failureReason,
      };

      await this.client.lPush('dead-letter-queue', JSON.stringify(deadJob));

      logger.warn('Job moved to dead letter queue', {
        jobId: job.id,
        attempts: job.attempts,
        failureReason,
      });
    } catch (error) {
      logger.error('Failed to push job to dead letter queue', error as Error, {
        jobId: job.id,
        failureReason,
      });
      throw error;
    }
  }

  async getQueueLength(queueName: string): Promise<number> {
    try {
      return await this.client.lLen(queueName);
    } catch (error) {
      logger.error('Failed to get queue length', error as Error, { queueName });
      return 0;
    }
  }

  async setJobStatus(jobId: string, status: string, ttl: number = 3600): Promise<void> {
    try {
      await this.client.setEx(`job:${jobId}:status`, ttl, status);
    } catch (error) {
      logger.error('Failed to set job status', error as Error, { jobId, status });
    }
  }

  async getJobStatus(jobId: string): Promise<string | null> {
    try {
      return await this.client.get(`job:${jobId}:status`);
    } catch (error) {
      logger.error('Failed to get job status', error as Error, { jobId });
      return null;
    }
  }

  get isHealthy(): boolean {
    return this.isConnected;
  }
}

// Export singleton instance
export const redisClient = new RedisClient();
export default redisClient; 