import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { StructuredLogger } from '../utils/logger';
import workerConfig from '../utils/config';

const logger = new StructuredLogger({ component: 'database-client' });

export interface AppointmentRequest {
  id: string;
  user_id: string;
  patient_id?: string;
  patient_first_name: string;
  patient_last_name: string;
  patient_health_card: string;
  patient_health_card_sequence: string;
  patient_date_of_birth: string;
  patient_postal_code: string;
  appointment_date?: string;
  appointment_time_preference: 'asap' | 'morning' | 'afternoon' | 'evening';
  search_radius?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  notification_email?: string;
  language_preference?: 'fr' | 'en';
}

export interface Database {
  public: {
    Tables: {
      appointment_requests: {
        Row: AppointmentRequest;
        Insert: Omit<AppointmentRequest, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<AppointmentRequest, 'id' | 'created_at'>>;
      };
      completed_appointments: {
        Row: any;
        Insert: any;
        Update: any;
      };
    };
  };
}

export class DatabaseClient {
  private client: SupabaseClient<Database>;
  private isConnected: boolean = false;

  constructor() {
    this.client = createClient<Database>(
      workerConfig.supabase.url,
      workerConfig.supabase.serviceKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        db: {
          schema: 'public',
        },
      }
    );

    logger.info('Database client initialized', {
      url: workerConfig.supabase.url.substring(0, 30) + '...',
    });
  }

  async connect(): Promise<void> {
    try {
      // Test connection by making a simple query
      const { error } = await this.client
        .from('appointment_requests')
        .select('count')
        .limit(1);

      if (error) {
        throw error;
      }

      this.isConnected = true;
      logger.info('Successfully connected to database');
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect to database', error as Error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    // Supabase client doesn't need explicit disconnection
    this.isConnected = false;
    logger.info('Disconnected from database');
  }

  async healthCheck(): Promise<boolean> {
    try {
      const { error } = await this.client
        .from('appointment_requests')
        .select('count')
        .limit(1);

      const healthy = !error;
      this.isConnected = healthy;

      if (!healthy) {
        logger.warn('Database health check failed', { error: error?.message });
      }

      return healthy;
    } catch (error) {
      logger.error('Database health check error', error as Error);
      this.isConnected = false;
      return false;
    }
  }

  async updateAppointmentStatus(
    id: string,
    status: AppointmentRequest['status'],
    correlationId?: string
  ): Promise<void> {
    const requestLogger = logger.withCorrelationId(correlationId || '');

    try {
      const { error } = await this.client
        .from('appointment_requests')
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      requestLogger.info('Updated appointment request status', {
        appointmentId: id,
        status,
      });
    } catch (error) {
      requestLogger.error('Failed to update appointment status', error as Error, {
        appointmentId: id,
        status,
      });
      throw error;
    }
  }

  async markAppointmentAsProcessing(id: string, correlationId?: string): Promise<void> {
    return this.updateAppointmentStatus(id, 'in_progress', correlationId);
  }

  async markAppointmentAsCompleted(id: string, correlationId?: string): Promise<void> {
    return this.updateAppointmentStatus(id, 'completed', correlationId);
  }

  async markAppointmentAsFailed(id: string, correlationId?: string): Promise<void> {
    return this.updateAppointmentStatus(id, 'cancelled', correlationId);
  }

  async saveCompletedAppointment(
    appointmentRequestId: string,
    appointmentDetails: {
      date: string;
      time: string;
      clinic: string;
      address: string;
    },
    correlationId?: string
  ): Promise<void> {
    const requestLogger = logger.withCorrelationId(correlationId || '');

    try {
      const { error } = await this.client
        .from('completed_appointments')
        .insert({
          appointment_request_id: appointmentRequestId,
          appointment_date: appointmentDetails.date,
          appointment_time: appointmentDetails.time,
          clinic_name: appointmentDetails.clinic,
          clinic_address: appointmentDetails.address,
          booked_at: new Date().toISOString(),
        });

      if (error) {
        throw error;
      }

      requestLogger.info('Saved completed appointment details', {
        appointmentRequestId,
        appointmentDate: appointmentDetails.date,
        appointmentTime: appointmentDetails.time,
      });
    } catch (error) {
      requestLogger.error('Failed to save completed appointment', error as Error, {
        appointmentRequestId,
      });
      throw error;
    }
  }

  get isHealthy(): boolean {
    return this.isConnected;
  }

  get supabaseClient(): SupabaseClient<Database> {
    return this.client;
  }
}

// Export singleton instance
export const databaseClient = new DatabaseClient();
export default databaseClient; 