import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import { StructuredLogger } from '../utils/logger';
import { JobData, JobResult, ScraperUserConfig } from '../types';
import workerConfig from '../utils/config';

const logger = new StructuredLogger({ component: 'scraper-runner' });

export class ScraperRunner {
  private scraperPath: string;
  private scraperScript: string;

  constructor() {
    this.scraperPath = path.resolve(workerConfig.scraper.path);
    this.scraperScript = workerConfig.scraper.script;
    
    logger.info('Scraper runner initialized', {
      scraperPath: this.scraperPath,
      scraperScript: this.scraperScript,
    });
  }

  async runScraper(jobData: JobData, correlationId: string): Promise<JobResult> {
    const startTime = Date.now();
    const jobLogger = logger.withCorrelationId(correlationId);
    const logs: string[] = [];

    try {
      // Validate scraper exists
      await this.validateScraperExists();

      // Create user configuration for the scraper
      const userConfig = this.mapJobDataToScraperConfig(jobData);

      // Create temporary config file
      const configFilePath = await this.createTempConfigFile(userConfig, jobData.appointmentRequestId);

      try {
        // Run the scraper
        const result = await this.executeScraper(configFilePath, jobLogger, logs);
        
        // Clean up temp file
        await this.cleanupTempFile(configFilePath);

        const duration = Date.now() - startTime;
        
        jobLogger.info('Scraper execution completed', {
          appointmentId: jobData.appointmentRequestId,
          duration,
          success: result.success,
        });

        return {
          ...result,
          duration,
          logs,
        };
      } catch (error) {
        // Clean up temp file on error
        await this.cleanupTempFile(configFilePath);
        throw error;
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      jobLogger.error('Scraper execution failed', error as Error, {
        appointmentId: jobData.appointmentRequestId,
        duration,
      });

      return {
        success: false,
        error: errorMessage,
        logs,
        duration,
      };
    }
  }

  private async validateScraperExists(): Promise<void> {
    const scriptPath = path.join(this.scraperPath, this.scraperScript);
    
    try {
      await fs.promises.access(scriptPath, fs.constants.F_OK);
    } catch (error) {
      throw new Error(`Scraper script not found at: ${scriptPath}`);
    }

    // Check if package.json exists
    const packageJsonPath = path.join(this.scraperPath, 'package.json');
    try {
      await fs.promises.access(packageJsonPath, fs.constants.F_OK);
    } catch (error) {
      logger.warn('package.json not found in scraper directory', {
        path: packageJsonPath,
      });
    }
  }

  private mapJobDataToScraperConfig(jobData: JobData): ScraperUserConfig {
    // Parse date of birth
    const dob = new Date(jobData.patientInfo.dateOfBirth);
    const birthDay = dob.getDate().toString().padStart(2, '0');
    const birthMonth = (dob.getMonth() + 1).toString().padStart(2, '0');
    const birthYear = dob.getFullYear().toString();

    // Map time preference
    const momentOfDay = jobData.searchCriteria.timePreference;

    // Map language
    const language = jobData.notificationPreferences.language === 'fr' ? 'french' : 'english';

    // Format appointment date if provided
    let appointmentDate = '';
    if (jobData.searchCriteria.appointmentDate) {
      const apptDate = new Date(jobData.searchCriteria.appointmentDate);
      appointmentDate = `${apptDate.getDate().toString().padStart(2, '0')}-${(apptDate.getMonth() + 1).toString().padStart(2, '0')}-${apptDate.getFullYear()}`;
    }

    return {
      firstName: jobData.patientInfo.firstName,
      lastName: jobData.patientInfo.lastName,
      healthCardNumber: jobData.patientInfo.healthCard,
      cardSequenceNumber: jobData.patientInfo.healthCardSequence,
      birthDay,
      birthMonth,
      birthYear,
      email: jobData.notificationPreferences.email,
      cellphone: '', // Will need to be added to JobData if needed
      language,
      communicationMethod: 'email',
      postalCode: jobData.patientInfo.postalCode,
      searchPerimeter: this.mapSearchRadiusToPerimeter(jobData.searchCriteria.searchRadius),
      appointmentDate,
      consultationReason: 'ac2a5fa4-8514-11ef-a759-005056b11d6c', // Default consultation urgente
      momentOfDay,
      transportationDistance: jobData.searchCriteria.searchRadius.toString(),
      afterThisTime: '', // Can be added to JobData if needed
      beforeThisTime: '', // Can be added to JobData if needed
      notBeforeThisDelay: '2', // Default 2 hours
    };
  }

  private mapSearchRadiusToPerimeter(radius: number): string {
    // Map radius to search perimeter options
    if (radius <= 10) return '1'; // 10km
    if (radius <= 20) return '2'; // 20km
    if (radius <= 30) return '3'; // 30km
    if (radius <= 40) return '4'; // 40km
    return '5'; // 50km
  }

  private async createTempConfigFile(userConfig: ScraperUserConfig, appointmentId: string): Promise<string> {
    const tempDir = path.join(this.scraperPath, 'temp');
    
    // Ensure temp directory exists
    try {
      await fs.promises.mkdir(tempDir, { recursive: true });
    } catch (error) {
      // Directory might already exist, ignore error
    }

    const configFileName = `config_${appointmentId}_${Date.now()}.js`;
    const configFilePath = path.join(tempDir, configFileName);

    // Create the config file content
    const configContent = `
// Auto-generated configuration for appointment ${appointmentId}
const USER_CONFIG = ${JSON.stringify(userConfig, null, 2)};

module.exports = USER_CONFIG;
`;

    await fs.promises.writeFile(configFilePath, configContent, 'utf8');
    
    logger.debug('Created temporary config file', {
      configFilePath,
      appointmentId,
    });

    return configFilePath;
  }

  private async executeScraper(configFilePath: string, jobLogger: StructuredLogger, logs: string[]): Promise<Omit<JobResult, 'duration' | 'logs'>> {
    return new Promise((resolve, reject) => {
      const scriptPath = path.join(this.scraperPath, this.scraperScript);
      
      // Set environment variable for config file path
      const env = {
        ...process.env,
        SCRAPER_CONFIG_PATH: configFilePath,
      };

      const childProcess: ChildProcess = spawn('node', [scriptPath], {
        cwd: this.scraperPath,
        env,
        stdio: ['pipe', 'pipe', 'pipe'],
      });

      let stdout = '';
      let stderr = '';
      let appointmentBooked = false;
      let appointmentDetails: JobResult['appointmentDetails'];

      // Handle stdout
      childProcess.stdout?.on('data', (data: Buffer) => {
        const output = data.toString();
        stdout += output;
        logs.push(`STDOUT: ${output.trim()}`);
        
        // Check for success message
        if (output.includes('SUCCESS: Appointment booked successfully!')) {
          appointmentBooked = true;
          jobLogger.info('Appointment booking detected in scraper output');
        }

        // Try to extract appointment details from logs
        // This would need to be customized based on the actual scraper output format
        this.extractAppointmentDetails(output, appointmentDetails);
      });

      // Handle stderr
      childProcess.stderr?.on('data', (data: Buffer) => {
        const output = data.toString();
        stderr += output;
        logs.push(`STDERR: ${output.trim()}`);
      });

      // Handle process exit
      childProcess.on('close', (code: number | null) => {
        jobLogger.debug('Scraper process exited', {
          exitCode: code,
          stdoutLength: stdout.length,
          stderrLength: stderr.length,
        });

        if (code === 0) {
          resolve({
            success: true,
            appointmentBooked,
            ...(appointmentDetails && { appointmentDetails }),
          });
        } else {
          resolve({
            success: false,
            error: `Scraper process exited with code ${code}. STDERR: ${stderr}`,
          });
        }
      });

      // Handle process error
      childProcess.on('error', (error: Error) => {
        jobLogger.error('Scraper process error', error);
        reject(new Error(`Failed to start scraper process: ${error.message}`));
      });

      // Set timeout
      const timeout = setTimeout(() => {
        childProcess.kill('SIGTERM');
        reject(new Error('Scraper execution timed out'));
      }, workerConfig.timeout);

      childProcess.on('close', () => {
        clearTimeout(timeout);
      });
    });
  }

  private extractAppointmentDetails(output: string, currentDetails?: JobResult['appointmentDetails']): JobResult['appointmentDetails'] {
    // This is a placeholder implementation
    // You would need to customize this based on the actual output format of your scraper
    
    // Look for patterns in the output that indicate appointment details
    // For example:
    // - Date: 2025-06-09
    // - Time: 14:30
    // - Clinic: Some Clinic Name
    // - Address: 123 Main St, Montreal, QC
    
    return currentDetails;
  }

  private async cleanupTempFile(filePath: string): Promise<void> {
    try {
      await fs.promises.unlink(filePath);
      logger.debug('Cleaned up temporary config file', { filePath });
    } catch (error) {
      logger.warn('Failed to cleanup temporary config file', { filePath, error });
    }
  }
} 