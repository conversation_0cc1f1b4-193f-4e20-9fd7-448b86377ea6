import * as http from 'http';
import { PuppeteerWorker } from './worker';
import { StructuredLogger } from './utils/logger';
import workerConfig from './utils/config';

const logger = new StructuredLogger({ component: 'main' });

class WorkerService {
  private worker: PuppeteerWorker;
  private healthServer?: http.Server;
  private isShuttingDown: boolean = false;

  constructor() {
    this.worker = new PuppeteerWorker();
  }

  async start(): Promise<void> {
    try {
      logger.info('Starting Puppeteer Worker Service', {
        workerId: workerConfig.workerId,
        nodeVersion: process.version,
        platform: process.platform,
      });

      // Start health check server
      await this.startHealthServer();

      // Setup graceful shutdown handlers
      this.setupGracefulShutdown();

      // Start the worker
      await this.worker.start();

    } catch (error) {
      logger.fatal('Failed to start worker service', error as Error);
      process.exit(1);
    }
  }

  private async startHealthServer(): Promise<void> {
    const port = parseInt(process.env['HEALTH_CHECK_PORT'] || '3001', 10);

    this.healthServer = http.createServer(async (req, res) => {
      // Set CORS headers
      res.setHeader('Access-Control-Allow-Origin', '*');
      res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

      if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
      }

      if (req.url === '/health' && req.method === 'GET') {
        try {
          const healthStatus = await this.worker.getHealthStatus();
          
          const statusCode = healthStatus.healthy ? 200 : 503;
          res.writeHead(statusCode, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            status: healthStatus.healthy ? 'healthy' : 'unhealthy',
            timestamp: new Date().toISOString(),
            workerId: workerConfig.workerId,
            ...healthStatus,
          }, null, 2));
        } catch (error) {
          logger.error('Health check failed', error as Error);
          res.writeHead(503, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            status: 'unhealthy',
            error: 'Health check failed',
            timestamp: new Date().toISOString(),
            workerId: workerConfig.workerId,
          }, null, 2));
        }
      } else if (req.url === '/metrics' && req.method === 'GET') {
        try {
          const metrics = this.worker.workerMetrics;
          res.writeHead(200, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            workerId: workerConfig.workerId,
            timestamp: new Date().toISOString(),
            metrics,
          }, null, 2));
        } catch (error) {
          logger.error('Metrics endpoint failed', error as Error);
          res.writeHead(500, { 'Content-Type': 'application/json' });
          res.end(JSON.stringify({
            error: 'Metrics unavailable',
            timestamp: new Date().toISOString(),
          }, null, 2));
        }
      } else {
        res.writeHead(404, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          error: 'Not found',
          availableEndpoints: ['/health', '/metrics'],
        }, null, 2));
      }
    });

    return new Promise((resolve, reject) => {
      this.healthServer!.listen(port, () => {
        logger.info('Health check server started', { port });
        resolve();
      });

      this.healthServer!.on('error', (error) => {
        logger.error('Health server error', error);
        reject(error);
      });
    });
  }

  private setupGracefulShutdown(): void {
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'] as const;

    signals.forEach((signal) => {
      process.on(signal, async () => {
        if (this.isShuttingDown) {
          logger.warn(`Received ${signal} during shutdown, forcing exit`);
          process.exit(1);
        }

        logger.info(`Received ${signal}, starting graceful shutdown`);
        this.isShuttingDown = true;

        try {
          await this.shutdown();
          logger.info('Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          logger.error('Error during shutdown', error as Error);
          process.exit(1);
        }
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.fatal('Uncaught exception', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.fatal('Unhandled promise rejection', new Error(String(reason)), {
        promise: String(promise),
      });
      process.exit(1);
    });
  }

  private async shutdown(): Promise<void> {
    const shutdownTimeout = 45000; // 45 seconds
    const shutdownPromise = this.performShutdown();
    
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Shutdown timeout exceeded'));
      }, shutdownTimeout);
    });

    try {
      await Promise.race([shutdownPromise, timeoutPromise]);
    } catch (error) {
      logger.error('Shutdown timeout or error', error as Error);
      throw error;
    }
  }

  private async performShutdown(): Promise<void> {
    logger.info('Starting shutdown sequence');

    // Stop accepting new health checks
    if (this.healthServer) {
      await new Promise<void>((resolve) => {
        this.healthServer!.close(() => {
          logger.info('Health server stopped');
          resolve();
        });
      });
    }

    // Stop the worker
    await this.worker.stop();

    logger.info('Shutdown sequence completed');
  }
}

// Start the service
const workerService = new WorkerService();
workerService.start().catch((error) => {
  logger.fatal('Failed to start worker service', error);
  process.exit(1);
}); 