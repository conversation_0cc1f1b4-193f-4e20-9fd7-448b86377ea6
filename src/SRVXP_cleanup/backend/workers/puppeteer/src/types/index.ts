export interface JobData {
  appointmentRequestId: string;
  patientInfo: {
    firstName: string;
    lastName: string;
    healthCard: string;
    healthCardSequence: string;
    dateOfBirth: string;
    postalCode: string;
  };
  searchCriteria: {
    appointmentDate?: string;
    timePreference: 'asap' | 'morning' | 'afternoon' | 'evening';
    searchRadius: number;
  };
  notificationPreferences: {
    email: string;
    language: 'fr' | 'en';
  };
  userId: string;
  familyMemberId?: string;
  correlationId: string;
}

export interface QueuedJob {
  id: string;
  type: 'appointment-request';
  isRetry: boolean;
  data: JobData;
  attempts: number;
  maxAttempts: number;
  createdAt: string;
  scheduledFor?: string;
}

export interface WorkerConfig {
  workerId: string;
  concurrency: number;
  timeout: number;
  retryAttempts: number;
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };
  puppeteer: {
    headless: boolean;
    timeout: number;
    viewport: {
      width: number;
      height: number;
    };
  };
  scraper: {
    path: string;
    script: string;
  };
  supabase: {
    url: string;
    serviceKey: string;
  };
}

export interface ScraperUserConfig {
  firstName: string;
  lastName: string;
  healthCardNumber: string;
  cardSequenceNumber: string;
  birthDay: string;
  birthMonth: string;
  birthYear: string;
  email: string;
  cellphone: string;
  language: 'french' | 'english';
  communicationMethod: 'email' | 'sms' | 'both';
  postalCode: string;
  searchPerimeter: string;
  appointmentDate: string;
  consultationReason: string;
  momentOfDay: 'asap' | 'morning' | 'afternoon' | 'evening';
  transportationDistance: string;
  afterThisTime: string;
  beforeThisTime: string;
  notBeforeThisDelay: string;
}

export interface JobResult {
  success: boolean;
  appointmentBooked?: boolean;
  appointmentDetails?: {
    date: string;
    time: string;
    clinic: string;
    address: string;
  };
  error?: string;
  logs: string[];
  duration: number;
}

export interface WorkerMetrics {
  jobsProcessed: number;
  jobsSucceeded: number;
  jobsFailed: number;
  averageProcessingTime: number;
  lastJobProcessedAt?: string;
  workerStartedAt: string;
} 