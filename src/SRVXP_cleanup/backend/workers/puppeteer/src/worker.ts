import { StructuredLogger } from './utils/logger';
import { <PERSON><PERSON><PERSON><PERSON>, JobR<PERSON>ult, WorkerMetrics } from './types';
import { RedisClient } from './services/redis-client';
import { ScraperRunner } from './services/scraper-runner';
import { DatabaseClient } from './services/database-client';
import workerConfig from './utils/config';

const logger = new StructuredLogger({ 
  component: 'puppeteer-worker',
  workerId: workerConfig.workerId,
});

export class PuppeteerWorker {
  private redisClient: RedisClient;
  private scraperRunner: ScraperRunner;
  private databaseClient: DatabaseClient;
  private isRunning: boolean = false;
  private shouldStop: boolean = false;
  private metrics: WorkerMetrics;
  private processingJobs: Set<string> = new Set();

  constructor() {
    this.redisClient = new RedisClient();
    this.scraperRunner = new ScraperRunner();
    this.databaseClient = new DatabaseClient();
    
    this.metrics = {
      jobsProcessed: 0,
      jobsSucceeded: 0,
      jobsFailed: 0,
      averageProcessingTime: 0,
      workerStartedAt: new Date().toISOString(),
    };

    logger.info('Puppeteer worker initialized', {
      workerId: workerConfig.workerId,
      concurrency: workerConfig.concurrency,
      timeout: workerConfig.timeout,
    });
  }

  async start(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Worker is already running');
      return;
    }

    try {
      // Connect to services
      await this.redisClient.connect();
      await this.databaseClient.connect();

      this.isRunning = true;
      this.shouldStop = false;

      logger.info('Worker started successfully', {
        workerId: workerConfig.workerId,
      });

      // Start processing jobs
      await this.processJobs();
    } catch (error) {
      logger.error('Failed to start worker', error as Error);
      await this.stop();
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logger.info('Stopping worker...', {
      workerId: workerConfig.workerId,
      processingJobs: this.processingJobs.size,
    });

    this.shouldStop = true;

    // Wait for current jobs to complete (with timeout)
    const stopTimeout = 30000; // 30 seconds
    const startTime = Date.now();
    
    while (this.processingJobs.size > 0 && (Date.now() - startTime) < stopTimeout) {
      logger.info('Waiting for jobs to complete', {
        remainingJobs: this.processingJobs.size,
      });
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (this.processingJobs.size > 0) {
      logger.warn('Force stopping worker with jobs still processing', {
        remainingJobs: this.processingJobs.size,
      });
    }

    // Disconnect from services
    await this.redisClient.disconnect();
    await this.databaseClient.disconnect();

    this.isRunning = false;

    logger.info('Worker stopped', {
      workerId: workerConfig.workerId,
      metrics: this.metrics,
    });
  }

  private async processJobs(): Promise<void> {
    const queueNames = ['retry-queue', 'appointment-requests'];
    
    while (!this.shouldStop) {
      try {
        // Check if we're at concurrency limit
        if (this.processingJobs.size >= workerConfig.concurrency) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }

        // Try to get a job from queues (retry queue has priority)
        let job: QueuedJob | null = null;
        
        for (const queueName of queueNames) {
          job = await this.redisClient.popFromQueue(queueName, 5); // 5 second timeout
          if (job) {
            logger.debug('Job received from queue', {
              queueName,
              jobId: job.id,
              appointmentId: job.data.appointmentRequestId,
            });
            break;
          }
        }

        if (!job) {
          // No jobs available, continue polling
          continue;
        }

        // Process job asynchronously
        this.processJobAsync(job).catch((error) => {
          logger.error('Unhandled error in job processing', error as Error, {
            jobId: job?.id,
          });
        });

      } catch (error) {
        logger.error('Error in job processing loop', error as Error);
        // Wait before retrying to avoid tight error loops
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }

  private async processJobAsync(job: QueuedJob): Promise<void> {
    const jobLogger = logger.withCorrelationId(job.data.correlationId);
    const startTime = Date.now();
    
    // Track processing job
    this.processingJobs.add(job.id);

    try {
      jobLogger.info('Processing job started', {
        jobId: job.id,
        appointmentId: job.data.appointmentRequestId,
        attempt: job.attempts,
        isRetry: job.isRetry,
      });

      // Mark appointment as in progress
      await this.databaseClient.markAppointmentAsProcessing(
        job.data.appointmentRequestId,
        job.data.correlationId
      );

      // Set job status in Redis
      await this.redisClient.setJobStatus(job.id, 'processing');

      // Run the scraper
      const result = await this.scraperRunner.runScraper(
        job.data,
        job.data.correlationId
      );

      // Process the result
      await this.handleJobResult(job, result, jobLogger);

      // Update metrics
      this.updateMetrics(true, Date.now() - startTime);

      jobLogger.info('Job processing completed', {
        jobId: job.id,
        appointmentId: job.data.appointmentRequestId,
        success: result.success,
        appointmentBooked: result.appointmentBooked,
        duration: result.duration,
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      
      jobLogger.error('Job processing failed', error as Error, {
        jobId: job.id,
        appointmentId: job.data.appointmentRequestId,
        duration,
      });

      // Handle job failure
      await this.handleJobFailure(job, error as Error, jobLogger);

      // Update metrics
      this.updateMetrics(false, duration);

    } finally {
      // Remove from processing set
      this.processingJobs.delete(job.id);
    }
  }

  private async handleJobResult(
    job: QueuedJob,
    result: JobResult,
    jobLogger: StructuredLogger
  ): Promise<void> {
    try {
      if (result.success && result.appointmentBooked) {
        // Appointment was successfully booked
        await this.databaseClient.markAppointmentAsCompleted(
          job.data.appointmentRequestId,
          job.data.correlationId
        );

        // Save appointment details if available
        if (result.appointmentDetails) {
          await this.databaseClient.saveCompletedAppointment(
            job.data.appointmentRequestId,
            result.appointmentDetails,
            job.data.correlationId
          );
        }

        await this.redisClient.setJobStatus(job.id, 'completed');

        jobLogger.info('Appointment successfully booked', {
          jobId: job.id,
          appointmentId: job.data.appointmentRequestId,
          appointmentDetails: result.appointmentDetails,
        });

      } else if (result.success && !result.appointmentBooked) {
        // Scraper ran successfully but no appointment was found/booked
        // This might be a retry case or no availability
        
        if (job.attempts < job.maxAttempts) {
          // Retry the job
          await this.redisClient.pushToRetryQueue(
            job,
            'No appointment available, retrying'
          );
          
          await this.redisClient.setJobStatus(job.id, 'retrying');

          jobLogger.info('No appointment found, job queued for retry', {
            jobId: job.id,
            appointmentId: job.data.appointmentRequestId,
            attempt: job.attempts,
            maxAttempts: job.maxAttempts,
          });
        } else {
          // Max attempts reached, mark as failed
          await this.databaseClient.markAppointmentAsFailed(
            job.data.appointmentRequestId,
            job.data.correlationId
          );

          await this.redisClient.setJobStatus(job.id, 'failed');

          jobLogger.warn('Max retry attempts reached, marking as failed', {
            jobId: job.id,
            appointmentId: job.data.appointmentRequestId,
            attempts: job.attempts,
          });
        }

      } else {
        // Scraper failed to run properly
        if (job.attempts < job.maxAttempts) {
          // Retry the job
          await this.redisClient.pushToRetryQueue(
            job,
            result.error || 'Scraper execution failed'
          );

          await this.redisClient.setJobStatus(job.id, 'retrying');

          jobLogger.warn('Scraper failed, job queued for retry', {
            jobId: job.id,
            appointmentId: job.data.appointmentRequestId,
            error: result.error,
            attempt: job.attempts,
          });
        } else {
          // Max attempts reached, move to dead letter queue
          await this.redisClient.pushToDeadLetterQueue(
            job,
            result.error || 'Scraper execution failed after max attempts'
          );

          await this.databaseClient.markAppointmentAsFailed(
            job.data.appointmentRequestId,
            job.data.correlationId
          );

          await this.redisClient.setJobStatus(job.id, 'dead');

          jobLogger.error('Job moved to dead letter queue', undefined, {
            jobId: job.id,
            appointmentId: job.data.appointmentRequestId,
            error: result.error,
            attempts: job.attempts,
          });
        }
      }
    } catch (error) {
      jobLogger.error('Failed to handle job result', error as Error, {
        jobId: job.id,
        appointmentId: job.data.appointmentRequestId,
      });
      throw error;
    }
  }

  private async handleJobFailure(
    job: QueuedJob,
    error: Error,
    jobLogger: StructuredLogger
  ): Promise<void> {
    try {
      if (job.attempts < job.maxAttempts) {
        // Retry the job
        await this.redisClient.pushToRetryQueue(job, error.message);
        await this.redisClient.setJobStatus(job.id, 'retrying');

        jobLogger.warn('Job failed, queued for retry', {
          jobId: job.id,
          appointmentId: job.data.appointmentRequestId,
          error: error.message,
          attempt: job.attempts,
        });
      } else {
        // Max attempts reached, move to dead letter queue
        await this.redisClient.pushToDeadLetterQueue(job, error.message);
        
        await this.databaseClient.markAppointmentAsFailed(
          job.data.appointmentRequestId,
          job.data.correlationId
        );

        await this.redisClient.setJobStatus(job.id, 'dead');

        jobLogger.error('Job moved to dead letter queue after max attempts', error, {
          jobId: job.id,
          appointmentId: job.data.appointmentRequestId,
          attempts: job.attempts,
        });
      }
    } catch (handlingError) {
      jobLogger.error('Failed to handle job failure', handlingError as Error, {
        jobId: job.id,
        appointmentId: job.data.appointmentRequestId,
        originalError: error.message,
      });
    }
  }

  private updateMetrics(success: boolean, duration: number): void {
    this.metrics.jobsProcessed++;
    
    if (success) {
      this.metrics.jobsSucceeded++;
    } else {
      this.metrics.jobsFailed++;
    }

    // Update average processing time
    const totalTime = this.metrics.averageProcessingTime * (this.metrics.jobsProcessed - 1) + duration;
    this.metrics.averageProcessingTime = totalTime / this.metrics.jobsProcessed;
    
    this.metrics.lastJobProcessedAt = new Date().toISOString();
  }

  async getHealthStatus(): Promise<{
    healthy: boolean;
    services: {
      redis: boolean;
      database: boolean;
    };
    metrics: WorkerMetrics;
    processingJobs: number;
  }> {
    const redisHealthy = await this.redisClient.healthCheck();
    const databaseHealthy = await this.databaseClient.healthCheck();
    
    return {
      healthy: redisHealthy && databaseHealthy && this.isRunning,
      services: {
        redis: redisHealthy,
        database: databaseHealthy,
      },
      metrics: { ...this.metrics },
      processingJobs: this.processingJobs.size,
    };
  }

  get isHealthy(): boolean {
    return this.isRunning && this.redisClient.isHealthy && this.databaseClient.isHealthy;
  }

  get workerMetrics(): WorkerMetrics {
    return { ...this.metrics };
  }
} 