# Environment Configuration for SRVXP Appointment Booking Backend
# Copy this file to .env and fill in your actual values

# Node.js Environment
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Redis Configuration
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=
REDIS_DB=0

# API Server Configuration
API_PORT=3001
API_HOST=0.0.0.0
API_CORS_ORIGIN=http://localhost:3002

# Worker Configuration
PUPPETEER_HEADLESS=true
PUPPETEER_TIMEOUT=30000
PUPPETEER_SLOWMO=0
PUPPETEER_DEVTOOLS=false

# Worker Scaling
MAX_WORKERS=3
WORKER_RESTART_DELAY=5000
WORKER_MAX_RETRIES=3

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_RETENTION_DAYS=30

# Healthcare Provider Configuration
PROVIDER_RETRY_ATTEMPTS=3
PROVIDER_RETRY_DELAY=5000
PROVIDER_TIMEOUT=60000

# Screenshot Configuration
SCREENSHOT_ENABLED=true
SCREENSHOT_QUALITY=80
SCREENSHOT_FORMAT=png

# Development Configuration (only used in development)
DEV_ENABLE_MOCK_PROVIDERS=false
DEV_WORKER_POLL_INTERVAL=5000
DEV_ENABLE_DEBUG_SCREENSHOTS=true 