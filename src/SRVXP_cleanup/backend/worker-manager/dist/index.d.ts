declare class WorkerManagerApp {
    private app;
    private server?;
    private isShuttingDown;
    constructor();
    private setupMiddleware;
    private setupRoutes;
    private setupErrorHandling;
    start(): Promise<void>;
    private initializeServices;
    private startHttpServer;
    private startMonitoringServices;
    private gracefulShutdown;
    setupSignalHandlers(): void;
}
export default WorkerManagerApp;
declare global {
    namespace Express {
        interface Request {
            correlationId?: string;
        }
    }
}
//# sourceMappingURL=index.d.ts.map