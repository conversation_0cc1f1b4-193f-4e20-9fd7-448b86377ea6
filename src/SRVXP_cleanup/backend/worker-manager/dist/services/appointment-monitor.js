"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.appointmentMonitor = void 0;
const events_1 = require("events");
const supabase_client_1 = __importDefault(require("../database/supabase-client"));
const job_publisher_1 = __importDefault(require("../queue/job-publisher"));
const logger_1 = require("../utils/logger");
const uuid_1 = require("uuid");
const logger = new logger_1.StructuredLogger({ component: 'appointment-monitor' });
class AppointmentMonitor extends events_1.EventEmitter {
    constructor() {
        super();
        this.isRunning = false;
        this.startTime = new Date();
        this.pollingIntervalMs = 30000; // 30 seconds fallback polling
        this.stats = {
            totalNotifications: 0,
            processedAppointments: 0,
            failedProcessing: 0,
            duplicateJobs: 0,
            rateLimitedJobs: 0,
            uptime: 0,
            startTime: this.startTime,
        };
        // Bind event handlers
        this.handleNewAppointmentRequest = this.handleNewAppointmentRequest.bind(this);
        this.handleRealtimeInsert = this.handleRealtimeInsert.bind(this);
        logger.info('Appointment monitor initialized');
    }
    async start() {
        try {
            if (this.isRunning) {
                logger.warn('Appointment monitor already running');
                return;
            }
            logger.info('Starting appointment monitor...');
            // Set up Supabase Realtime subscription
            await this.setupRealtimeSubscription();
            // Start fallback polling (in case Realtime fails)
            this.startPolling();
            this.isRunning = true;
            this.startTime = new Date();
            logger.info('Appointment monitor started successfully with Supabase Realtime');
            this.emit('started');
        }
        catch (error) {
            logger.error('Failed to start appointment monitor', error);
            throw error;
        }
    }
    async stop() {
        try {
            if (!this.isRunning) {
                logger.warn('Appointment monitor not running');
                return;
            }
            logger.info('Stopping appointment monitor...');
            // Unsubscribe from Realtime
            if (this.realtimeChannel) {
                await supabase_client_1.default.supabaseClient.removeChannel(this.realtimeChannel);
                this.realtimeChannel = undefined;
            }
            // Stop polling
            this.stopPolling();
            this.isRunning = false;
            logger.info('Appointment monitor stopped');
            this.emit('stopped');
        }
        catch (error) {
            logger.error('Error stopping appointment monitor', error);
            throw error;
        }
    }
    async setupRealtimeSubscription() {
        try {
            // Create a channel for appointment_requests table
            this.realtimeChannel = supabase_client_1.default.supabaseClient
                .channel('appointment_requests_changes')
                .on('postgres_changes', {
                event: 'INSERT',
                schema: 'public',
                table: 'appointment_requests',
                filter: 'status=eq.pending'
            }, this.handleRealtimeInsert)
                .subscribe((status) => {
                if (status === 'SUBSCRIBED') {
                    logger.info('Successfully subscribed to Supabase Realtime for appointment_requests');
                }
                else if (status === 'CHANNEL_ERROR') {
                    logger.error('Failed to subscribe to Supabase Realtime', new Error('Channel error'));
                }
                else if (status === 'TIMED_OUT') {
                    logger.error('Supabase Realtime subscription timed out', new Error('Subscription timeout'));
                }
            });
        }
        catch (error) {
            logger.error('Failed to setup Supabase Realtime subscription', error);
            throw error;
        }
    }
    handleRealtimeInsert(payload) {
        const correlationId = (0, uuid_1.v4)();
        const requestLogger = logger.withCorrelationId(correlationId);
        try {
            const newRecord = payload.new;
            requestLogger.info('Received Realtime notification for new appointment request', {
                appointmentId: newRecord.id,
                userId: newRecord.user_id,
                status: newRecord.status,
            });
            // Process the new appointment request
            this.handleNewAppointmentRequest(newRecord);
        }
        catch (error) {
            logger.error('Error handling Realtime insert notification', error, {
                payload: JSON.stringify(payload),
            });
        }
    }
    async handleNewAppointmentRequest(notificationRecord) {
        const correlationId = (0, uuid_1.v4)();
        const requestLogger = logger.withCorrelationId(correlationId);
        try {
            this.stats.totalNotifications++;
            requestLogger.info('Processing new appointment request notification', {
                appointmentId: notificationRecord.id,
                userId: notificationRecord.user_id,
            });
            // Fetch full appointment request from Supabase
            const appointmentRequests = await supabase_client_1.default.getNewAppointmentRequests();
            const appointmentRequest = appointmentRequests.find(req => req.id === notificationRecord.id);
            if (!appointmentRequest) {
                requestLogger.warn('Appointment request not found or no longer pending', {
                    appointmentId: notificationRecord.id,
                });
                return;
            }
            // Mark as processing in database
            await supabase_client_1.default.markAppointmentAsProcessing(appointmentRequest.id, correlationId);
            // Publish job to queue
            const jobId = await job_publisher_1.default.publishAppointmentRequest(appointmentRequest, correlationId);
            if (jobId === 'duplicate') {
                this.stats.duplicateJobs++;
                requestLogger.info('Appointment request was duplicate, skipped', {
                    appointmentId: appointmentRequest.id,
                });
                return;
            }
            this.stats.processedAppointments++;
            requestLogger.info('Appointment request processed successfully', {
                appointmentId: appointmentRequest.id,
                jobId,
            });
            this.emit('appointmentProcessed', {
                appointmentId: appointmentRequest.id,
                jobId,
                correlationId,
            });
        }
        catch (error) {
            this.stats.failedProcessing++;
            if (error instanceof Error && error.message.includes('Rate limit exceeded')) {
                this.stats.rateLimitedJobs++;
                requestLogger.warn('Job rejected due to rate limiting', {
                    appointmentId: notificationRecord.id,
                    userId: notificationRecord.user_id,
                });
            }
            else {
                requestLogger.error('Failed to process appointment request', error, {
                    appointmentId: notificationRecord.id,
                });
            }
            this.emit('processingError', {
                appointmentId: notificationRecord.id,
                error: error,
                correlationId,
            });
        }
    }
    startPolling() {
        this.pollingInterval = setInterval(async () => {
            try {
                await this.pollForPendingAppointments();
            }
            catch (error) {
                logger.error('Error during polling', error);
            }
        }, this.pollingIntervalMs);
        logger.debug('Started fallback polling', {
            intervalMs: this.pollingIntervalMs,
        });
    }
    stopPolling() {
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = undefined;
            logger.debug('Stopped fallback polling');
        }
    }
    async pollForPendingAppointments() {
        try {
            const pendingAppointments = await supabase_client_1.default.getNewAppointmentRequests();
            if (pendingAppointments.length === 0) {
                return;
            }
            logger.debug('Found pending appointments during polling', {
                count: pendingAppointments.length,
            });
            // Process each pending appointment
            for (const appointment of pendingAppointments) {
                // Create a mock notification record for consistency
                const notificationRecord = {
                    id: appointment.id,
                    user_id: appointment.user_id,
                    status: appointment.status,
                };
                await this.handleNewAppointmentRequest(notificationRecord);
            }
        }
        catch (error) {
            logger.error('Error polling for pending appointments', error);
        }
    }
    async healthCheck() {
        try {
            const databaseHealthy = await supabase_client_1.default.healthCheck();
            // Update uptime
            this.stats.uptime = Date.now() - this.startTime.getTime();
            return {
                isRunning: this.isRunning,
                databaseHealthy,
                stats: { ...this.stats },
            };
        }
        catch (error) {
            logger.error('Health check failed', error);
            return {
                isRunning: false,
                databaseHealthy: false,
                stats: { ...this.stats },
            };
        }
    }
    getStats() {
        return {
            ...this.stats,
            uptime: Date.now() - this.startTime.getTime(),
        };
    }
    resetStats() {
        this.stats = {
            totalNotifications: 0,
            processedAppointments: 0,
            failedProcessing: 0,
            duplicateJobs: 0,
            rateLimitedJobs: 0,
            uptime: 0,
            startTime: new Date(),
        };
        this.startTime = new Date();
        logger.info('Statistics reset');
    }
    get isHealthy() {
        return this.isRunning && supabase_client_1.default.isHealthy;
    }
}
// Export singleton instance
exports.appointmentMonitor = new AppointmentMonitor();
exports.default = exports.appointmentMonitor;
//# sourceMappingURL=appointment-monitor.js.map