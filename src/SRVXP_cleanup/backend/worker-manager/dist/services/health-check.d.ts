export interface HealthStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: string;
    uptime: number;
    version: string;
    services: {
        supabase: ServiceHealth;
        redis: ServiceHealth;
        appointmentMonitor: ServiceHealth;
        jobPublisher: ServiceHealth;
    };
    queues: {
        appointmentRequests: number;
        retryQueue: number;
        deadLetterQueue: number;
    };
    stats: {
        totalNotifications: number;
        processedAppointments: number;
        failedProcessing: number;
        duplicateJobs: number;
        rateLimitedJobs: number;
    };
}
export interface ServiceHealth {
    status: 'healthy' | 'unhealthy';
    lastCheck: string;
    responseTime?: number;
    error?: string;
}
declare class HealthCheckService {
    private startTime;
    private healthCheckInterval?;
    private intervalMs;
    private isRunning;
    constructor();
    startPeriodicHealthChecks(): Promise<void>;
    stopPeriodicHealthChecks(): void;
    getHealthStatus(): Promise<HealthStatus>;
    private checkSupabaseHealth;
    private checkRedisHealth;
    private checkAppointmentMonitorHealth;
    private getQueueStats;
    private getMonitorStats;
    isReady(): Promise<boolean>;
    isAlive(): Promise<boolean>;
}
export declare const healthCheckService: HealthCheckService;
export default healthCheckService;
//# sourceMappingURL=health-check.d.ts.map