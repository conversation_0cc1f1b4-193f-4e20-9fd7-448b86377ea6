import { EventEmitter } from 'events';
export interface MonitorStats {
    totalNotifications: number;
    processedAppointments: number;
    failedProcessing: number;
    duplicateJobs: number;
    rateLimitedJobs: number;
    uptime: number;
    startTime: Date;
}
declare class AppointmentMonitor extends EventEmitter {
    private isRunning;
    private stats;
    private startTime;
    private pollingInterval?;
    private pollingIntervalMs;
    private realtimeChannel?;
    constructor();
    start(): Promise<void>;
    stop(): Promise<void>;
    private setupRealtimeSubscription;
    private handleRealtimeInsert;
    private handleNewAppointmentRequest;
    private startPolling;
    private stopPolling;
    private pollForPendingAppointments;
    healthCheck(): Promise<{
        isRunning: boolean;
        databaseHealthy: boolean;
        stats: MonitorStats;
    }>;
    getStats(): MonitorStats;
    resetStats(): void;
    get isHealthy(): boolean;
}
export declare const appointmentMonitor: AppointmentMonitor;
export default appointmentMonitor;
//# sourceMappingURL=appointment-monitor.d.ts.map