"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheckService = void 0;
const logger_1 = require("../utils/logger");
const supabase_client_1 = __importDefault(require("../database/supabase-client"));
const redis_client_1 = __importDefault(require("../queue/redis-client"));
const appointment_monitor_1 = __importDefault(require("./appointment-monitor"));
const job_publisher_1 = __importDefault(require("../queue/job-publisher"));
const logger = new logger_1.StructuredLogger({ component: 'health-check' });
class HealthCheckService {
    constructor() {
        this.startTime = new Date();
        this.intervalMs = 30000; // 30 seconds
        this.isRunning = false;
        logger.info('Health check service initialized');
    }
    async startPeriodicHealthChecks() {
        if (this.isRunning) {
            logger.warn('Health checks already running');
            return;
        }
        this.healthCheckInterval = setInterval(async () => {
            try {
                const health = await this.getHealthStatus();
                if (health.status === 'unhealthy') {
                    logger.error('System health check failed', new Error('Unhealthy status'), {
                        health,
                    });
                }
                else if (health.status === 'degraded') {
                    logger.warn('System health degraded', { health });
                }
                else {
                    logger.debug('System health check passed');
                }
            }
            catch (error) {
                logger.error('Error during periodic health check', error);
            }
        }, this.intervalMs);
        this.isRunning = true;
        logger.info('Started periodic health checks', {
            intervalMs: this.intervalMs,
        });
    }
    stopPeriodicHealthChecks() {
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = undefined;
            this.isRunning = false;
            logger.info('Stopped periodic health checks');
        }
    }
    async getHealthStatus() {
        const startTime = Date.now();
        try {
            // Check all services in parallel
            const [supabaseHealth, redisHealth, appointmentMonitorHealth, queueStats, monitorStats,] = await Promise.all([
                this.checkSupabaseHealth(),
                this.checkRedisHealth(),
                this.checkAppointmentMonitorHealth(),
                this.getQueueStats(),
                this.getMonitorStats(),
            ]);
            const jobPublisherHealth = {
                status: 'healthy',
                lastCheck: new Date().toISOString(),
            };
            // Determine overall status
            const services = {
                supabase: supabaseHealth,
                redis: redisHealth,
                appointmentMonitor: appointmentMonitorHealth,
                jobPublisher: jobPublisherHealth,
            };
            const unhealthyServices = Object.values(services).filter(s => s.status === 'unhealthy');
            const totalServices = Object.values(services).length;
            let overallStatus;
            if (unhealthyServices.length === 0) {
                overallStatus = 'healthy';
            }
            else if (unhealthyServices.length < totalServices / 2) {
                overallStatus = 'degraded';
            }
            else {
                overallStatus = 'unhealthy';
            }
            const healthStatus = {
                status: overallStatus,
                timestamp: new Date().toISOString(),
                uptime: Date.now() - this.startTime.getTime(),
                version: process.env.npm_package_version || '1.0.0',
                services,
                queues: queueStats,
                stats: monitorStats,
            };
            const responseTime = Date.now() - startTime;
            logger.debug('Health check completed', {
                status: overallStatus,
                responseTime,
                unhealthyServices: unhealthyServices.length,
            });
            return healthStatus;
        }
        catch (error) {
            logger.error('Error getting health status', error);
            return {
                status: 'unhealthy',
                timestamp: new Date().toISOString(),
                uptime: Date.now() - this.startTime.getTime(),
                version: process.env.npm_package_version || '1.0.0',
                services: {
                    supabase: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
                    redis: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
                    appointmentMonitor: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
                    jobPublisher: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
                },
                queues: {
                    appointmentRequests: 0,
                    retryQueue: 0,
                    deadLetterQueue: 0,
                },
                stats: {
                    totalNotifications: 0,
                    processedAppointments: 0,
                    failedProcessing: 0,
                    duplicateJobs: 0,
                    rateLimitedJobs: 0,
                },
            };
        }
    }
    async checkSupabaseHealth() {
        const startTime = Date.now();
        try {
            const isHealthy = await supabase_client_1.default.healthCheck();
            const responseTime = Date.now() - startTime;
            return {
                status: isHealthy ? 'healthy' : 'unhealthy',
                lastCheck: new Date().toISOString(),
                responseTime,
                error: isHealthy ? undefined : 'Supabase connection failed',
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                status: 'unhealthy',
                lastCheck: new Date().toISOString(),
                responseTime,
                error: error.message,
            };
        }
    }
    async checkRedisHealth() {
        const startTime = Date.now();
        try {
            const isHealthy = await redis_client_1.default.healthCheck();
            const responseTime = Date.now() - startTime;
            return {
                status: isHealthy ? 'healthy' : 'unhealthy',
                lastCheck: new Date().toISOString(),
                responseTime,
                error: isHealthy ? undefined : 'Redis connection failed',
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                status: 'unhealthy',
                lastCheck: new Date().toISOString(),
                responseTime,
                error: error.message,
            };
        }
    }
    async checkAppointmentMonitorHealth() {
        try {
            const healthCheck = await appointment_monitor_1.default.healthCheck();
            return {
                status: healthCheck.isRunning && healthCheck.databaseHealthy ? 'healthy' : 'unhealthy',
                lastCheck: new Date().toISOString(),
                error: healthCheck.isRunning && healthCheck.databaseHealthy ? undefined : 'Appointment monitor not running or database unhealthy',
            };
        }
        catch (error) {
            return {
                status: 'unhealthy',
                lastCheck: new Date().toISOString(),
                error: error.message,
            };
        }
    }
    async getQueueStats() {
        try {
            return await job_publisher_1.default.getQueueStats();
        }
        catch (error) {
            logger.error('Failed to get queue stats for health check', error);
            return {
                appointmentRequests: 0,
                retryQueue: 0,
                deadLetterQueue: 0,
            };
        }
    }
    getMonitorStats() {
        try {
            const stats = appointment_monitor_1.default.getStats();
            return {
                totalNotifications: stats.totalNotifications,
                processedAppointments: stats.processedAppointments,
                failedProcessing: stats.failedProcessing,
                duplicateJobs: stats.duplicateJobs,
                rateLimitedJobs: stats.rateLimitedJobs,
            };
        }
        catch (error) {
            logger.error('Failed to get monitor stats for health check', error);
            return {
                totalNotifications: 0,
                processedAppointments: 0,
                failedProcessing: 0,
                duplicateJobs: 0,
                rateLimitedJobs: 0,
            };
        }
    }
    // Quick health check for readiness probes
    async isReady() {
        try {
            const [supabaseHealthy, redisHealthy, monitorHealthy] = await Promise.all([
                supabase_client_1.default.healthCheck(),
                redis_client_1.default.healthCheck(),
                appointment_monitor_1.default.isHealthy,
            ]);
            return supabaseHealthy && redisHealthy && monitorHealthy;
        }
        catch (error) {
            logger.error('Readiness check failed', error);
            return false;
        }
    }
    // Quick health check for liveness probes
    async isAlive() {
        try {
            // Basic check to ensure the service is responding
            return true;
        }
        catch (error) {
            logger.error('Liveness check failed', error);
            return false;
        }
    }
}
// Export singleton instance
exports.healthCheckService = new HealthCheckService();
exports.default = exports.healthCheckService;
//# sourceMappingURL=health-check.js.map