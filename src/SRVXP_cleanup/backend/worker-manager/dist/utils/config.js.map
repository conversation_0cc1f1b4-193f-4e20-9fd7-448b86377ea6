{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/utils/config.ts"], "names": [], "mappings": ";;;AAAA,mCAAgC;AAEhC,4CAA4C;AAC5C,IAAA,eAAM,GAAE,CAAC;AAwDT,SAAS,sBAAsB,CAAC,IAAY;IAC1C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,iCAAiC,IAAI,aAAa,CAAC,CAAC;IACtE,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAY,EAAE,YAAoB;IAC3D,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,CAAC,KAAK;QAAE,OAAO,YAAY,CAAC;IAEhC,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;IACnC,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC;QAClB,MAAM,IAAI,KAAK,CAAC,wBAAwB,IAAI,yBAAyB,CAAC,CAAC;IACzE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAY,EAAE,YAAqB;IAC7D,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,CAAC,KAAK;QAAE,OAAO,YAAY,CAAC;IAEhC,OAAO,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,CAAC;AACxC,CAAC;AAEY,QAAA,SAAS,GAAW;IAC/B,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;IAC9C,IAAI,EAAE,iBAAiB,CAAC,MAAM,EAAE,IAAI,CAAC;IAErC,QAAQ,EAAE;QACR,GAAG,EAAE,sBAAsB,CAAC,cAAc,CAAC;QAC3C,UAAU,EAAE,sBAAsB,CAAC,sBAAsB,CAAC;KAC3D;IAED,KAAK,EAAE;QACL,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,wBAAwB;QACtD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;KACrC;IAED,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;QACxC,IAAI,EAAE,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC;QACxC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU;QACvC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU;QACvC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW;KAClC;IAED,OAAO,EAAE;QACP,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,MAAM;QACtC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,2BAA2B;KAC1D;IAED,aAAa,EAAE;QACb,mBAAmB,EAAE,iBAAiB,CAAC,uBAAuB,EAAE,KAAK,CAAC;QACtE,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,iBAAiB;QAC1E,SAAS,EAAE;YACT,MAAM,EAAE,iBAAiB,CAAC,mBAAmB,EAAE,KAAK,CAAC;YACrD,WAAW,EAAE,iBAAiB,CAAC,yBAAyB,EAAE,GAAG,CAAC;SAC/D;KACF;IAED,UAAU,EAAE;QACV,OAAO,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,IAAI,CAAC;QACpD,IAAI,EAAE,iBAAiB,CAAC,cAAc,EAAE,IAAI,CAAC;KAC9C;IAED,QAAQ,EAAE;QACR,OAAO,EAAE,iBAAiB,CAAC,kBAAkB,EAAE,KAAK,CAAC;KACtD;CACF,CAAC;AAEF,kBAAe,iBAAS,CAAC"}