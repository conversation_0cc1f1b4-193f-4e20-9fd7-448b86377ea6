import winston from 'winston';
declare const logger: winston.Logger;
export interface LoggerContext {
    correlationId?: string;
    component?: string;
    userId?: string;
    appointmentId?: string;
    jobId?: string;
}
export declare class StructuredLogger {
    private context;
    constructor(context?: LoggerContext);
    private log;
    info(message: string, meta?: any): void;
    error(message: string, error?: Error, meta?: any): void;
    warn(message: string, meta?: any): void;
    debug(message: string, meta?: any): void;
    child(additionalContext: LoggerContext): StructuredLogger;
    withCorrelationId(correlationId?: string): StructuredLogger;
    withComponent(component: string): StructuredLogger;
    getCorrelationId(): string | undefined;
}
export declare const defaultLogger: StructuredLogger;
export { logger as winstonLogger };
export default defaultLogger;
//# sourceMappingURL=logger.d.ts.map