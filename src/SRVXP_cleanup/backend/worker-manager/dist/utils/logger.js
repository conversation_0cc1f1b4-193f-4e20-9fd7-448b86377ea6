"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.winstonLogger = exports.defaultLogger = exports.StructuredLogger = void 0;
const winston_1 = __importDefault(require("winston"));
const uuid_1 = require("uuid");
const config_1 = __importDefault(require("./config"));
// Create logs directory if it doesn't exist
const fs_1 = require("fs");
const path_1 = require("path");
const logDir = (0, path_1.dirname)(config_1.default.logging.file);
try {
    (0, fs_1.mkdirSync)(logDir, { recursive: true });
}
catch (err) {
    // Directory might already exist
}
// Custom log format
const logFormat = winston_1.default.format.combine(winston_1.default.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston_1.default.format.errors({ stack: true }), winston_1.default.format.json(), winston_1.default.format.printf(({ timestamp, level, message, correlationId, component, ...meta }) => {
    const logEntry = {
        timestamp,
        level,
        message,
        correlationId,
        component,
        ...meta,
    };
    return JSON.stringify(logEntry);
}));
// Create Winston logger
const logger = winston_1.default.createLogger({
    level: config_1.default.logging.level,
    format: logFormat,
    defaultMeta: {
        service: 'worker-manager',
        version: process.env.npm_package_version || '1.0.0',
    },
    transports: [
        // File transport
        new winston_1.default.transports.File({
            filename: config_1.default.logging.file,
            handleExceptions: true,
            handleRejections: true,
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        // Error file transport
        new winston_1.default.transports.File({
            filename: config_1.default.logging.file.replace('.log', '.error.log'),
            level: 'error',
            handleExceptions: true,
            handleRejections: true,
            maxsize: 5242880, // 5MB
            maxFiles: 3,
        }),
    ],
});
exports.winstonLogger = logger;
// Add console transport in development
if (config_1.default.nodeEnv === 'development') {
    logger.add(new winston_1.default.transports.Console({
        format: winston_1.default.format.combine(winston_1.default.format.colorize(), winston_1.default.format.simple(), winston_1.default.format.printf(({ timestamp, level, message, correlationId, component }) => {
            const prefix = correlationId ? `[${correlationId}]` : '';
            const comp = component ? `[${component}]` : '';
            return `${timestamp} ${level}: ${prefix}${comp} ${message}`;
        })),
    }));
}
class StructuredLogger {
    constructor(context = {}) {
        this.context = context;
    }
    log(level, message, meta = {}) {
        logger.log(level, message, {
            ...this.context,
            ...meta,
        });
    }
    info(message, meta = {}) {
        this.log('info', message, meta);
    }
    error(message, error, meta = {}) {
        this.log('error', message, {
            ...meta,
            error: error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : undefined,
        });
    }
    warn(message, meta = {}) {
        this.log('warn', message, meta);
    }
    debug(message, meta = {}) {
        this.log('debug', message, meta);
    }
    // Create child logger with additional context
    child(additionalContext) {
        return new StructuredLogger({
            ...this.context,
            ...additionalContext,
        });
    }
    // Generate new correlation ID
    withCorrelationId(correlationId) {
        return new StructuredLogger({
            ...this.context,
            correlationId: correlationId || (0, uuid_1.v4)(),
        });
    }
    // Add component context
    withComponent(component) {
        return new StructuredLogger({
            ...this.context,
            component,
        });
    }
    // Get current correlation ID
    getCorrelationId() {
        return this.context.correlationId;
    }
}
exports.StructuredLogger = StructuredLogger;
// Default logger instance
exports.defaultLogger = new StructuredLogger({
    component: 'worker-manager',
});
exports.default = exports.defaultLogger;
//# sourceMappingURL=logger.js.map