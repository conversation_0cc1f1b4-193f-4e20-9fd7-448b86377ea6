interface Config {
    nodeEnv: string;
    port: number;
    supabase: {
        url: string;
        serviceKey: string;
    };
    redis: {
        url: string;
        password?: string;
    };
    database: {
        host: string;
        port: number;
        name: string;
        user: string;
        password?: string;
    };
    logging: {
        level: string;
        file: string;
    };
    workerManager: {
        healthCheckInterval: number;
        notificationChannel: string;
        rateLimit: {
            window: number;
            maxRequests: number;
        };
    };
    monitoring: {
        enabled: boolean;
        port: number;
    };
    shutdown: {
        timeout: number;
    };
}
export declare const appConfig: Config;
export default appConfig;
//# sourceMappingURL=config.d.ts.map