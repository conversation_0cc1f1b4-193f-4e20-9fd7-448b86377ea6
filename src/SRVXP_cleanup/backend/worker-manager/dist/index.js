"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const config_1 = __importDefault(require("./utils/config"));
const logger_1 = require("./utils/logger");
const supabase_client_1 = __importDefault(require("./database/supabase-client"));
const redis_client_1 = __importDefault(require("./queue/redis-client"));
const appointment_monitor_1 = __importDefault(require("./services/appointment-monitor"));
const health_check_1 = __importDefault(require("./services/health-check"));
const logger = new logger_1.StructuredLogger({ component: 'main' });
class WorkerManagerApp {
    constructor() {
        this.isShuttingDown = false;
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupErrorHandling();
        logger.info('Worker Manager application initialized', {
            version: process.env.npm_package_version || '1.0.0',
            nodeEnv: config_1.default.nodeEnv,
            port: config_1.default.port,
        });
    }
    setupMiddleware() {
        // Security middleware
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: false, // Disable CSP for API service
        }));
        // CORS configuration
        this.app.use((0, cors_1.default)({
            origin: config_1.default.nodeEnv === 'development' ? true : false,
            credentials: true,
        }));
        // Body parsing
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        // Request logging middleware
        this.app.use((req, res, next) => {
            const requestLogger = logger.withCorrelationId();
            req.correlationId = requestLogger.getCorrelationId();
            requestLogger.info('Incoming request', {
                method: req.method,
                url: req.url,
                userAgent: req.get('User-Agent'),
                ip: req.ip,
            });
            res.on('finish', () => {
                requestLogger.info('Request completed', {
                    method: req.method,
                    url: req.url,
                    statusCode: res.statusCode,
                    contentLength: res.get('Content-Length'),
                });
            });
            next();
        });
    }
    setupRoutes() {
        // Health check endpoints
        this.app.get('/health', async (req, res) => {
            try {
                const health = await health_check_1.default.getHealthStatus();
                const statusCode = health.status === 'healthy' ? 200 :
                    health.status === 'degraded' ? 200 : 503;
                res.status(statusCode).json(health);
            }
            catch (error) {
                logger.error('Health check endpoint error', error);
                res.status(503).json({
                    status: 'unhealthy',
                    error: 'Health check failed',
                    timestamp: new Date().toISOString(),
                });
            }
        });
        // Readiness probe
        this.app.get('/ready', async (req, res) => {
            try {
                const isReady = await health_check_1.default.isReady();
                res.status(isReady ? 200 : 503).json({
                    ready: isReady,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                res.status(503).json({
                    ready: false,
                    error: 'Readiness check failed',
                    timestamp: new Date().toISOString(),
                });
            }
        });
        // Liveness probe
        this.app.get('/live', async (req, res) => {
            try {
                const isAlive = await health_check_1.default.isAlive();
                res.status(isAlive ? 200 : 503).json({
                    alive: isAlive,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                res.status(503).json({
                    alive: false,
                    error: 'Liveness check failed',
                    timestamp: new Date().toISOString(),
                });
            }
        });
        // Stats endpoint
        this.app.get('/stats', (req, res) => {
            try {
                const stats = appointment_monitor_1.default.getStats();
                res.json({
                    ...stats,
                    timestamp: new Date().toISOString(),
                });
            }
            catch (error) {
                logger.error('Stats endpoint error', error);
                res.status(500).json({
                    error: 'Failed to get stats',
                    timestamp: new Date().toISOString(),
                });
            }
        });
        // Reset stats endpoint (development only)
        if (config_1.default.nodeEnv === 'development') {
            this.app.post('/stats/reset', (req, res) => {
                try {
                    appointment_monitor_1.default.resetStats();
                    res.json({
                        message: 'Stats reset successfully',
                        timestamp: new Date().toISOString(),
                    });
                }
                catch (error) {
                    logger.error('Stats reset endpoint error', error);
                    res.status(500).json({
                        error: 'Failed to reset stats',
                        timestamp: new Date().toISOString(),
                    });
                }
            });
        }
        // Root endpoint
        this.app.get('/', (req, res) => {
            res.json({
                service: 'Worker Manager',
                version: process.env.npm_package_version || '1.0.0',
                environment: config_1.default.nodeEnv,
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
            });
        });
        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json({
                error: 'Not Found',
                message: `Route ${req.method} ${req.originalUrl} not found`,
                timestamp: new Date().toISOString(),
            });
        });
    }
    setupErrorHandling() {
        // Global error handler
        this.app.use((error, req, res, next) => {
            const correlationId = req.correlationId;
            const requestLogger = logger.withCorrelationId(correlationId);
            requestLogger.error('Unhandled application error', error, {
                url: req.url,
                method: req.method,
                userAgent: req.get('User-Agent'),
                ip: req.ip,
            });
            if (res.headersSent) {
                return next(error);
            }
            res.status(500).json({
                error: 'Internal Server Error',
                message: config_1.default.nodeEnv === 'development' ? error.message : 'Something went wrong',
                correlationId,
                timestamp: new Date().toISOString(),
            });
        });
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception', error);
            if (!this.isShuttingDown) {
                this.gracefulShutdown('uncaughtException');
            }
        });
        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection', reason, { promise });
            if (!this.isShuttingDown) {
                this.gracefulShutdown('unhandledRejection');
            }
        });
    }
    async start() {
        try {
            logger.info('Starting Worker Manager application...');
            // Initialize connections
            await this.initializeServices();
            // Start HTTP server
            await this.startHttpServer();
            // Start monitoring services
            await this.startMonitoringServices();
            logger.info('Worker Manager application started successfully', {
                port: config_1.default.port,
                nodeEnv: config_1.default.nodeEnv,
            });
        }
        catch (error) {
            logger.error('Failed to start Worker Manager application', error);
            process.exit(1);
        }
    }
    async initializeServices() {
        logger.info('Initializing services...');
        // Connect to Supabase
        await supabase_client_1.default.connect();
        logger.info('Supabase connection established');
        // Connect to Redis
        await redis_client_1.default.connect();
        logger.info('Redis connection established');
        // Start appointment monitor
        await appointment_monitor_1.default.start();
        logger.info('Appointment monitor started');
        logger.info('All services initialized successfully');
    }
    async startHttpServer() {
        return new Promise((resolve, reject) => {
            this.server = this.app.listen(config_1.default.port, (error) => {
                if (error) {
                    reject(error);
                }
                else {
                    logger.info('HTTP server started', {
                        port: config_1.default.port,
                        host: 'localhost',
                    });
                    resolve();
                }
            });
        });
    }
    async startMonitoringServices() {
        logger.info('Starting monitoring services...');
        // Start periodic health checks
        await health_check_1.default.startPeriodicHealthChecks();
        logger.info('Health check service started');
        logger.info('All monitoring services started');
    }
    async gracefulShutdown(signal) {
        if (this.isShuttingDown) {
            logger.warn('Shutdown already in progress, ignoring signal', { signal });
            return;
        }
        this.isShuttingDown = true;
        logger.info('Graceful shutdown initiated', { signal });
        const shutdownTimeout = setTimeout(() => {
            logger.error('Graceful shutdown timeout exceeded, forcing exit');
            process.exit(1);
        }, config_1.default.shutdown.timeout);
        try {
            // Stop accepting new requests
            if (this.server) {
                await new Promise((resolve) => {
                    this.server.close(() => {
                        logger.info('HTTP server closed');
                        resolve();
                    });
                });
            }
            // Stop monitoring services
            health_check_1.default.stopPeriodicHealthChecks();
            logger.info('Health check service stopped');
            // Stop appointment monitor
            await appointment_monitor_1.default.stop();
            logger.info('Appointment monitor stopped');
            // Disconnect from Redis
            await redis_client_1.default.disconnect();
            logger.info('Redis connection closed');
            // Disconnect from Supabase
            await supabase_client_1.default.disconnect();
            logger.info('Supabase connection closed');
            clearTimeout(shutdownTimeout);
            logger.info('Graceful shutdown completed');
            process.exit(0);
        }
        catch (error) {
            clearTimeout(shutdownTimeout);
            logger.error('Error during graceful shutdown', error);
            process.exit(1);
        }
    }
    // Setup signal handlers
    setupSignalHandlers() {
        process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
        process.on('SIGUSR2', () => this.gracefulShutdown('SIGUSR2')); // nodemon restart
    }
}
// Initialize and start the application
async function main() {
    try {
        const app = new WorkerManagerApp();
        app.setupSignalHandlers();
        await app.start();
    }
    catch (error) {
        logger.error('Failed to start application', error);
        process.exit(1);
    }
}
// Start the application if this file is run directly
if (require.main === module) {
    main();
}
// For testing purposes
exports.default = WorkerManagerApp;
//# sourceMappingURL=index.js.map