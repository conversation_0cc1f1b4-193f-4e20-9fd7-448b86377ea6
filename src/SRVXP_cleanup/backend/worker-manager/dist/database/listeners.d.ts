import { EventEmitter } from 'events';
export interface NotificationPayload {
    operation: 'INSERT' | 'UPDATE' | 'DELETE';
    record: {
        id: string;
        status: string;
        priority: number;
        user_id: string;
        created_at: string;
    };
}
export declare class DatabaseListener extends EventEmitter {
    private client;
    private isConnected;
    private isListening;
    private reconnectAttempts;
    private maxReconnectAttempts;
    private reconnectDelay;
    constructor();
    connect(): Promise<void>;
    startListening(): Promise<void>;
    stopListening(): Promise<void>;
    disconnect(): Promise<void>;
    private handleNotification;
    private scheduleReconnect;
    private reconnect;
    healthCheck(): Promise<boolean>;
    get isHealthy(): boolean;
}
export declare const databaseListener: DatabaseListener;
export default databaseListener;
//# sourceMappingURL=listeners.d.ts.map