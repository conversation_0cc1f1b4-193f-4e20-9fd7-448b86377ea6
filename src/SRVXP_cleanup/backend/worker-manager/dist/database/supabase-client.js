"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.supabaseService = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const config_1 = __importDefault(require("../utils/config"));
const logger_1 = require("../utils/logger");
const logger = new logger_1.StructuredLogger({ component: 'supabase-client' });
class SupabaseService {
    constructor() {
        this.isConnected = false;
        this.client = (0, supabase_js_1.createClient)(config_1.default.supabase.url, config_1.default.supabase.serviceKey, {
            auth: {
                autoRefreshToken: false,
                persistSession: false,
            },
            db: {
                schema: 'public',
            },
        });
        logger.info('Supabase client initialized', {
            url: config_1.default.supabase.url.substring(0, 30) + '...',
        });
    }
    async connect() {
        try {
            // Test connection by making a simple query
            const { error } = await this.client
                .from('appointment_requests')
                .select('count')
                .limit(1);
            if (error) {
                throw error;
            }
            this.isConnected = true;
            logger.info('Successfully connected to Supabase');
        }
        catch (error) {
            this.isConnected = false;
            logger.error('Failed to connect to Supabase', error);
            throw error;
        }
    }
    async disconnect() {
        // Supabase client doesn't need explicit disconnection
        this.isConnected = false;
        logger.info('Disconnected from Supabase');
    }
    async healthCheck() {
        try {
            const { error } = await this.client
                .from('appointment_requests')
                .select('count')
                .limit(1);
            const healthy = !error;
            this.isConnected = healthy;
            if (!healthy) {
                logger.warn('Supabase health check failed', { error: error?.message });
            }
            return healthy;
        }
        catch (error) {
            logger.error('Supabase health check error', error);
            this.isConnected = false;
            return false;
        }
    }
    async getNewAppointmentRequests() {
        try {
            const { data, error } = await this.client
                .from('appointment_requests')
                .select('*')
                .eq('status', 'pending')
                .order('created_at', { ascending: true });
            if (error) {
                throw error;
            }
            logger.debug('Retrieved appointment requests', {
                count: data?.length || 0,
            });
            return data || [];
        }
        catch (error) {
            logger.error('Failed to retrieve appointment requests', error);
            throw error;
        }
    }
    async updateAppointmentStatus(id, status, correlationId) {
        const requestLogger = logger.withCorrelationId(correlationId);
        try {
            const { error } = await this.client
                .from('appointment_requests')
                .update({
                status,
                updated_at: new Date().toISOString(),
            })
                .eq('id', id);
            if (error) {
                throw error;
            }
            requestLogger.info('Updated appointment request status', {
                appointmentId: id,
                status,
            });
        }
        catch (error) {
            requestLogger.error('Failed to update appointment status', error, {
                appointmentId: id,
                status,
            });
            throw error;
        }
    }
    async markAppointmentAsProcessing(id, correlationId) {
        return this.updateAppointmentStatus(id, 'in_progress', correlationId);
    }
    async markAppointmentAsCompleted(id, correlationId) {
        return this.updateAppointmentStatus(id, 'completed', correlationId);
    }
    async markAppointmentAsFailed(id, correlationId) {
        return this.updateAppointmentStatus(id, 'cancelled', correlationId);
    }
    get isHealthy() {
        return this.isConnected;
    }
    get supabaseClient() {
        return this.client;
    }
}
// Export singleton instance
exports.supabaseService = new SupabaseService();
exports.default = exports.supabaseService;
//# sourceMappingURL=supabase-client.js.map