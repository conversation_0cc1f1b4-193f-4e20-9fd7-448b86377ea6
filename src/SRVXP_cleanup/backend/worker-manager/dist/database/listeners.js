"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseListener = exports.DatabaseListener = void 0;
const pg_1 = require("pg");
const config_1 = __importDefault(require("../utils/config"));
const logger_1 = require("../utils/logger");
const events_1 = require("events");
const logger = new logger_1.StructuredLogger({ component: 'database-listener' });
class DatabaseListener extends events_1.EventEmitter {
    constructor() {
        super();
        this.isConnected = false;
        this.isListening = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectDelay = 1000; // Start with 1 second
        this.client = new pg_1.Client({
            host: config_1.default.database.host,
            port: config_1.default.database.port,
            database: config_1.default.database.name,
            user: config_1.default.database.user,
            password: config_1.default.database.password,
            ssl: config_1.default.nodeEnv === 'production' ? { rejectUnauthorized: false } : false,
        });
        // Handle connection errors
        this.client.on('error', (err) => {
            logger.error('PostgreSQL client error', err);
            this.isConnected = false;
            this.scheduleReconnect();
        });
        // Handle notifications
        this.client.on('notification', (message) => {
            this.handleNotification(message);
        });
        logger.info('Database listener initialized');
    }
    async connect() {
        try {
            if (this.isConnected) {
                logger.warn('Already connected to database');
                return;
            }
            await this.client.connect();
            this.isConnected = true;
            this.reconnectAttempts = 0;
            logger.info('Connected to PostgreSQL database');
        }
        catch (error) {
            this.isConnected = false;
            logger.error('Failed to connect to PostgreSQL', error);
            throw error;
        }
    }
    async startListening() {
        try {
            if (!this.isConnected) {
                throw new Error('Database connection not established');
            }
            if (this.isListening) {
                logger.warn('Already listening for notifications');
                return;
            }
            // Listen to the notification channel
            await this.client.query(`LISTEN ${config_1.default.workerManager.notificationChannel}`);
            this.isListening = true;
            logger.info('Started listening for database notifications', {
                channel: config_1.default.workerManager.notificationChannel,
            });
        }
        catch (error) {
            this.isListening = false;
            logger.error('Failed to start listening for notifications', error);
            throw error;
        }
    }
    async stopListening() {
        try {
            if (!this.isListening) {
                return;
            }
            await this.client.query(`UNLISTEN ${config_1.default.workerManager.notificationChannel}`);
            this.isListening = false;
            logger.info('Stopped listening for database notifications');
        }
        catch (error) {
            logger.error('Failed to stop listening for notifications', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.isListening) {
                await this.stopListening();
            }
            if (this.isConnected) {
                await this.client.end();
                this.isConnected = false;
            }
            logger.info('Disconnected from PostgreSQL database');
        }
        catch (error) {
            logger.error('Error during database disconnection', error);
            throw error;
        }
    }
    handleNotification(message) {
        try {
            const { channel, payload } = message;
            logger.debug('Received database notification', {
                channel,
                payload: payload?.substring(0, 100) + '...',
            });
            if (channel !== config_1.default.workerManager.notificationChannel) {
                logger.warn('Received notification from unexpected channel', { channel });
                return;
            }
            // Handle simplified payload (just appointment ID from existing function)
            let appointmentId;
            try {
                // Try to parse as JSON first (in case it gets updated later)
                const parsed = JSON.parse(payload);
                appointmentId = parsed.id || parsed.record?.id || parsed;
            }
            catch (parseError) {
                // If not JSON, treat as plain text ID
                appointmentId = payload;
            }
            if (!appointmentId) {
                logger.error('No appointment ID found in notification payload', new Error('Invalid payload'), {
                    payload: payload?.substring(0, 200),
                });
                return;
            }
            logger.info('New appointment request detected', {
                appointmentId,
            });
            // Create a mock notification record for compatibility
            const notificationRecord = {
                id: appointmentId,
                user_id: 'unknown', // Will be fetched from database
                status: 'pending',
                priority: 5,
            };
            this.emit('newAppointmentRequest', notificationRecord);
            // Emit general notification event
            this.emit('notification', {
                channel,
                operation: 'INSERT',
                record: notificationRecord,
            });
        }
        catch (error) {
            logger.error('Error handling database notification', error, {
                message: JSON.stringify(message),
            });
        }
    }
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger.error('Max reconnection attempts reached, giving up', new Error(`Max reconnection attempts reached: ${this.reconnectAttempts}`));
            this.emit('error', new Error('Database connection lost and could not be restored'));
            return;
        }
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), 30000);
        this.reconnectAttempts++;
        logger.info('Scheduling database reconnection', {
            attempt: this.reconnectAttempts,
            delay,
        });
        setTimeout(async () => {
            try {
                await this.reconnect();
            }
            catch (error) {
                logger.error('Reconnection attempt failed', error);
                this.scheduleReconnect();
            }
        }, delay);
    }
    async reconnect() {
        logger.info('Attempting to reconnect to database');
        // Clean up existing connection
        try {
            await this.client.end();
        }
        catch (error) {
            // Ignore cleanup errors
        }
        // Create new client
        this.client = new pg_1.Client({
            host: config_1.default.database.host,
            port: config_1.default.database.port,
            database: config_1.default.database.name,
            user: config_1.default.database.user,
            password: config_1.default.database.password,
            ssl: config_1.default.nodeEnv === 'production' ? { rejectUnauthorized: false } : false,
        });
        // Re-attach event handlers
        this.client.on('error', (err) => {
            logger.error('PostgreSQL client error', err);
            this.isConnected = false;
            this.scheduleReconnect();
        });
        this.client.on('notification', (message) => {
            this.handleNotification(message);
        });
        // Reconnect and restart listening
        await this.connect();
        await this.startListening();
        logger.info('Successfully reconnected to database');
    }
    async healthCheck() {
        try {
            if (!this.isConnected) {
                return false;
            }
            await this.client.query('SELECT 1');
            return true;
        }
        catch (error) {
            logger.error('Database health check failed', error);
            return false;
        }
    }
    get isHealthy() {
        return this.isConnected && this.isListening;
    }
}
exports.DatabaseListener = DatabaseListener;
// Export singleton instance
exports.databaseListener = new DatabaseListener();
exports.default = exports.databaseListener;
//# sourceMappingURL=listeners.js.map