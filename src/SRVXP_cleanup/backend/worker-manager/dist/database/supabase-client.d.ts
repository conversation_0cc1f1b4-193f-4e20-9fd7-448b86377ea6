import { SupabaseClient } from '@supabase/supabase-js';
export interface AppointmentRequest {
    id: string;
    user_id: string;
    patient_id?: string;
    patient_first_name: string;
    patient_last_name: string;
    patient_health_card: string;
    patient_health_card_sequence: string;
    patient_date_of_birth: string;
    patient_postal_code: string;
    appointment_date?: string;
    appointment_time_preference: 'asap' | 'morning' | 'afternoon' | 'evening';
    search_radius?: string;
    status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
    created_at: string;
    updated_at: string;
    notification_email?: string;
    language_preference?: 'fr' | 'en';
}
export interface Database {
    public: {
        Tables: {
            appointment_requests: {
                Row: AppointmentRequest;
                Insert: Omit<AppointmentRequest, 'id' | 'created_at' | 'updated_at'>;
                Update: Partial<Omit<AppointmentRequest, 'id' | 'created_at'>>;
            };
            completed_appointments: {
                Row: any;
                Insert: any;
                Update: any;
            };
        };
    };
}
declare class SupabaseService {
    private client;
    private isConnected;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    healthCheck(): Promise<boolean>;
    getNewAppointmentRequests(): Promise<AppointmentRequest[]>;
    updateAppointmentStatus(id: string, status: AppointmentRequest['status'], correlationId?: string): Promise<void>;
    markAppointmentAsProcessing(id: string, correlationId?: string): Promise<void>;
    markAppointmentAsCompleted(id: string, correlationId?: string): Promise<void>;
    markAppointmentAsFailed(id: string, correlationId?: string): Promise<void>;
    get isHealthy(): boolean;
    get supabaseClient(): SupabaseClient<Database>;
}
export declare const supabaseService: SupabaseService;
export default supabaseService;
//# sourceMappingURL=supabase-client.d.ts.map