"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisService = void 0;
const redis_1 = require("redis");
const config_1 = __importDefault(require("../utils/config"));
const logger_1 = require("../utils/logger");
const logger = new logger_1.StructuredLogger({ component: 'redis-client' });
class RedisService {
    constructor() {
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        const redisConfig = {
            url: config_1.default.redis.url,
            password: config_1.default.redis.password,
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
        };
        this.client = (0, redis_1.createClient)({
            url: redisConfig.url,
            password: redisConfig.password,
        });
        // Handle connection events
        this.client.on('connect', () => {
            logger.info('Redis client connecting...');
        });
        this.client.on('ready', () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            logger.info('Redis client connected and ready');
        });
        this.client.on('error', (error) => {
            logger.error('Redis client error', error);
            this.isConnected = false;
        });
        this.client.on('end', () => {
            this.isConnected = false;
            logger.info('Redis client connection ended');
        });
        this.client.on('reconnecting', () => {
            this.reconnectAttempts++;
            logger.info('Redis client reconnecting...', {
                attempt: this.reconnectAttempts,
            });
        });
        logger.info('Redis client initialized', {
            url: config_1.default.redis.url.replace(/:[^:]*@/, ':***@'), // Hide password in logs
        });
    }
    async connect() {
        try {
            if (this.isConnected) {
                logger.warn('Redis client already connected');
                return;
            }
            await this.client.connect();
            logger.info('Successfully connected to Redis');
        }
        catch (error) {
            this.isConnected = false;
            logger.error('Failed to connect to Redis', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (!this.isConnected) {
                return;
            }
            await this.client.disconnect();
            this.isConnected = false;
            logger.info('Disconnected from Redis');
        }
        catch (error) {
            logger.error('Error during Redis disconnection', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            if (!this.isConnected) {
                return false;
            }
            const result = await this.client.ping();
            const healthy = result === 'PONG';
            if (!healthy) {
                logger.warn('Redis health check failed - unexpected ping response', {
                    response: result,
                });
            }
            return healthy;
        }
        catch (error) {
            logger.error('Redis health check error', error);
            return false;
        }
    }
    // Queue operations
    async pushToQueue(queueName, jobData, isRetry = false) {
        try {
            const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const job = {
                id: jobId,
                data: jobData,
                isRetry,
                createdAt: new Date().toISOString(),
                attempts: 0,
                maxAttempts: 3,
            };
            // FIFO scoring: Use timestamp for first-come-first-serve
            // Exception: Retry jobs get a lower score (higher priority) to skip the line
            let score;
            if (isRetry) {
                // Retry jobs get priority - use negative timestamp to put them at the front
                score = -Date.now();
            }
            else {
                // Regular jobs use positive timestamp for FIFO ordering
                score = Date.now();
            }
            await this.client.zAdd(queueName, {
                score,
                value: JSON.stringify(job),
            });
            logger.debug('Job added to queue', {
                queueName,
                jobId,
                isRetry,
                score,
            });
            return jobId;
        }
        catch (error) {
            logger.error('Failed to push job to queue', error, {
                queueName,
                isRetry,
            });
            throw error;
        }
    }
    async popFromQueue(queueName) {
        try {
            // Get job with lowest score (retry jobs have negative scores, so they come first)
            // Regular jobs have positive timestamps, so they follow FIFO order
            const result = await this.client.zPopMin(queueName);
            if (!result) {
                return null;
            }
            const job = JSON.parse(result.value);
            logger.debug('Job popped from queue', {
                queueName,
                jobId: job.id,
                isRetry: job.isRetry,
                score: result.score,
            });
            return job;
        }
        catch (error) {
            logger.error('Failed to pop job from queue', error, {
                queueName,
            });
            throw error;
        }
    }
    async getQueueLength(queueName) {
        try {
            const length = await this.client.zCard(queueName);
            return length;
        }
        catch (error) {
            logger.error('Failed to get queue length', error, {
                queueName,
            });
            throw error;
        }
    }
    async removeJobFromQueue(queueName, jobData) {
        try {
            const removed = await this.client.zRem(queueName, jobData);
            return removed > 0;
        }
        catch (error) {
            logger.error('Failed to remove job from queue', error, {
                queueName,
            });
            throw error;
        }
    }
    // Job deduplication
    async isJobDuplicate(deduplicationKey, ttlSeconds = 300) {
        try {
            const exists = await this.client.exists(deduplicationKey);
            return exists === 1;
        }
        catch (error) {
            logger.error('Failed to check job duplication', error, {
                deduplicationKey,
            });
            throw error;
        }
    }
    async markJobAsProcessed(deduplicationKey, ttlSeconds = 300) {
        try {
            await this.client.setEx(deduplicationKey, ttlSeconds, 'processed');
            logger.debug('Job marked as processed', {
                deduplicationKey,
                ttlSeconds,
            });
        }
        catch (error) {
            logger.error('Failed to mark job as processed', error, {
                deduplicationKey,
            });
            throw error;
        }
    }
    // Metrics and monitoring
    async incrementCounter(key, increment = 1) {
        try {
            const result = await this.client.incrBy(key, increment);
            return result;
        }
        catch (error) {
            logger.error('Failed to increment counter', error, { key });
            throw error;
        }
    }
    async setMetric(key, value, ttlSeconds) {
        try {
            if (ttlSeconds) {
                await this.client.setEx(key, ttlSeconds, JSON.stringify(value));
            }
            else {
                await this.client.set(key, JSON.stringify(value));
            }
        }
        catch (error) {
            logger.error('Failed to set metric', error, { key });
            throw error;
        }
    }
    async getMetric(key) {
        try {
            const value = await this.client.get(key);
            return value ? JSON.parse(value) : null;
        }
        catch (error) {
            logger.error('Failed to get metric', error, { key });
            throw error;
        }
    }
    get isHealthy() {
        return this.isConnected;
    }
    get redisClient() {
        return this.client;
    }
}
// Export singleton instance
exports.redisService = new RedisService();
exports.default = exports.redisService;
//# sourceMappingURL=redis-client.js.map