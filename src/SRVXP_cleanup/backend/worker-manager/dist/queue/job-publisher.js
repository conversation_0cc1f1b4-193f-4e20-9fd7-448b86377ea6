"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.jobPublisher = exports.QUEUE_NAMES = void 0;
const uuid_1 = require("uuid");
const redis_client_1 = __importDefault(require("./redis-client"));
const logger_1 = require("../utils/logger");
const logger = new logger_1.StructuredLogger({ component: 'job-publisher' });
// Queue names
exports.QUEUE_NAMES = {
    APPOINTMENT_REQUESTS: 'appointment-requests',
    RETRY_QUEUE: 'retry-queue',
    DEAD_LETTER_QUEUE: 'dead-letter-queue'
};
class JobPublisher {
    constructor() {
        this.rateLimiters = new Map();
        logger.info('Job publisher initialized');
    }
    async publishAppointmentRequest(appointmentRequest, correlationId) {
        const requestCorrelationId = correlationId || (0, uuid_1.v4)();
        const requestLogger = logger.withCorrelationId(requestCorrelationId);
        try {
            // Check rate limiting
            if (await this.isRateLimited(appointmentRequest.user_id)) {
                throw new Error(`Rate limit exceeded for user ${appointmentRequest.user_id}`);
            }
            // Check for duplicate jobs
            const deduplicationKey = this.generateDeduplicationKey(appointmentRequest);
            if (await redis_client_1.default.isJobDuplicate(deduplicationKey)) {
                requestLogger.warn('Duplicate job detected, skipping', {
                    appointmentId: appointmentRequest.id,
                    deduplicationKey,
                });
                return 'duplicate';
            }
            // Create job data
            const jobData = {
                appointmentRequestId: appointmentRequest.id,
                patientInfo: {
                    firstName: appointmentRequest.patient_first_name,
                    lastName: appointmentRequest.patient_last_name,
                    healthCard: appointmentRequest.patient_health_card,
                    healthCardSequence: appointmentRequest.patient_health_card_sequence,
                    dateOfBirth: appointmentRequest.patient_date_of_birth,
                    postalCode: appointmentRequest.patient_postal_code,
                },
                searchCriteria: {
                    appointmentDate: appointmentRequest.appointment_date,
                    timePreference: appointmentRequest.appointment_time_preference,
                    searchRadius: parseInt(appointmentRequest.search_radius || '50', 10), // Convert to number, default to 50km
                },
                notificationPreferences: {
                    email: appointmentRequest.notification_email || '', // Will need to get from user table
                    language: appointmentRequest.language_preference || 'en', // Default to english
                },
                userId: appointmentRequest.user_id,
                familyMemberId: appointmentRequest.patient_id,
                correlationId: requestCorrelationId,
            };
            // Publish job to queue
            const jobId = await redis_client_1.default.pushToQueue(exports.QUEUE_NAMES.APPOINTMENT_REQUESTS, jobData, false // Not a retry job
            );
            // Mark as processed in deduplication cache
            await redis_client_1.default.markJobAsProcessed(deduplicationKey, 300); // 5 minutes TTL
            // Update rate limiter
            await this.updateRateLimit(appointmentRequest.user_id);
            // Update metrics
            await this.updateMetrics('appointment_request_published', jobId);
            requestLogger.info('Appointment request job published successfully', {
                appointmentId: appointmentRequest.id,
                jobId,
                queueName: exports.QUEUE_NAMES.APPOINTMENT_REQUESTS,
            });
            return jobId;
        }
        catch (error) {
            requestLogger.error('Failed to publish appointment request job', error, {
                appointmentId: appointmentRequest.id,
            });
            throw error;
        }
    }
    async publishRetryJob(originalJob, retryReason, correlationId) {
        const requestCorrelationId = correlationId || (0, uuid_1.v4)();
        const requestLogger = logger.withCorrelationId(requestCorrelationId);
        try {
            if (originalJob.attempts >= originalJob.maxAttempts) {
                // Move to dead letter queue
                return await this.publishToDeadLetterQueue(originalJob, retryReason, requestCorrelationId);
            }
            const retryJob = {
                ...originalJob,
                id: (0, uuid_1.v4)(),
                isRetry: true,
                attempts: originalJob.attempts + 1,
                scheduledFor: this.calculateRetryDelay(originalJob.attempts),
            };
            const jobId = await redis_client_1.default.pushToQueue(exports.QUEUE_NAMES.RETRY_QUEUE, retryJob.data, true // This is a retry job
            );
            await this.updateMetrics('job_retried', jobId);
            requestLogger.info('Job scheduled for retry', {
                originalJobId: originalJob.id,
                retryJobId: jobId,
                attempt: retryJob.attempts,
                maxAttempts: retryJob.maxAttempts,
                retryReason,
                scheduledFor: retryJob.scheduledFor,
            });
            return jobId;
        }
        catch (error) {
            requestLogger.error('Failed to publish retry job', error, {
                originalJobId: originalJob.id,
                retryReason,
            });
            throw error;
        }
    }
    async publishToDeadLetterQueue(job, reason, correlationId) {
        const requestLogger = logger.withCorrelationId(correlationId);
        try {
            const deadJob = {
                ...job,
                id: (0, uuid_1.v4)(),
                failedAt: new Date().toISOString(),
                failureReason: reason,
            };
            const jobId = await redis_client_1.default.pushToQueue(exports.QUEUE_NAMES.DEAD_LETTER_QUEUE, deadJob, false // Dead letter jobs don't skip the line
            );
            await this.updateMetrics('job_dead_lettered', jobId);
            requestLogger.error('Job moved to dead letter queue', new Error(reason), {
                originalJobId: job.id,
                deadJobId: jobId,
                attempts: job.attempts,
                reason,
            });
            return jobId;
        }
        catch (error) {
            requestLogger.error('Failed to publish to dead letter queue', error, {
                originalJobId: job.id,
                reason,
            });
            throw error;
        }
    }
    calculateRetryDelay(attempts) {
        // Exponential backoff: 2^attempts minutes, max 60 minutes
        const delayMinutes = Math.min(Math.pow(2, attempts), 60);
        const retryTime = new Date(Date.now() + delayMinutes * 60 * 1000);
        return retryTime.toISOString();
    }
    async isRateLimited(userId) {
        const now = Date.now();
        const windowMs = 60000; // 1 minute window
        const maxRequests = 10; // Max 10 requests per minute per user
        const userLimit = this.rateLimiters.get(userId);
        if (!userLimit || now > userLimit.resetTime) {
            // Reset or initialize rate limiter
            this.rateLimiters.set(userId, {
                count: 0,
                resetTime: now + windowMs,
            });
            return false;
        }
        return userLimit.count >= maxRequests;
    }
    async updateRateLimit(userId) {
        const userLimit = this.rateLimiters.get(userId);
        if (userLimit) {
            userLimit.count += 1;
        }
    }
    async updateMetrics(eventType, jobId) {
        try {
            const metricsKey = `metrics:${eventType}`;
            const dailyKey = `metrics:daily:${eventType}:${new Date().toISOString().split('T')[0]}`;
            await Promise.all([
                redis_client_1.default.incrementCounter(metricsKey),
                redis_client_1.default.incrementCounter(dailyKey),
                redis_client_1.default.setMetric(`job:${jobId}:metrics`, {
                    eventType,
                    timestamp: new Date().toISOString(),
                }, 3600), // 1 hour TTL
            ]);
        }
        catch (error) {
            logger.error('Failed to update metrics', error, {
                eventType,
                jobId,
            });
            // Don't throw - metrics shouldn't break the main flow
        }
    }
    async getQueueStats() {
        try {
            const [appointmentRequests, retryQueue, deadLetterQueue] = await Promise.all([
                redis_client_1.default.getQueueLength(exports.QUEUE_NAMES.APPOINTMENT_REQUESTS),
                redis_client_1.default.getQueueLength(exports.QUEUE_NAMES.RETRY_QUEUE),
                redis_client_1.default.getQueueLength(exports.QUEUE_NAMES.DEAD_LETTER_QUEUE),
            ]);
            return {
                appointmentRequests,
                retryQueue,
                deadLetterQueue,
            };
        }
        catch (error) {
            logger.error('Failed to get queue stats', error);
            throw error;
        }
    }
    generateDeduplicationKey(appointmentRequest) {
        // Create a unique key based on user and request details to prevent duplicates
        const keyParts = [
            appointmentRequest.user_id,
            appointmentRequest.patient_first_name,
            appointmentRequest.patient_last_name,
            appointmentRequest.patient_health_card,
            appointmentRequest.patient_date_of_birth,
            appointmentRequest.appointment_time_preference,
        ];
        const baseKey = keyParts.join(':').toLowerCase().replace(/\s+/g, '');
        return `dedup:appointment:${Buffer.from(baseKey).toString('base64')}`;
    }
}
// Export singleton instance
exports.jobPublisher = new JobPublisher();
exports.default = exports.jobPublisher;
//# sourceMappingURL=job-publisher.js.map