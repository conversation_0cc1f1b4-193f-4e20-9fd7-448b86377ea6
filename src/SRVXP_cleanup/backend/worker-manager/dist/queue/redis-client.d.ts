import { RedisClientType } from 'redis';
export interface RedisConfig {
    url: string;
    password?: string;
    retryDelayOnFailover: number;
    maxRetriesPerRequest: number;
    lazyConnect: boolean;
}
declare class RedisService {
    private client;
    private isConnected;
    private reconnectAttempts;
    private maxReconnectAttempts;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    healthCheck(): Promise<boolean>;
    pushToQueue(queueName: string, jobData: any, isRetry?: boolean): Promise<string>;
    popFromQueue(queueName: string): Promise<any | null>;
    getQueueLength(queueName: string): Promise<number>;
    removeJobFromQueue(queueName: string, jobData: string): Promise<boolean>;
    isJobDuplicate(deduplicationKey: string, ttlSeconds?: number): Promise<boolean>;
    markJobAsProcessed(deduplicationKey: string, ttlSeconds?: number): Promise<void>;
    incrementCounter(key: string, increment?: number): Promise<number>;
    setMetric(key: string, value: any, ttlSeconds?: number): Promise<void>;
    getMetric(key: string): Promise<any>;
    get isHealthy(): boolean;
    get redisClient(): RedisClientType;
}
export declare const redisService: RedisService;
export default redisService;
//# sourceMappingURL=redis-client.d.ts.map