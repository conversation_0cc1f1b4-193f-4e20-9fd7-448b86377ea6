import { AppointmentRequest } from '../database/supabase-client';
export interface JobData {
    appointmentRequestId: string;
    patientInfo: {
        firstName: string;
        lastName: string;
        healthCard: string;
        healthCardSequence: string;
        dateOfBirth: string;
        postalCode: string;
    };
    searchCriteria: {
        appointmentDate?: string;
        timePreference: 'asap' | 'morning' | 'afternoon' | 'evening';
        searchRadius: number;
    };
    notificationPreferences: {
        email: string;
        language: 'fr' | 'en';
    };
    userId: string;
    familyMemberId?: string;
    correlationId: string;
}
export interface QueuedJob {
    id: string;
    type: 'appointment-request';
    isRetry: boolean;
    data: JobData;
    attempts: number;
    maxAttempts: number;
    createdAt: string;
    scheduledFor?: string;
}
export declare const QUEUE_NAMES: {
    readonly APPOINTMENT_REQUESTS: "appointment-requests";
    readonly RETRY_QUEUE: "retry-queue";
    readonly DEAD_LETTER_QUEUE: "dead-letter-queue";
};
declare class JobPublisher {
    private rateLimiters;
    constructor();
    publishAppointmentRequest(appointmentRequest: AppointmentRequest, correlationId?: string): Promise<string>;
    publishRetryJob(originalJob: QueuedJob, retryReason: string, correlationId?: string): Promise<string>;
    private publishToDeadLetterQueue;
    private calculateRetryDelay;
    private isRateLimited;
    private updateRateLimit;
    private updateMetrics;
    getQueueStats(): Promise<{
        appointmentRequests: number;
        retryQueue: number;
        deadLetterQueue: number;
    }>;
    private generateDeduplicationKey;
}
export declare const jobPublisher: JobPublisher;
export default jobPublisher;
//# sourceMappingURL=job-publisher.d.ts.map