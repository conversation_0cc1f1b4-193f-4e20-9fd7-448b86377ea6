# Worker Manager Service

The Worker Manager is a core component of the Sans Rendez-Vous Express appointment booking system. It monitors Supabase for new appointment requests and queues them for processing by Puppeteer workers.

## Architecture Overview

```
[Supabase DB] → [PostgreSQL LISTEN/NOTIFY] → [Worker Manager] → [Redis Queue] → [Puppeteer Workers]
```

## Features

- **Real-time Monitoring**: Uses PostgreSQL LISTEN/NOTIFY for instant detection of new appointment requests
- **Fallback Polling**: Ensures no requests are missed with periodic database polling
- **Job Deduplication**: Prevents duplicate jobs using Redis-based deduplication
- **Rate Limiting**: Protects against spam and abuse with user-based rate limiting
- **Priority Queue**: Handles urgent appointments first using intelligent prioritization
- **Health Monitoring**: Comprehensive health checks and metrics collection
- **Graceful Shutdown**: Handles termination signals properly for safe shutdowns

## Getting Started

### Prerequisites

- Node.js 18+
- Redis 7+
- PostgreSQL (via Supabase)
- Docker (optional)

### Environment Variables

Copy `env.example` to `.env` and configure:

```bash
cp env.example .env
```

Required variables:
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_KEY`: Service role key for database access
- `DB_HOST`, `DB_PORT`, `DB_NAME`, `DB_USER`, `DB_PASSWORD`: PostgreSQL connection details
- `REDIS_URL`: Redis connection string

### Installation

```bash
# Install dependencies
npm install

# Build the project
npm run build

# Run in development
npm run dev

# Run in production
npm start
```

### Docker Deployment

```bash
# Build and start with Redis
docker-compose up -d

# Start with monitoring services
docker-compose --profile monitoring up -d

# Scale for high availability
docker-compose up -d --scale worker-manager=2
```

## API Endpoints

### Health Endpoints

#### `GET /health`
Comprehensive health check of all services.

**Response:**
```json
{
  "status": "healthy|degraded|unhealthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 12345,
  "version": "1.0.0",
  "services": {
    "supabase": {
      "status": "healthy",
      "lastCheck": "2024-01-01T00:00:00.000Z",
      "responseTime": 45
    },
    "redis": {
      "status": "healthy", 
      "lastCheck": "2024-01-01T00:00:00.000Z",
      "responseTime": 12
    }
  },
  "queues": {
    "appointmentRequests": 5,
    "retryQueue": 2,
    "deadLetterQueue": 0
  }
}
```

#### `GET /ready`
Kubernetes readiness probe endpoint.

**Response:**
```json
{
  "ready": true,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### `GET /live`
Kubernetes liveness probe endpoint.

**Response:**
```json
{
  "alive": true,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Monitoring Endpoints

#### `GET /stats`
Service statistics and metrics.

**Response:**
```json
{
  "totalNotifications": 1234,
  "processedAppointments": 1200,
  "failedProcessing": 12,
  "duplicateJobs": 20,
  "rateLimitedJobs": 2,
  "uptime": 123456,
  "startTime": "2024-01-01T00:00:00.000Z"
}
```

#### `POST /stats/reset` (Development Only)
Reset all statistics counters.

## Configuration

### Priority Levels

Jobs are prioritized based on appointment urgency:

- **Priority 1 (Urgent)**: ASAP appointments
- **Priority 3 (High)**: Today/tomorrow appointments  
- **Priority 5 (Normal)**: This week appointments
- **Priority 7 (Low)**: Next week+ appointments
- **Priority 9 (Retry)**: Retry jobs

### Rate Limiting

- **Window**: 60 seconds
- **Max Requests**: 10 per user per window
- **Scope**: Per user ID

### Deduplication

Duplicate jobs are detected using a composite key:
- User ID
- Patient name
- Health card number
- Appointment date/preference

TTL: 5 minutes

## Monitoring

### Queue Dashboard

Access the queue monitoring dashboard at `http://localhost:3001` when running with the monitoring profile.

### Redis Monitoring

RedisInsight is available at `http://localhost:8001` for Redis monitoring.

### Logs

Structured JSON logs are written to:
- Console (development)
- File: `./logs/worker-manager.log`
- Error file: `./logs/worker-manager.error.log`

Log levels: `error`, `warn`, `info`, `debug`

### Metrics

Key performance indicators tracked:
- Job processing rate
- Queue lengths
- Error rates  
- Response times
- Resource usage

## Database Schema Requirements

The service expects these Supabase tables:

### `appointment_requests`
```sql
CREATE TABLE appointment_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  family_member_id UUID,
  patient_first_name TEXT NOT NULL,
  patient_last_name TEXT NOT NULL,
  health_card_number TEXT NOT NULL,
  health_card_sequence_number TEXT NOT NULL,
  date_of_birth DATE NOT NULL,
  postal_code TEXT NOT NULL,
  appointment_date DATE,
  time_preference TEXT CHECK (time_preference IN ('asap', 'morning', 'afternoon', 'evening')),
  search_radius INTEGER DEFAULT 50,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'cancelled', 'failed')),
  priority INTEGER DEFAULT 5,
  notification_email TEXT NOT NULL,
  language_preference TEXT DEFAULT 'en' CHECK (language_preference IN ('fr', 'en')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Required Trigger

```sql
CREATE OR REPLACE FUNCTION notify_new_appointment()
RETURNS TRIGGER AS $$
BEGIN
  PERFORM pg_notify('new_appointment_requests', json_build_object(
    'operation', TG_OP,
    'record', json_build_object(
      'id', NEW.id,
      'user_id', NEW.user_id,
      'status', NEW.status,
      'priority', NEW.priority,
      'created_at', NEW.created_at
    )
  )::text);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER appointment_request_notify
  AFTER INSERT OR UPDATE ON appointment_requests
  FOR EACH ROW
  EXECUTE FUNCTION notify_new_appointment();
```

## Troubleshooting

### Service Won't Start

1. Check environment variables are set correctly
2. Verify Supabase and Redis connections
3. Ensure required database tables exist
4. Check port 3002 is available

### No Jobs Processing

1. Verify PostgreSQL LISTEN/NOTIFY is working
2. Check Redis queue contains jobs: `redis-cli ZCARD appointment-requests`
3. Confirm Puppeteer workers are running
4. Review error logs for failed connections

### High Memory Usage

1. Check Redis maxmemory setting
2. Monitor queue lengths for buildup
3. Verify log rotation is working
4. Check for memory leaks in application

### Performance Issues

1. Monitor Redis performance
2. Check database connection pool
3. Review job processing times
4. Scale horizontally if needed

## Development

### Running Tests

```bash
# Unit tests
npm test

# Watch mode
npm run test:watch

# Coverage
npm run test:coverage
```

### Code Quality

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Type checking
npm run build
```

### Debugging

Set environment variable for debug logging:
```bash
LOG_LEVEL=debug npm run dev
```

## Production Deployment

### Docker Swarm

```bash
# Initialize swarm
docker swarm init

# Deploy stack
docker stack deploy -c docker-compose.yml appointment-system
```

### Kubernetes

See `k8s/` directory for Kubernetes manifests.

### Environment Considerations

- Set `NODE_ENV=production`
- Use strong Redis password
- Configure log aggregation
- Set up monitoring alerts
- Enable container health checks

## Contributing

1. Fork the repository
2. Create a feature branch
3. Write tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the MIT License. 