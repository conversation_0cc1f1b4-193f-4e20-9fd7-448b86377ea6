require('dotenv').config();

// Simple test to see if the service can start
async function testService() {
  console.log('Testing Worker Manager service...');
  
  try {
    // Import and test core components
    const supabaseService = require('./dist/database/supabase-client').default;
    const redisService = require('./dist/queue/redis-client').default;
    
    console.log('✅ Modules loaded successfully');
    
    // Test Supabase connection
    console.log('Testing Supabase connection...');
    await supabaseService.connect();
    const isHealthy = await supabaseService.healthCheck();
    console.log(`✅ Supabase connection: ${isHealthy ? 'Healthy' : 'Unhealthy'}`);
    
    // Test Redis connection (will fail without <PERSON>is but we can check if module loads)
    console.log('Testing Redis connection...');
    try {
      await redisService.connect();
      const redisHealthy = await redisService.healthCheck();
      console.log(`✅ Redis connection: ${redisHealthy ? 'Healthy' : 'Unhealthy'}`);
    } catch (error) {
      console.log(`⚠️  Redis connection failed (expected if Redis not running): ${error.message}`);
    }
    
    // Test appointment request fetch
    console.log('Testing appointment request fetch...');
    const requests = await supabaseService.getNewAppointmentRequests();
    console.log(`✅ Found ${requests.length} pending appointment requests`);
    
    console.log('\n🎉 All tests completed successfully!');
    
    // Cleanup
    await supabaseService.disconnect();
    try {
      await redisService.disconnect();
    } catch (error) {
      // Ignore redis disconnect errors
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

testService(); 