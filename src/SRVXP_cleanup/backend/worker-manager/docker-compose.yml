version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: appointment-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - worker-network

  worker-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: appointment-worker-manager
    restart: unless-stopped
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - PORT=3002
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=info
      - HEALTH_CHECK_INTERVAL=30000
      - NOTIFICATION_CHANNEL=new_appointment_requests
      - RATE_LIMIT_WINDOW=60000
      - RATE_LIMIT_MAX_REQUESTS=100
      - METRICS_ENABLED=true
      - METRICS_PORT=3003
      - SHUTDOWN_TIMEOUT=10000
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config:ro
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 45s
    networks:
      - worker-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Optional: Redis monitoring with RedisInsight
  redis-insight:
    image: redislabs/redisinsight:latest
    container_name: appointment-redis-insight
    restart: unless-stopped
    ports:
      - "8001:8001"
    volumes:
      - redis_insight_data:/db
    depends_on:
      - redis
    networks:
      - worker-network
    profiles:
      - monitoring

  # Optional: Queue monitoring dashboard
  queue-dashboard:
    image: deadly0/bull-board
    container_name: appointment-queue-dashboard
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - worker-network
    profiles:
      - monitoring

volumes:
  redis_data:
    driver: local
  redis_insight_data:
    driver: local

networks:
  worker-network:
    driver: bridge
    name: appointment-worker-network 