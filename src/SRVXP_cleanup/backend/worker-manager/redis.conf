# Redis configuration for Worker Manager
# Based on Redis 7.0 defaults with worker-specific optimizations

# Network
port 6379
bind 0.0.0.0
protected-mode no

# General
timeout 300
tcp-keepalive 300
tcp-backlog 511

# Memory management
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Persistence
save 900 1
save 300 10
save 60 10000

rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Append only file
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Logging
loglevel notice
logfile ""

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency monitoring
latency-monitor-threshold 100

# Client output buffer limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Advanced config
hz 10
dynamic-hz yes

# Memory
maxmemory 256mb

# Security (basic)
rename-command FLUSHDB ""
rename-command <PERSON>USHALL ""
rename-command DEBUG ""
rename-command CONFIG "CONFIG_a1b2c3d4e5f6" 