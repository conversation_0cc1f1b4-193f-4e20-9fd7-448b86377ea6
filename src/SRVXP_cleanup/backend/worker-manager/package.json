{"name": "worker-manager", "version": "1.0.0", "description": "Worker Manager service for monitoring appointment requests and queuing jobs", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["appointment", "worker", "queue", "supabase", "redis"], "author": "SRVXP Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "redis": "^4.6.10", "winston": "^3.11.0", "dotenv": "^16.3.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "pg": "^8.11.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/pg": "^8.10.9", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}