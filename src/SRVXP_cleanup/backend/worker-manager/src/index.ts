import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import appConfig from './utils/config';
import { StructuredLogger } from './utils/logger';
import supabaseService from './database/supabase-client';
import redisService from './queue/redis-client';
import appointmentMonitor from './services/appointment-monitor';
import healthCheckService from './services/health-check';

const logger = new StructuredLogger({ component: 'main' });

class WorkerManagerApp {
  private app: express.Application;
  private server?: any;
  private isShuttingDown: boolean = false;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
    
    logger.info('Worker Manager application initialized', {
      version: process.env.npm_package_version || '1.0.0',
      nodeEnv: appConfig.nodeEnv,
      port: appConfig.port,
    });
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: false, // Disable CSP for API service
    }));

    // CORS configuration
    this.app.use(cors({
      origin: appConfig.nodeEnv === 'development' ? true : false,
      credentials: true,
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging middleware
    this.app.use((req, res, next) => {
      const requestLogger = logger.withCorrelationId();
      req.correlationId = requestLogger.getCorrelationId();
      
      requestLogger.info('Incoming request', {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
      });

      res.on('finish', () => {
        requestLogger.info('Request completed', {
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          contentLength: res.get('Content-Length'),
        });
      });

      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoints
    this.app.get('/health', async (req, res) => {
      try {
        const health = await healthCheckService.getHealthStatus();
        const statusCode = health.status === 'healthy' ? 200 : 
                          health.status === 'degraded' ? 200 : 503;
        
        res.status(statusCode).json(health);
      } catch (error) {
        logger.error('Health check endpoint error', error as Error);
        res.status(503).json({
          status: 'unhealthy',
          error: 'Health check failed',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Readiness probe
    this.app.get('/ready', async (req, res) => {
      try {
        const isReady = await healthCheckService.isReady();
        res.status(isReady ? 200 : 503).json({
          ready: isReady,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(503).json({
          ready: false,
          error: 'Readiness check failed',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Liveness probe
    this.app.get('/live', async (req, res) => {
      try {
        const isAlive = await healthCheckService.isAlive();
        res.status(isAlive ? 200 : 503).json({
          alive: isAlive,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        res.status(503).json({
          alive: false,
          error: 'Liveness check failed',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Stats endpoint
    this.app.get('/stats', (req, res) => {
      try {
        const stats = appointmentMonitor.getStats();
        res.json({
          ...stats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        logger.error('Stats endpoint error', error as Error);
        res.status(500).json({
          error: 'Failed to get stats',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // Reset stats endpoint (development only)
    if (appConfig.nodeEnv === 'development') {
      this.app.post('/stats/reset', (req, res) => {
        try {
          appointmentMonitor.resetStats();
          res.json({
            message: 'Stats reset successfully',
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          logger.error('Stats reset endpoint error', error as Error);
          res.status(500).json({
            error: 'Failed to reset stats',
            timestamp: new Date().toISOString(),
          });
        }
      });
    }

    // Root endpoint
    this.app.get('/', (req, res) => {
      res.json({
        service: 'Worker Manager',
        version: process.env.npm_package_version || '1.0.0',
        environment: appConfig.nodeEnv,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.originalUrl} not found`,
        timestamp: new Date().toISOString(),
      });
    });
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use((error: any, req: any, res: any, next: any) => {
      const correlationId = req.correlationId;
      const requestLogger = logger.withCorrelationId(correlationId);

      requestLogger.error('Unhandled application error', error, {
        url: req.url,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
      });

      if (res.headersSent) {
        return next(error);
      }

      res.status(500).json({
        error: 'Internal Server Error',
        message: appConfig.nodeEnv === 'development' ? error.message : 'Something went wrong',
        correlationId,
        timestamp: new Date().toISOString(),
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', error);
      
      if (!this.isShuttingDown) {
        this.gracefulShutdown('uncaughtException');
      }
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection', reason as Error, { promise });
      
      if (!this.isShuttingDown) {
        this.gracefulShutdown('unhandledRejection');
      }
    });
  }

  async start(): Promise<void> {
    try {
      logger.info('Starting Worker Manager application...');

      // Initialize connections
      await this.initializeServices();

      // Start HTTP server
      await this.startHttpServer();

      // Start monitoring services
      await this.startMonitoringServices();

      logger.info('Worker Manager application started successfully', {
        port: appConfig.port,
        nodeEnv: appConfig.nodeEnv,
      });

    } catch (error) {
      logger.error('Failed to start Worker Manager application', error as Error);
      process.exit(1);
    }
  }

  private async initializeServices(): Promise<void> {
    logger.info('Initializing services...');

    // Connect to Supabase
    await supabaseService.connect();
    logger.info('Supabase connection established');

    // Connect to Redis
    await redisService.connect();
    logger.info('Redis connection established');

    // Start appointment monitor
    await appointmentMonitor.start();
    logger.info('Appointment monitor started');

    logger.info('All services initialized successfully');
  }

  private async startHttpServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(appConfig.port, (error?: any) => {
        if (error) {
          reject(error);
        } else {
          logger.info('HTTP server started', {
            port: appConfig.port,
            host: 'localhost',
          });
          resolve();
        }
      });
    });
  }

  private async startMonitoringServices(): Promise<void> {
    logger.info('Starting monitoring services...');

    // Start periodic health checks
    await healthCheckService.startPeriodicHealthChecks();
    logger.info('Health check service started');

    logger.info('All monitoring services started');
  }

  private async gracefulShutdown(signal: string): Promise<void> {
    if (this.isShuttingDown) {
      logger.warn('Shutdown already in progress, ignoring signal', { signal });
      return;
    }

    this.isShuttingDown = true;
    logger.info('Graceful shutdown initiated', { signal });

    const shutdownTimeout = setTimeout(() => {
      logger.error('Graceful shutdown timeout exceeded, forcing exit');
      process.exit(1);
    }, appConfig.shutdown.timeout);

    try {
      // Stop accepting new requests
      if (this.server) {
        await new Promise<void>((resolve) => {
          this.server.close(() => {
            logger.info('HTTP server closed');
            resolve();
          });
        });
      }

      // Stop monitoring services
      healthCheckService.stopPeriodicHealthChecks();
      logger.info('Health check service stopped');

      // Stop appointment monitor
      await appointmentMonitor.stop();
      logger.info('Appointment monitor stopped');

      // Disconnect from Redis
      await redisService.disconnect();
      logger.info('Redis connection closed');

      // Disconnect from Supabase
      await supabaseService.disconnect();
      logger.info('Supabase connection closed');

      clearTimeout(shutdownTimeout);
      logger.info('Graceful shutdown completed');
      process.exit(0);

    } catch (error) {
      clearTimeout(shutdownTimeout);
      logger.error('Error during graceful shutdown', error as Error);
      process.exit(1);
    }
  }

  // Setup signal handlers
  setupSignalHandlers(): void {
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
    process.on('SIGUSR2', () => this.gracefulShutdown('SIGUSR2')); // nodemon restart
  }
}

// Initialize and start the application
async function main(): Promise<void> {
  try {
    const app = new WorkerManagerApp();
    app.setupSignalHandlers();
    await app.start();
  } catch (error) {
    logger.error('Failed to start application', error as Error);
    process.exit(1);
  }
}

// Start the application if this file is run directly
if (require.main === module) {
  main();
}

// For testing purposes
export default WorkerManagerApp;

// Add correlation ID to Express request type
declare global {
  namespace Express {
    interface Request {
      correlationId?: string;
    }
  }
} 