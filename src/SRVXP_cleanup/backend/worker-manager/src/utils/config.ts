import { config } from 'dotenv';

// Load environment variables from .env file
config();

interface Config {
  // Environment
  nodeEnv: string;
  port: number;
  
  // Supabase
  supabase: {
    url: string;
    serviceKey: string;
  };
  
  // Redis
  redis: {
    url: string;
    password?: string;
  };
  
  // Database
  database: {
    host: string;
    port: number;
    name: string;
    user: string;
    password?: string;
  };
  
  // Logging
  logging: {
    level: string;
    file: string;
  };
  
  // Worker Manager
  workerManager: {
    healthCheckInterval: number;
    notificationChannel: string;
    rateLimit: {
      window: number;
      maxRequests: number;
    };
  };
  
  // Monitoring
  monitoring: {
    enabled: boolean;
    port: number;
  };
  
  // Shutdown
  shutdown: {
    timeout: number;
  };
}

function validateRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    throw new Error(`Required environment variable ${name} is not set`);
  }
  return value;
}

function getEnvVarAsNumber(name: string, defaultValue: number): number {
  const value = process.env[name];
  if (!value) return defaultValue;
  
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${name} must be a valid number`);
  }
  return parsed;
}

function getEnvVarAsBoolean(name: string, defaultValue: boolean): boolean {
  const value = process.env[name];
  if (!value) return defaultValue;
  
  return value.toLowerCase() === 'true';
}

export const appConfig: Config = {
  nodeEnv: process.env.NODE_ENV || 'development',
  port: getEnvVarAsNumber('PORT', 3002),
  
  supabase: {
    url: validateRequiredEnvVar('SUPABASE_URL'),
    serviceKey: validateRequiredEnvVar('SUPABASE_SERVICE_KEY'),
  },
  
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    password: process.env.REDIS_PASSWORD,
  },
  
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: getEnvVarAsNumber('DB_PORT', 5432),
    name: process.env.DB_NAME || 'postgres',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/worker-manager.log',
  },
  
  workerManager: {
    healthCheckInterval: getEnvVarAsNumber('HEALTH_CHECK_INTERVAL', 30000),
    notificationChannel: process.env.NOTIFICATION_CHANNEL || 'new_appointment',
    rateLimit: {
      window: getEnvVarAsNumber('RATE_LIMIT_WINDOW', 60000),
      maxRequests: getEnvVarAsNumber('RATE_LIMIT_MAX_REQUESTS', 100),
    },
  },
  
  monitoring: {
    enabled: getEnvVarAsBoolean('METRICS_ENABLED', true),
    port: getEnvVarAsNumber('METRICS_PORT', 3003),
  },
  
  shutdown: {
    timeout: getEnvVarAsNumber('SHUTDOWN_TIMEOUT', 10000),
  },
};

export default appConfig; 