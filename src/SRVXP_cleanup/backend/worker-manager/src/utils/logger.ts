import winston from 'winston';
import { v4 as uuidv4 } from 'uuid';
import appConfig from './config';

// Create logs directory if it doesn't exist
import { mkdirSync } from 'fs';
import { dirname } from 'path';

const logDir = dirname(appConfig.logging.file);
try {
  mkdirSync(logDir, { recursive: true });
} catch (err) {
  // Directory might already exist
}

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, correlationId, component, ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      message,
      correlationId,
      component,
      ...meta,
    };
    return JSON.stringify(logEntry);
  })
);

// Create Winston logger
const logger = winston.createLogger({
  level: appConfig.logging.level,
  format: logFormat,
  defaultMeta: {
    service: 'worker-manager',
    version: process.env.npm_package_version || '1.0.0',
  },
  transports: [
    // File transport
    new winston.transports.File({
      filename: appConfig.logging.file,
      handleExceptions: true,
      handleRejections: true,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    
    // Error file transport
    new winston.transports.File({
      filename: appConfig.logging.file.replace('.log', '.error.log'),
      level: 'error',
      handleExceptions: true,
      handleRejections: true,
      maxsize: 5242880, // 5MB
      maxFiles: 3,
    }),
  ],
});

// Add console transport in development
if (appConfig.nodeEnv === 'development') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, correlationId, component }) => {
        const prefix = correlationId ? `[${correlationId}]` : '';
        const comp = component ? `[${component}]` : '';
        return `${timestamp} ${level}: ${prefix}${comp} ${message}`;
      })
    ),
  }));
}

// Logger interface for consistent usage
export interface LoggerContext {
  correlationId?: string;
  component?: string;
  userId?: string;
  appointmentId?: string;
  jobId?: string;
}

export class StructuredLogger {
  private context: LoggerContext;

  constructor(context: LoggerContext = {}) {
    this.context = context;
  }

  private log(level: string, message: string, meta: any = {}) {
    logger.log(level, message, {
      ...this.context,
      ...meta,
    });
  }

  info(message: string, meta: any = {}) {
    this.log('info', message, meta);
  }

  error(message: string, error?: Error, meta: any = {}) {
    this.log('error', message, {
      ...meta,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : undefined,
    });
  }

  warn(message: string, meta: any = {}) {
    this.log('warn', message, meta);
  }

  debug(message: string, meta: any = {}) {
    this.log('debug', message, meta);
  }

  // Create child logger with additional context
  child(additionalContext: LoggerContext): StructuredLogger {
    return new StructuredLogger({
      ...this.context,
      ...additionalContext,
    });
  }

  // Generate new correlation ID
  withCorrelationId(correlationId?: string): StructuredLogger {
    return new StructuredLogger({
      ...this.context,
      correlationId: correlationId || uuidv4(),
    });
  }

  // Add component context
  withComponent(component: string): StructuredLogger {
    return new StructuredLogger({
      ...this.context,
      component,
    });
  }

  // Get current correlation ID
  getCorrelationId(): string | undefined {
    return this.context.correlationId;
  }
}

// Default logger instance
export const defaultLogger = new StructuredLogger({
  component: 'worker-manager',
});

// Export raw winston logger for advanced usage
export { logger as winstonLogger };

export default defaultLogger; 