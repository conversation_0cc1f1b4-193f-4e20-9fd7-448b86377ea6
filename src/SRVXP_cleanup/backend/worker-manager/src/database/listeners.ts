import { Client } from 'pg';
import appConfig from '../utils/config';
import { StructuredLogger } from '../utils/logger';
import { EventEmitter } from 'events';

const logger = new StructuredLogger({ component: 'database-listener' });

export interface NotificationPayload {
  operation: 'INSERT' | 'UPDATE' | 'DELETE';
  record: {
    id: string;
    status: string;
    priority: number;
    user_id: string;
    created_at: string;
  };
}

export class DatabaseListener extends EventEmitter {
  private client: Client;
  private isConnected: boolean = false;
  private isListening: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 10;
  private reconnectDelay: number = 1000; // Start with 1 second

  constructor() {
    super();
    
    this.client = new Client({
      host: appConfig.database.host,
      port: appConfig.database.port,
      database: appConfig.database.name,
      user: appConfig.database.user,
      password: appConfig.database.password,
      ssl: appConfig.nodeEnv === 'production' ? { rejectUnauthorized: false } : false,
    });

    // Handle connection errors
    this.client.on('error', (err) => {
      logger.error('PostgreSQL client error', err);
      this.isConnected = false;
      this.scheduleReconnect();
    });

    // Handle notifications
    this.client.on('notification', (message) => {
      this.handleNotification(message);
    });

    logger.info('Database listener initialized');
  }

  async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        logger.warn('Already connected to database');
        return;
      }

      await this.client.connect();
      this.isConnected = true;
      this.reconnectAttempts = 0;
      
      logger.info('Connected to PostgreSQL database');
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect to PostgreSQL', error as Error);
      throw error;
    }
  }

  async startListening(): Promise<void> {
    try {
      if (!this.isConnected) {
        throw new Error('Database connection not established');
      }

      if (this.isListening) {
        logger.warn('Already listening for notifications');
        return;
      }

      // Listen to the notification channel
      await this.client.query(`LISTEN ${appConfig.workerManager.notificationChannel}`);
      this.isListening = true;

      logger.info('Started listening for database notifications', {
        channel: appConfig.workerManager.notificationChannel,
      });
    } catch (error) {
      this.isListening = false;
      logger.error('Failed to start listening for notifications', error as Error);
      throw error;
    }
  }

  async stopListening(): Promise<void> {
    try {
      if (!this.isListening) {
        return;
      }

      await this.client.query(`UNLISTEN ${appConfig.workerManager.notificationChannel}`);
      this.isListening = false;

      logger.info('Stopped listening for database notifications');
    } catch (error) {
      logger.error('Failed to stop listening for notifications', error as Error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.isListening) {
        await this.stopListening();
      }

      if (this.isConnected) {
        await this.client.end();
        this.isConnected = false;
      }

      logger.info('Disconnected from PostgreSQL database');
    } catch (error) {
      logger.error('Error during database disconnection', error as Error);
      throw error;
    }
  }

  private handleNotification(message: any): void {
    try {
      const { channel, payload } = message;
      
      logger.debug('Received database notification', {
        channel,
        payload: payload?.substring(0, 100) + '...',
      });

      if (channel !== appConfig.workerManager.notificationChannel) {
        logger.warn('Received notification from unexpected channel', { channel });
        return;
      }

      // Handle simplified payload (just appointment ID from existing function)
      let appointmentId: string;
      try {
        // Try to parse as JSON first (in case it gets updated later)
        const parsed = JSON.parse(payload);
        appointmentId = parsed.id || parsed.record?.id || parsed;
      } catch (parseError) {
        // If not JSON, treat as plain text ID
        appointmentId = payload;
      }

      if (!appointmentId) {
        logger.error('No appointment ID found in notification payload', new Error('Invalid payload'), {
          payload: payload?.substring(0, 200),
        });
        return;
      }

      logger.info('New appointment request detected', {
        appointmentId,
      });

      // Create a mock notification record for compatibility
      const notificationRecord = {
        id: appointmentId,
        user_id: 'unknown', // Will be fetched from database
        status: 'pending',
        priority: 5,
      };

      this.emit('newAppointmentRequest', notificationRecord);

      // Emit general notification event
      this.emit('notification', {
        channel,
        operation: 'INSERT',
        record: notificationRecord,
      });

    } catch (error) {
      logger.error('Error handling database notification', error as Error, {
        message: JSON.stringify(message),
      });
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached, giving up', new Error(`Max reconnection attempts reached: ${this.reconnectAttempts}`));
      this.emit('error', new Error('Database connection lost and could not be restored'));
      return;
    }

    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts), 30000);
    this.reconnectAttempts++;

    logger.info('Scheduling database reconnection', {
      attempt: this.reconnectAttempts,
      delay,
    });

    setTimeout(async () => {
      try {
        await this.reconnect();
      } catch (error) {
        logger.error('Reconnection attempt failed', error as Error);
        this.scheduleReconnect();
      }
    }, delay);
  }

  private async reconnect(): Promise<void> {
    logger.info('Attempting to reconnect to database');

    // Clean up existing connection
    try {
      await this.client.end();
    } catch (error) {
      // Ignore cleanup errors
    }

    // Create new client
    this.client = new Client({
      host: appConfig.database.host,
      port: appConfig.database.port,
      database: appConfig.database.name,
      user: appConfig.database.user,
      password: appConfig.database.password,
      ssl: appConfig.nodeEnv === 'production' ? { rejectUnauthorized: false } : false,
    });

    // Re-attach event handlers
    this.client.on('error', (err) => {
      logger.error('PostgreSQL client error', err);
      this.isConnected = false;
      this.scheduleReconnect();
    });

    this.client.on('notification', (message) => {
      this.handleNotification(message);
    });

    // Reconnect and restart listening
    await this.connect();
    await this.startListening();

    logger.info('Successfully reconnected to database');
  }

  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }

      await this.client.query('SELECT 1');
      return true;
    } catch (error) {
      logger.error('Database health check failed', error as Error);
      return false;
    }
  }

  get isHealthy(): boolean {
    return this.isConnected && this.isListening;
  }
}

// Export singleton instance
export const databaseListener = new DatabaseListener();
export default databaseListener; 