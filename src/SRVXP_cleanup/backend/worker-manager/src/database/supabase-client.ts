import { createClient, SupabaseClient } from '@supabase/supabase-js';
import appConfig from '../utils/config';
import { StructuredLogger } from '../utils/logger';

const logger = new StructuredLogger({ component: 'supabase-client' });

export interface AppointmentRequest {
  id: string;
  user_id: string;
  patient_id?: string;
  patient_first_name: string;
  patient_last_name: string;
  patient_health_card: string;
  patient_health_card_sequence: string;
  patient_date_of_birth: string;
  patient_postal_code: string;
  appointment_date?: string;
  appointment_time_preference: 'asap' | 'morning' | 'afternoon' | 'evening';
  search_radius?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  created_at: string;
  updated_at: string;
  notification_email?: string;
  language_preference?: 'fr' | 'en';
}

export interface Database {
  public: {
    Tables: {
      appointment_requests: {
        Row: AppointmentRequest;
        Insert: Omit<AppointmentRequest, 'id' | 'created_at' | 'updated_at'>;
        Update: Partial<Omit<AppointmentRequest, 'id' | 'created_at'>>;
      };
      completed_appointments: {
        Row: any;
        Insert: any;
        Update: any;
      };
    };
  };
}

class SupabaseService {
  private client: SupabaseClient<Database>;
  private isConnected: boolean = false;

  constructor() {
    this.client = createClient<Database>(
      appConfig.supabase.url,
      appConfig.supabase.serviceKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
        db: {
          schema: 'public',
        },
      }
    );

    logger.info('Supabase client initialized', {
      url: appConfig.supabase.url.substring(0, 30) + '...',
    });
  }

  async connect(): Promise<void> {
    try {
      // Test connection by making a simple query
      const { error } = await this.client
        .from('appointment_requests')
        .select('count')
        .limit(1);

      if (error) {
        throw error;
      }

      this.isConnected = true;
      logger.info('Successfully connected to Supabase');
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect to Supabase', error as Error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    // Supabase client doesn't need explicit disconnection
    this.isConnected = false;
    logger.info('Disconnected from Supabase');
  }

  async healthCheck(): Promise<boolean> {
    try {
      const { error } = await this.client
        .from('appointment_requests')
        .select('count')
        .limit(1);

      const healthy = !error;
      this.isConnected = healthy;

      if (!healthy) {
        logger.warn('Supabase health check failed', { error: error?.message });
      }

      return healthy;
    } catch (error) {
      logger.error('Supabase health check error', error as Error);
      this.isConnected = false;
      return false;
    }
  }

  async getNewAppointmentRequests(): Promise<AppointmentRequest[]> {
    try {
      const { data, error } = await this.client
        .from('appointment_requests')
        .select('*')
        .eq('status', 'pending')
        .order('created_at', { ascending: true });

      if (error) {
        throw error;
      }

      logger.debug('Retrieved appointment requests', {
        count: data?.length || 0,
      });

      return data || [];
    } catch (error) {
      logger.error('Failed to retrieve appointment requests', error as Error);
      throw error;
    }
  }

  async updateAppointmentStatus(
    id: string,
    status: AppointmentRequest['status'],
    correlationId?: string
  ): Promise<void> {
    const requestLogger = logger.withCorrelationId(correlationId);

    try {
      const { error } = await this.client
        .from('appointment_requests')
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);

      if (error) {
        throw error;
      }

      requestLogger.info('Updated appointment request status', {
        appointmentId: id,
        status,
      });
    } catch (error) {
      requestLogger.error('Failed to update appointment status', error as Error, {
        appointmentId: id,
        status,
      });
      throw error;
    }
  }

  async markAppointmentAsProcessing(id: string, correlationId?: string): Promise<void> {
    return this.updateAppointmentStatus(id, 'in_progress', correlationId);
  }

  async markAppointmentAsCompleted(id: string, correlationId?: string): Promise<void> {
    return this.updateAppointmentStatus(id, 'completed', correlationId);
  }

  async markAppointmentAsFailed(id: string, correlationId?: string): Promise<void> {
    return this.updateAppointmentStatus(id, 'cancelled', correlationId);
  }

  get isHealthy(): boolean {
    return this.isConnected;
  }

  get supabaseClient(): SupabaseClient<Database> {
    return this.client;
  }
}

// Export singleton instance
export const supabaseService = new SupabaseService();
export default supabaseService; 