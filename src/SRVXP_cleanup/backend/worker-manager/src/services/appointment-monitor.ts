import { EventEmitter } from 'events';
import supabaseService from '../database/supabase-client';
import jobPublisher from '../queue/job-publisher';
import { StructuredLogger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import type { RealtimeChannel } from '@supabase/supabase-js';

const logger = new StructuredLogger({ component: 'appointment-monitor' });

export interface MonitorStats {
  totalNotifications: number;
  processedAppointments: number;
  failedProcessing: number;
  duplicateJobs: number;
  rateLimitedJobs: number;
  uptime: number;
  startTime: Date;
}

class AppointmentMonitor extends EventEmitter {
  private isRunning: boolean = false;
  private stats: MonitorStats;
  private startTime: Date = new Date();
  private pollingInterval?: NodeJS.Timeout;
  private pollingIntervalMs: number = 30000; // 30 seconds fallback polling
  private realtimeChannel?: RealtimeChannel;

  constructor() {
    super();
    
    this.stats = {
      totalNotifications: 0,
      processedAppointments: 0,
      failedProcessing: 0,
      duplicateJobs: 0,
      rateLimitedJobs: 0,
      uptime: 0,
      startTime: this.startTime,
    };

    // Bind event handlers
    this.handleNewAppointmentRequest = this.handleNewAppointmentRequest.bind(this);
    this.handleRealtimeInsert = this.handleRealtimeInsert.bind(this);

    logger.info('Appointment monitor initialized');
  }

  async start(): Promise<void> {
    try {
      if (this.isRunning) {
        logger.warn('Appointment monitor already running');
        return;
      }

      logger.info('Starting appointment monitor...');

      // Set up Supabase Realtime subscription
      await this.setupRealtimeSubscription();

      // Start fallback polling (in case Realtime fails)
      this.startPolling();

      this.isRunning = true;
      this.startTime = new Date();
      
      logger.info('Appointment monitor started successfully with Supabase Realtime');
      this.emit('started');
    } catch (error) {
      logger.error('Failed to start appointment monitor', error as Error);
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      if (!this.isRunning) {
        logger.warn('Appointment monitor not running');
        return;
      }

      logger.info('Stopping appointment monitor...');

      // Unsubscribe from Realtime
      if (this.realtimeChannel) {
        await supabaseService.supabaseClient.removeChannel(this.realtimeChannel);
        this.realtimeChannel = undefined;
      }

      // Stop polling
      this.stopPolling();

      this.isRunning = false;
      
      logger.info('Appointment monitor stopped');
      this.emit('stopped');
    } catch (error) {
      logger.error('Error stopping appointment monitor', error as Error);
      throw error;
    }
  }

  private async setupRealtimeSubscription(): Promise<void> {
    try {
      // Create a channel for appointment_requests table
      this.realtimeChannel = supabaseService.supabaseClient
        .channel('appointment_requests_changes')
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'appointment_requests',
            filter: 'status=eq.pending'
          },
          this.handleRealtimeInsert
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            logger.info('Successfully subscribed to Supabase Realtime for appointment_requests');
          } else if (status === 'CHANNEL_ERROR') {
            logger.error('Failed to subscribe to Supabase Realtime', new Error('Channel error'));
          } else if (status === 'TIMED_OUT') {
            logger.error('Supabase Realtime subscription timed out', new Error('Subscription timeout'));
          }
        });

    } catch (error) {
      logger.error('Failed to setup Supabase Realtime subscription', error as Error);
      throw error;
    }
  }

  private handleRealtimeInsert(payload: any): void {
    const correlationId = uuidv4();
    const requestLogger = logger.withCorrelationId(correlationId);

    try {
      const newRecord = payload.new;
      
      requestLogger.info('Received Realtime notification for new appointment request', {
        appointmentId: newRecord.id,
        userId: newRecord.user_id,
        status: newRecord.status,
      });

      // Process the new appointment request
      this.handleNewAppointmentRequest(newRecord);

    } catch (error) {
      logger.error('Error handling Realtime insert notification', error as Error, {
        payload: JSON.stringify(payload),
      });
    }
  }

  private async handleNewAppointmentRequest(notificationRecord: any): Promise<void> {
    const correlationId = uuidv4();
    const requestLogger = logger.withCorrelationId(correlationId);

    try {
      this.stats.totalNotifications++;
      
      requestLogger.info('Processing new appointment request notification', {
        appointmentId: notificationRecord.id,
        userId: notificationRecord.user_id,
      });

      // Fetch full appointment request from Supabase
      const appointmentRequests = await supabaseService.getNewAppointmentRequests();
      const appointmentRequest = appointmentRequests.find(req => req.id === notificationRecord.id);

      if (!appointmentRequest) {
        requestLogger.warn('Appointment request not found or no longer pending', {
          appointmentId: notificationRecord.id,
        });
        return;
      }

      // Mark as processing in database
      await supabaseService.markAppointmentAsProcessing(appointmentRequest.id, correlationId);

      // Publish job to queue
      const jobId = await jobPublisher.publishAppointmentRequest(appointmentRequest, correlationId);

      if (jobId === 'duplicate') {
        this.stats.duplicateJobs++;
        requestLogger.info('Appointment request was duplicate, skipped', {
          appointmentId: appointmentRequest.id,
        });
        return;
      }

      this.stats.processedAppointments++;
      
      requestLogger.info('Appointment request processed successfully', {
        appointmentId: appointmentRequest.id,
        jobId,
      });

      this.emit('appointmentProcessed', {
        appointmentId: appointmentRequest.id,
        jobId,
        correlationId,
      });

    } catch (error) {
      this.stats.failedProcessing++;
      
      if (error instanceof Error && error.message.includes('Rate limit exceeded')) {
        this.stats.rateLimitedJobs++;
        requestLogger.warn('Job rejected due to rate limiting', {
          appointmentId: notificationRecord.id,
          userId: notificationRecord.user_id,
        });
      } else {
        requestLogger.error('Failed to process appointment request', error as Error, {
          appointmentId: notificationRecord.id,
        });
      }

      this.emit('processingError', {
        appointmentId: notificationRecord.id,
        error: error as Error,
        correlationId,
      });
    }
  }

  private startPolling(): void {
    this.pollingInterval = setInterval(async () => {
      try {
        await this.pollForPendingAppointments();
      } catch (error) {
        logger.error('Error during polling', error as Error);
      }
    }, this.pollingIntervalMs);

    logger.debug('Started fallback polling', {
      intervalMs: this.pollingIntervalMs,
    });
  }

  private stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = undefined;
      logger.debug('Stopped fallback polling');
    }
  }

  private async pollForPendingAppointments(): Promise<void> {
    try {
      const pendingAppointments = await supabaseService.getNewAppointmentRequests();
      
      if (pendingAppointments.length === 0) {
        return;
      }

      logger.debug('Found pending appointments during polling', {
        count: pendingAppointments.length,
      });

      // Process each pending appointment
      for (const appointment of pendingAppointments) {
        // Create a mock notification record for consistency
        const notificationRecord = {
          id: appointment.id,
          user_id: appointment.user_id,
          status: appointment.status,
        };

        await this.handleNewAppointmentRequest(notificationRecord);
      }
    } catch (error) {
      logger.error('Error polling for pending appointments', error as Error);
    }
  }

  async healthCheck(): Promise<{
    isRunning: boolean;
    databaseHealthy: boolean;
    stats: MonitorStats;
  }> {
    try {
      const databaseHealthy = await supabaseService.healthCheck();
      
      // Update uptime
      this.stats.uptime = Date.now() - this.startTime.getTime();

      return {
        isRunning: this.isRunning,
        databaseHealthy,
        stats: { ...this.stats },
      };
    } catch (error) {
      logger.error('Health check failed', error as Error);
      return {
        isRunning: false,
        databaseHealthy: false,
        stats: { ...this.stats },
      };
    }
  }

  getStats(): MonitorStats {
    return {
      ...this.stats,
      uptime: Date.now() - this.startTime.getTime(),
    };
  }

  resetStats(): void {
    this.stats = {
      totalNotifications: 0,
      processedAppointments: 0,
      failedProcessing: 0,
      duplicateJobs: 0,
      rateLimitedJobs: 0,
      uptime: 0,
      startTime: new Date(),
    };
    this.startTime = new Date();
    
    logger.info('Statistics reset');
  }

  get isHealthy(): boolean {
    return this.isRunning && supabaseService.isHealthy;
  }
}

// Export singleton instance
export const appointmentMonitor = new AppointmentMonitor();
export default appointmentMonitor; 