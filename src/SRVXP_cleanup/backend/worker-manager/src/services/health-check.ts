import { StructuredLogger } from '../utils/logger';
import supabaseService from '../database/supabase-client';
import redisService from '../queue/redis-client';
import appointmentMonitor from './appointment-monitor';
import jobPublisher from '../queue/job-publisher';

const logger = new StructuredLogger({ component: 'health-check' });

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  services: {
    supabase: ServiceHealth;
    redis: ServiceHealth;
    appointmentMonitor: ServiceHealth;
    jobPublisher: ServiceHealth;
  };
  queues: {
    appointmentRequests: number;
    retryQueue: number;
    deadLetterQueue: number;
  };
  stats: {
    totalNotifications: number;
    processedAppointments: number;
    failedProcessing: number;
    duplicateJobs: number;
    rateLimitedJobs: number;
  };
}

export interface ServiceHealth {
  status: 'healthy' | 'unhealthy';
  lastCheck: string;
  responseTime?: number;
  error?: string;
}

class HealthCheckService {
  private startTime: Date = new Date();
  private healthCheckInterval?: NodeJS.Timeout;
  private intervalMs: number = 30000; // 30 seconds
  private isRunning: boolean = false;

  constructor() {
    logger.info('Health check service initialized');
  }

  async startPeriodicHealthChecks(): Promise<void> {
    if (this.isRunning) {
      logger.warn('Health checks already running');
      return;
    }

    this.healthCheckInterval = setInterval(async () => {
      try {
        const health = await this.getHealthStatus();
        
        if (health.status === 'unhealthy') {
          logger.error('System health check failed', new Error('Unhealthy status'), {
            health,
          });
        } else if (health.status === 'degraded') {
          logger.warn('System health degraded', { health });
        } else {
          logger.debug('System health check passed');
        }
      } catch (error) {
        logger.error('Error during periodic health check', error as Error);
      }
    }, this.intervalMs);

    this.isRunning = true;
    logger.info('Started periodic health checks', {
      intervalMs: this.intervalMs,
    });
  }

  stopPeriodicHealthChecks(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
      this.isRunning = false;
      logger.info('Stopped periodic health checks');
    }
  }

  async getHealthStatus(): Promise<HealthStatus> {
    const startTime = Date.now();
    
    try {
      // Check all services in parallel
      const [
        supabaseHealth,
        redisHealth,
        appointmentMonitorHealth,
        queueStats,
        monitorStats,
      ] = await Promise.all([
        this.checkSupabaseHealth(),
        this.checkRedisHealth(),
        this.checkAppointmentMonitorHealth(),
        this.getQueueStats(),
        this.getMonitorStats(),
      ]);

      const jobPublisherHealth: ServiceHealth = {
        status: 'healthy',
        lastCheck: new Date().toISOString(),
      };

      // Determine overall status
      const services = {
        supabase: supabaseHealth,
        redis: redisHealth,
        appointmentMonitor: appointmentMonitorHealth,
        jobPublisher: jobPublisherHealth,
      };

      const unhealthyServices = Object.values(services).filter(s => s.status === 'unhealthy');
      const totalServices = Object.values(services).length;

      let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
      if (unhealthyServices.length === 0) {
        overallStatus = 'healthy';
      } else if (unhealthyServices.length < totalServices / 2) {
        overallStatus = 'degraded';
      } else {
        overallStatus = 'unhealthy';
      }

      const healthStatus: HealthStatus = {
        status: overallStatus,
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime.getTime(),
        version: process.env.npm_package_version || '1.0.0',
        services,
        queues: queueStats,
        stats: monitorStats,
      };

      const responseTime = Date.now() - startTime;
      logger.debug('Health check completed', {
        status: overallStatus,
        responseTime,
        unhealthyServices: unhealthyServices.length,
      });

      return healthStatus;
    } catch (error) {
      logger.error('Error getting health status', error as Error);
      
      return {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime.getTime(),
        version: process.env.npm_package_version || '1.0.0',
        services: {
          supabase: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
          redis: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
          appointmentMonitor: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
          jobPublisher: { status: 'unhealthy', lastCheck: new Date().toISOString(), error: 'Health check failed' },
        },
        queues: {
          appointmentRequests: 0,
          retryQueue: 0,
          deadLetterQueue: 0,
        },
        stats: {
          totalNotifications: 0,
          processedAppointments: 0,
          failedProcessing: 0,
          duplicateJobs: 0,
          rateLimitedJobs: 0,
        },
      };
    }
  }

  private async checkSupabaseHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      const isHealthy = await supabaseService.healthCheck();
      const responseTime = Date.now() - startTime;

      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        lastCheck: new Date().toISOString(),
        responseTime,
        error: isHealthy ? undefined : 'Supabase connection failed',
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'unhealthy',
        lastCheck: new Date().toISOString(),
        responseTime,
        error: (error as Error).message,
      };
    }
  }

  private async checkRedisHealth(): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      const isHealthy = await redisService.healthCheck();
      const responseTime = Date.now() - startTime;

      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        lastCheck: new Date().toISOString(),
        responseTime,
        error: isHealthy ? undefined : 'Redis connection failed',
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'unhealthy',
        lastCheck: new Date().toISOString(),
        responseTime,
        error: (error as Error).message,
      };
    }
  }

  private async checkAppointmentMonitorHealth(): Promise<ServiceHealth> {
    try {
      const healthCheck = await appointmentMonitor.healthCheck();

      return {
        status: healthCheck.isRunning && healthCheck.databaseHealthy ? 'healthy' : 'unhealthy',
        lastCheck: new Date().toISOString(),
        error: healthCheck.isRunning && healthCheck.databaseHealthy ? undefined : 'Appointment monitor not running or database unhealthy',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        lastCheck: new Date().toISOString(),
        error: (error as Error).message,
      };
    }
  }

  private async getQueueStats(): Promise<{
    appointmentRequests: number;
    retryQueue: number;
    deadLetterQueue: number;
  }> {
    try {
      return await jobPublisher.getQueueStats();
    } catch (error) {
      logger.error('Failed to get queue stats for health check', error as Error);
      return {
        appointmentRequests: 0,
        retryQueue: 0,
        deadLetterQueue: 0,
      };
    }
  }

  private getMonitorStats(): {
    totalNotifications: number;
    processedAppointments: number;
    failedProcessing: number;
    duplicateJobs: number;
    rateLimitedJobs: number;
  } {
    try {
      const stats = appointmentMonitor.getStats();
      return {
        totalNotifications: stats.totalNotifications,
        processedAppointments: stats.processedAppointments,
        failedProcessing: stats.failedProcessing,
        duplicateJobs: stats.duplicateJobs,
        rateLimitedJobs: stats.rateLimitedJobs,
      };
    } catch (error) {
      logger.error('Failed to get monitor stats for health check', error as Error);
      return {
        totalNotifications: 0,
        processedAppointments: 0,
        failedProcessing: 0,
        duplicateJobs: 0,
        rateLimitedJobs: 0,
      };
    }
  }

  // Quick health check for readiness probes
  async isReady(): Promise<boolean> {
    try {
      const [supabaseHealthy, redisHealthy, monitorHealthy] = await Promise.all([
        supabaseService.healthCheck(),
        redisService.healthCheck(),
        appointmentMonitor.isHealthy,
      ]);

      return supabaseHealthy && redisHealthy && monitorHealthy;
    } catch (error) {
      logger.error('Readiness check failed', error as Error);
      return false;
    }
  }

  // Quick health check for liveness probes
  async isAlive(): Promise<boolean> {
    try {
      // Basic check to ensure the service is responding
      return true;
    } catch (error) {
      logger.error('Liveness check failed', error as Error);
      return false;
    }
  }
}

// Export singleton instance
export const healthCheckService = new HealthCheckService();
export default healthCheckService; 