import { createClient, RedisClientType } from 'redis';
import appConfig from '../utils/config';
import { StructuredLogger } from '../utils/logger';

const logger = new StructuredLogger({ component: 'redis-client' });

export interface RedisConfig {
  url: string;
  password?: string;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
}

class RedisService {
  private client: RedisClientType;
  private isConnected: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 10;

  constructor() {
    const redisConfig: RedisConfig = {
      url: appConfig.redis.url,
      password: appConfig.redis.password,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    };

    this.client = createClient({
      url: redisConfig.url,
      password: redisConfig.password,
    });

    // Handle connection events
    this.client.on('connect', () => {
      logger.info('Redis client connecting...');
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      this.reconnectAttempts = 0;
      logger.info('Redis client connected and ready');
    });

    this.client.on('error', (error) => {
      logger.error('Redis client error', error);
      this.isConnected = false;
    });

    this.client.on('end', () => {
      this.isConnected = false;
      logger.info('Redis client connection ended');
    });

    this.client.on('reconnecting', () => {
      this.reconnectAttempts++;
      logger.info('Redis client reconnecting...', {
        attempt: this.reconnectAttempts,
      });
    });

    logger.info('Redis client initialized', {
      url: appConfig.redis.url.replace(/:[^:]*@/, ':***@'), // Hide password in logs
    });
  }

  async connect(): Promise<void> {
    try {
      if (this.isConnected) {
        logger.warn('Redis client already connected');
        return;
      }

      await this.client.connect();
      logger.info('Successfully connected to Redis');
    } catch (error) {
      this.isConnected = false;
      logger.error('Failed to connect to Redis', error as Error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (!this.isConnected) {
        return;
      }

      await this.client.disconnect();
      this.isConnected = false;
      logger.info('Disconnected from Redis');
    } catch (error) {
      logger.error('Error during Redis disconnection', error as Error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected) {
        return false;
      }

      const result = await this.client.ping();
      const healthy = result === 'PONG';

      if (!healthy) {
        logger.warn('Redis health check failed - unexpected ping response', {
          response: result,
        });
      }

      return healthy;
    } catch (error) {
      logger.error('Redis health check error', error as Error);
      return false;
    }
  }

  // Queue operations
  async pushToQueue(queueName: string, jobData: any, isRetry: boolean = false): Promise<string> {
    try {
      const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const job = {
        id: jobId,
        data: jobData,
        isRetry,
        createdAt: new Date().toISOString(),
        attempts: 0,
        maxAttempts: 3,
      };

      // FIFO scoring: Use timestamp for first-come-first-serve
      // Exception: Retry jobs get a lower score (higher priority) to skip the line
      let score: number;
      if (isRetry) {
        // Retry jobs get priority - use negative timestamp to put them at the front
        score = -Date.now();
      } else {
        // Regular jobs use positive timestamp for FIFO ordering
        score = Date.now();
      }

      await this.client.zAdd(queueName, {
        score,
        value: JSON.stringify(job),
      });

      logger.debug('Job added to queue', {
        queueName,
        jobId,
        isRetry,
        score,
      });

      return jobId;
    } catch (error) {
      logger.error('Failed to push job to queue', error as Error, {
        queueName,
        isRetry,
      });
      throw error;
    }
  }

  async popFromQueue(queueName: string): Promise<any | null> {
    try {
      // Get job with lowest score (retry jobs have negative scores, so they come first)
      // Regular jobs have positive timestamps, so they follow FIFO order
      const result = await this.client.zPopMin(queueName);
      
      if (!result) {
        return null;
      }

      const job = JSON.parse(result.value);
      
      logger.debug('Job popped from queue', {
        queueName,
        jobId: job.id,
        isRetry: job.isRetry,
        score: result.score,
      });

      return job;
    } catch (error) {
      logger.error('Failed to pop job from queue', error as Error, {
        queueName,
      });
      throw error;
    }
  }

  async getQueueLength(queueName: string): Promise<number> {
    try {
      const length = await this.client.zCard(queueName);
      return length;
    } catch (error) {
      logger.error('Failed to get queue length', error as Error, {
        queueName,
      });
      throw error;
    }
  }

  async removeJobFromQueue(queueName: string, jobData: string): Promise<boolean> {
    try {
      const removed = await this.client.zRem(queueName, jobData);
      return removed > 0;
    } catch (error) {
      logger.error('Failed to remove job from queue', error as Error, {
        queueName,
      });
      throw error;
    }
  }

  // Job deduplication
  async isJobDuplicate(deduplicationKey: string, ttlSeconds: number = 300): Promise<boolean> {
    try {
      const exists = await this.client.exists(deduplicationKey);
      return exists === 1;
    } catch (error) {
      logger.error('Failed to check job duplication', error as Error, {
        deduplicationKey,
      });
      throw error;
    }
  }

  async markJobAsProcessed(deduplicationKey: string, ttlSeconds: number = 300): Promise<void> {
    try {
      await this.client.setEx(deduplicationKey, ttlSeconds, 'processed');
      logger.debug('Job marked as processed', {
        deduplicationKey,
        ttlSeconds,
      });
    } catch (error) {
      logger.error('Failed to mark job as processed', error as Error, {
        deduplicationKey,
      });
      throw error;
    }
  }

  // Metrics and monitoring
  async incrementCounter(key: string, increment: number = 1): Promise<number> {
    try {
      const result = await this.client.incrBy(key, increment);
      return result;
    } catch (error) {
      logger.error('Failed to increment counter', error as Error, { key });
      throw error;
    }
  }

  async setMetric(key: string, value: any, ttlSeconds?: number): Promise<void> {
    try {
      if (ttlSeconds) {
        await this.client.setEx(key, ttlSeconds, JSON.stringify(value));
      } else {
        await this.client.set(key, JSON.stringify(value));
      }
    } catch (error) {
      logger.error('Failed to set metric', error as Error, { key });
      throw error;
    }
  }

  async getMetric(key: string): Promise<any> {
    try {
      const value = await this.client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Failed to get metric', error as Error, { key });
      throw error;
    }
  }

  get isHealthy(): boolean {
    return this.isConnected;
  }

  get redisClient(): RedisClientType {
    return this.client;
  }
}

// Export singleton instance
export const redisService = new RedisService();
export default redisService; 