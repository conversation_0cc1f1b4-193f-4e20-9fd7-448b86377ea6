import { v4 as uuidv4 } from 'uuid';
import redisService from './redis-client';
import { AppointmentRequest } from '../database/supabase-client';
import { StructuredLogger } from '../utils/logger';

const logger = new StructuredLogger({ component: 'job-publisher' });

export interface JobData {
  appointmentRequestId: string;
  patientInfo: {
    firstName: string;
    lastName: string;
    healthCard: string;
    healthCardSequence: string;
    dateOfBirth: string;
    postalCode: string;
  };
  searchCriteria: {
    appointmentDate?: string;
    timePreference: 'asap' | 'morning' | 'afternoon' | 'evening';
    searchRadius: number;
  };
  notificationPreferences: {
    email: string;
    language: 'fr' | 'en';
  };
  userId: string;
  familyMemberId?: string;
  correlationId: string;
}

export interface QueuedJob {
  id: string;
  type: 'appointment-request';
  isRetry: boolean;
  data: JobData;
  attempts: number;
  maxAttempts: number;
  createdAt: string;
  scheduledFor?: string;
}

// Queue names
export const QUEUE_NAMES = {
  APPOINTMENT_REQUESTS: 'appointment-requests',
  RETRY_QUEUE: 'retry-queue',
  DEAD_LETTER_QUEUE: 'dead-letter-queue'
} as const;

class JobPublisher {
  private rateLimiters: Map<string, { count: number; resetTime: number }> = new Map();

  constructor() {
    logger.info('Job publisher initialized');
  }

  async publishAppointmentRequest(
    appointmentRequest: AppointmentRequest,
    correlationId?: string
  ): Promise<string> {
    const requestCorrelationId = correlationId || uuidv4();
    const requestLogger = logger.withCorrelationId(requestCorrelationId);

    try {
      // Check rate limiting
      if (await this.isRateLimited(appointmentRequest.user_id)) {
        throw new Error(`Rate limit exceeded for user ${appointmentRequest.user_id}`);
      }

      // Check for duplicate jobs
      const deduplicationKey = this.generateDeduplicationKey(appointmentRequest);
      if (await redisService.isJobDuplicate(deduplicationKey)) {
        requestLogger.warn('Duplicate job detected, skipping', {
          appointmentId: appointmentRequest.id,
          deduplicationKey,
        });
        return 'duplicate';
      }

      // Create job data
      const jobData: JobData = {
        appointmentRequestId: appointmentRequest.id,
        patientInfo: {
          firstName: appointmentRequest.patient_first_name,
          lastName: appointmentRequest.patient_last_name,
          healthCard: appointmentRequest.patient_health_card,
          healthCardSequence: appointmentRequest.patient_health_card_sequence,
          dateOfBirth: appointmentRequest.patient_date_of_birth,
          postalCode: appointmentRequest.patient_postal_code,
        },
        searchCriteria: {
          appointmentDate: appointmentRequest.appointment_date,
          timePreference: appointmentRequest.appointment_time_preference,
          searchRadius: parseInt(appointmentRequest.search_radius || '50', 10), // Convert to number, default to 50km
        },
        notificationPreferences: {
          email: appointmentRequest.notification_email || '', // Will need to get from user table
          language: appointmentRequest.language_preference || 'en', // Default to english
        },
        userId: appointmentRequest.user_id,
        familyMemberId: appointmentRequest.patient_id,
        correlationId: requestCorrelationId,
      };

      // Publish job to queue
      const jobId = await redisService.pushToQueue(
        QUEUE_NAMES.APPOINTMENT_REQUESTS,
        jobData,
        false // Not a retry job
      );

      // Mark as processed in deduplication cache
      await redisService.markJobAsProcessed(deduplicationKey, 300); // 5 minutes TTL

      // Update rate limiter
      await this.updateRateLimit(appointmentRequest.user_id);

      // Update metrics
      await this.updateMetrics('appointment_request_published', jobId);

      requestLogger.info('Appointment request job published successfully', {
        appointmentId: appointmentRequest.id,
        jobId,
        queueName: QUEUE_NAMES.APPOINTMENT_REQUESTS,
      });

      return jobId;
    } catch (error) {
      requestLogger.error('Failed to publish appointment request job', error as Error, {
        appointmentId: appointmentRequest.id,
      });
      throw error;
    }
  }

  async publishRetryJob(
    originalJob: QueuedJob,
    retryReason: string,
    correlationId?: string
  ): Promise<string> {
    const requestCorrelationId = correlationId || uuidv4();
    const requestLogger = logger.withCorrelationId(requestCorrelationId);

    try {
      if (originalJob.attempts >= originalJob.maxAttempts) {
        // Move to dead letter queue
        return await this.publishToDeadLetterQueue(originalJob, retryReason, requestCorrelationId);
      }

      const retryJob: QueuedJob = {
        ...originalJob,
        id: uuidv4(),
        isRetry: true,
        attempts: originalJob.attempts + 1,
        scheduledFor: this.calculateRetryDelay(originalJob.attempts),
      };

      const jobId = await redisService.pushToQueue(
        QUEUE_NAMES.RETRY_QUEUE,
        retryJob.data,
        true // This is a retry job
      );

      await this.updateMetrics('job_retried', jobId);

      requestLogger.info('Job scheduled for retry', {
        originalJobId: originalJob.id,
        retryJobId: jobId,
        attempt: retryJob.attempts,
        maxAttempts: retryJob.maxAttempts,
        retryReason,
        scheduledFor: retryJob.scheduledFor,
      });

      return jobId;
    } catch (error) {
      requestLogger.error('Failed to publish retry job', error as Error, {
        originalJobId: originalJob.id,
        retryReason,
      });
      throw error;
    }
  }

  private async publishToDeadLetterQueue(
    job: QueuedJob,
    reason: string,
    correlationId: string
  ): Promise<string> {
    const requestLogger = logger.withCorrelationId(correlationId);

    try {
      const deadJob = {
        ...job,
        id: uuidv4(),
        failedAt: new Date().toISOString(),
        failureReason: reason,
      };

      const jobId = await redisService.pushToQueue(
        QUEUE_NAMES.DEAD_LETTER_QUEUE,
        deadJob,
        false // Dead letter jobs don't skip the line
      );

      await this.updateMetrics('job_dead_lettered', jobId);

      requestLogger.error('Job moved to dead letter queue', new Error(reason), {
        originalJobId: job.id,
        deadJobId: jobId,
        attempts: job.attempts,
        reason,
      });

      return jobId;
    } catch (error) {
      requestLogger.error('Failed to publish to dead letter queue', error as Error, {
        originalJobId: job.id,
        reason,
      });
      throw error;
    }
  }

  private calculateRetryDelay(attempts: number): string {
    // Exponential backoff: 2^attempts minutes, max 60 minutes
    const delayMinutes = Math.min(Math.pow(2, attempts), 60);
    const retryTime = new Date(Date.now() + delayMinutes * 60 * 1000);
    return retryTime.toISOString();
  }

  private async isRateLimited(userId: string): Promise<boolean> {
    const now = Date.now();
    const windowMs = 60000; // 1 minute window
    const maxRequests = 10; // Max 10 requests per minute per user

    const userLimit = this.rateLimiters.get(userId);
    
    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize rate limiter
      this.rateLimiters.set(userId, {
        count: 0,
        resetTime: now + windowMs,
      });
      return false;
    }

    return userLimit.count >= maxRequests;
  }

  private async updateRateLimit(userId: string): Promise<void> {
    const userLimit = this.rateLimiters.get(userId);
    if (userLimit) {
      userLimit.count += 1;
    }
  }

  private async updateMetrics(
    eventType: string,
    jobId: string
  ): Promise<void> {
    try {
      const metricsKey = `metrics:${eventType}`;
      const dailyKey = `metrics:daily:${eventType}:${new Date().toISOString().split('T')[0]}`;

      await Promise.all([
        redisService.incrementCounter(metricsKey),
        redisService.incrementCounter(dailyKey),
        redisService.setMetric(`job:${jobId}:metrics`, {
          eventType,
          timestamp: new Date().toISOString(),
        }, 3600), // 1 hour TTL
      ]);
    } catch (error) {
      logger.error('Failed to update metrics', error as Error, {
        eventType,
        jobId,
      });
      // Don't throw - metrics shouldn't break the main flow
    }
  }

  async getQueueStats(): Promise<{
    appointmentRequests: number;
    retryQueue: number;
    deadLetterQueue: number;
  }> {
    try {
      const [appointmentRequests, retryQueue, deadLetterQueue] = await Promise.all([
        redisService.getQueueLength(QUEUE_NAMES.APPOINTMENT_REQUESTS),
        redisService.getQueueLength(QUEUE_NAMES.RETRY_QUEUE),
        redisService.getQueueLength(QUEUE_NAMES.DEAD_LETTER_QUEUE),
      ]);

      return {
        appointmentRequests,
        retryQueue,
        deadLetterQueue,
      };
    } catch (error) {
      logger.error('Failed to get queue stats', error as Error);
      throw error;
    }
  }

  private generateDeduplicationKey(appointmentRequest: AppointmentRequest): string {
    // Create a unique key based on user and request details to prevent duplicates
    const keyParts = [
      appointmentRequest.user_id,
      appointmentRequest.patient_first_name,
      appointmentRequest.patient_last_name,
      appointmentRequest.patient_health_card,
      appointmentRequest.patient_date_of_birth,
      appointmentRequest.appointment_time_preference,
    ];

    const baseKey = keyParts.join(':').toLowerCase().replace(/\s+/g, '');
    return `dedup:appointment:${Buffer.from(baseKey).toString('base64')}`;
  }
}

// Export singleton instance
export const jobPublisher = new JobPublisher();
export default jobPublisher; 