# Environment Configuration
NODE_ENV=development
PORT=3002

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/worker-manager.log

# Worker Manager Configuration
HEALTH_CHECK_INTERVAL=30000
NOTIFICATION_CHANNEL=new_appointment_requests
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=3003

# Graceful Shutdown
SHUTDOWN_TIMEOUT=10000 