// Test script for appointment request API

async function testAppointmentRequest() {
  try {
    console.log('Testing appointment request API...');
    
    const response = await fetch('http://localhost:3000/api/appointment-requests', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Note: This will fail without proper authentication, but we can see the API response
      },
      body: JSON.stringify({
        request_type: 'general_appointment',
        patient_id: null,
        request_details: {
          postalCode: 'K1A 0L8',
          date: '2025-01-30',
          time: 'Dès que possible',
          healthCardLastDigits: '',
          cardSequenceNumber: '',
          patientName: 'Test Patient'
        }
      })
    });

    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response data:', result);
    
    if (response.status === 401) {
      console.log('✓ API is working - authentication required as expected');
    } else if (response.status === 403) {
      console.log('✓ API is working - subscription required as expected');
    } else if (response.status === 200) {
      console.log('✓ API request successful!');
    } else {
      console.log('⚠ Unexpected response status');
    }
  } catch (error) {
    console.error('Error testing API:', error);
  }
}

testAppointmentRequest(); 