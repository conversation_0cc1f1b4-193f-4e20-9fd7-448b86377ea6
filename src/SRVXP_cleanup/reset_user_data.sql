-- =====================================================
-- SRVXP Database Reset Script
-- =====================================================
-- This script will delete all user data and related information
-- to provide a fresh start for testing and development.
-- 
-- WARNING: This will permanently delete all user data!
-- Only run this in development/testing environments.
-- =====================================================

-- Start transaction to ensure atomicity
BEGIN;

-- Disable foreign key checks temporarily (PostgreSQL doesn't have this, but we'll handle order carefully)
-- We'll delete in reverse dependency order to avoid foreign key violations

-- =====================================================
-- 1. DELETE PUBLIC SCHEMA USER-RELATED DATA
-- =====================================================

-- Delete webhook events (no dependencies)
DELETE FROM public.webhook_events;

-- Delete completed appointments (depends on appointment_requests)
DELETE FROM public.completed_appointments;

-- Delete cancelled appointments (depends on appointment_requests)
DELETE FROM public.cancelled_appointments;

-- Delete appointment requests (depends on users and family_members)
DELETE FROM public.appointment_requests;

-- Delete family members (depends on auth.users)
DELETE FROM public.family_members;

-- Delete subscriptions (depends on public.users)
DELETE FROM public.subscriptions;

-- Delete public users table (depends on auth.users via user_id)
DELETE FROM public.users;

-- =====================================================
-- 2. DELETE AUTH SCHEMA USER-RELATED DATA
-- =====================================================

-- Delete MFA challenges (depends on mfa_factors)
DELETE FROM auth.mfa_challenges;

-- Delete MFA factors (depends on users)
DELETE FROM auth.mfa_factors;

-- Delete MFA AMR claims (depends on sessions)
DELETE FROM auth.mfa_amr_claims;

-- Delete one-time tokens (depends on users)
DELETE FROM auth.one_time_tokens;

-- Delete refresh tokens (depends on sessions)
DELETE FROM auth.refresh_tokens;

-- Delete SAML relay states (depends on flow_state and sso_providers)
DELETE FROM auth.saml_relay_states;

-- Delete sessions (depends on users)
DELETE FROM auth.sessions;

-- Delete flow state (depends on users)
DELETE FROM auth.flow_state;

-- Delete identities (depends on users)
DELETE FROM auth.identities;

-- Delete SAML providers (depends on sso_providers)
DELETE FROM auth.saml_providers;

-- Delete SSO domains (depends on sso_providers)
DELETE FROM auth.sso_domains;

-- Delete SSO providers (no dependencies on users)
DELETE FROM auth.sso_providers;

-- Delete audit log entries (no user dependencies but good to clean)
DELETE FROM auth.audit_log_entries;

-- Finally, delete all users from auth.users
DELETE FROM auth.users;

-- =====================================================
-- 3. RESET SEQUENCES (if any auto-incrementing columns exist)
-- =====================================================

-- Note: Sequence reset skipped due to Supabase permission restrictions
-- The refresh_tokens_id_seq will continue from its current value
-- This doesn't affect the functionality of your fresh start

-- =====================================================
-- 4. VERIFY CLEANUP
-- =====================================================

-- Show counts of remaining records in user-related tables
DO $$
DECLARE
    user_count INTEGER;
    public_user_count INTEGER;
    subscription_count INTEGER;
    family_count INTEGER;
    appointment_count INTEGER;
    session_count INTEGER;
    identity_count INTEGER;
BEGIN
    -- Check auth.users
    SELECT COUNT(*) INTO user_count FROM auth.users;
    
    -- Check public.users
    SELECT COUNT(*) INTO public_user_count FROM public.users;
    
    -- Check subscriptions
    SELECT COUNT(*) INTO subscription_count FROM public.subscriptions;
    
    -- Check family members
    SELECT COUNT(*) INTO family_count FROM public.family_members;
    
    -- Check appointment requests
    SELECT COUNT(*) INTO appointment_count FROM public.appointment_requests;
    
    -- Check sessions
    SELECT COUNT(*) INTO session_count FROM auth.sessions;
    
    -- Check identities
    SELECT COUNT(*) INTO identity_count FROM auth.identities;
    
    -- Output results
    RAISE NOTICE 'Cleanup Results:';
    RAISE NOTICE 'Auth Users: %', user_count;
    RAISE NOTICE 'Public Users: %', public_user_count;
    RAISE NOTICE 'Subscriptions: %', subscription_count;
    RAISE NOTICE 'Family Members: %', family_count;
    RAISE NOTICE 'Appointment Requests: %', appointment_count;
    RAISE NOTICE 'Sessions: %', session_count;
    RAISE NOTICE 'Identities: %', identity_count;
    
    IF user_count = 0 AND public_user_count = 0 AND subscription_count = 0 
       AND family_count = 0 AND appointment_count = 0 AND session_count = 0 
       AND identity_count = 0 THEN
        RAISE NOTICE 'SUCCESS: All user data has been successfully removed!';
    ELSE
        RAISE NOTICE 'WARNING: Some data may still remain. Please check the counts above.';
    END IF;
END $$;

-- Commit the transaction
COMMIT;

-- =====================================================
-- USAGE INSTRUCTIONS:
-- =====================================================
-- 1. Save this file as reset_user_data.sql
-- 2. Connect to your Supabase database
-- 3. Run: psql -h your-db-host -U postgres -d postgres -f reset_user_data.sql
-- 4. Or execute this script in the Supabase SQL editor
-- 
-- IMPORTANT: This script is irreversible! Always backup your data first
-- if you need to preserve any information.
-- ===================================================== 